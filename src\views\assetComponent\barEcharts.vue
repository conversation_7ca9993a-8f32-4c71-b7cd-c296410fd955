<template>
  <div id="barEcharts" class="barBox" />
</template>

<script>
import * as echarts from 'echarts'
export default {
  props: {
    echartData: {
      type: Object,
      default: () => {}
    },
    jyxIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    echartData: {
      deep: true,
      handler(newVal) {
        this.$nextTick(() => {
          this.initializationEcharts()
        })
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    initializationEcharts() {
      const barEcharts = document.getElementById('barEcharts')
      this.myChart = echarts.init(barEcharts)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          textStyle: {
            fontSize: 14, // 字体大小
            color: '#fff' // 字体颜色
          },
          data: this.echartData.name
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.echartData.year,
            axisTick: {
              alignWithLabel: true,
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#66d6ff',
                fontSize: 14
              },
              rotate: 45
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#2e9eff',
                fontSize: 14
              },
              formatter: this.jyxIndex == 0 ? '{value} 万元' : '{value}'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#123d72'
              }
            }
          }
        ],
        series: [
          {
            name: this.echartData.name[0],
            type: 'bar',
            // barGap: 12,
            data: this.echartData.fjy,
            barWidth: 12,
            barGap: '30%',
            itemStyle: {
              normal: {
                // 这里设置柱形图圆角 [左上角，右上角，右下角，左下角]
                barBorderRadius: [12, 12, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#06dced'
                  },
                  {
                    offset: 1,
                    color: '#067ee6'
                  }
                ])
              }
            }
          },
          {
            name: this.echartData.name[1],
            type: 'bar',
            // barGap: 0,
            data: this.echartData.jy,
            barWidth: 12,
            barGap: '30%',
            itemStyle: {
              normal: {
                // 这里设置柱形图圆角 [左上角，右上角，右下角，左下角]
                barBorderRadius: [12, 12, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#05dd92'
                  },
                  {
                    offset: 1,
                    color: '#05c04d'
                  }
                ])
              }
            }
          },
          {
            name: this.echartData.name[2],
            type: 'bar',
            // barGap: 0,
            data: this.echartData.zc,
            barWidth: 12,
            barGap: '30%',
            itemStyle: {
              normal: {
                // 这里设置柱形图圆角 [左上角，右上角，右下角，左下角]
                barBorderRadius: [12, 12, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#e6d769'
                  },
                  {
                    offset: 1,
                    color: '#e1a80f'
                  }
                ])
              }
            }
          }
        ]
      }
      this.myChart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.barBox {
  margin-top: 20px;
  height: 350px;
}
</style>
