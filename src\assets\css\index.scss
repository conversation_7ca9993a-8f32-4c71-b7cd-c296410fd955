* {
  padding: 0;
  margin: 0;
}

ul,
li {
  list-style: none;
}

html,
body {
  box-sizing: border-box;
  overflow: unset;
  background-color: #07134f;
  // background: url('../../assets/image/bj.jpg') 0 0 no-repeat;
  background-size: cover;
}

#webpack-dev-server-client-overlay {
  display: none;
}

.radio-btn {
  display: inline-block;
  cursor: pointer;
  border-radius: 5px;
  color: #d1fcff;
  padding: 3px 8px;
  margin-left: 10px;
  background: linear-gradient(to bottom, #014e6a, #087997);
  &.selected {
    background: radial-gradient(circle, rgb(36, 28, 1), rgb(210, 167, 6));
  }
}

.radio-btn1 {
  display: inline-block;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 5px;
  color: #9fd7ff;
  border: 1px solid #8697e9;
  padding: 3px 8px;
  margin-right: 10px;
  background: linear-gradient(rgb(1, 78, 106), rgb(54, 91, 90));
  &.selected {
    color: #2fd0ca;
    border: 1px solid #2fd0ca;
    background: radial-gradient(to top, #0f605d, #2fd0ca);
  }
}
