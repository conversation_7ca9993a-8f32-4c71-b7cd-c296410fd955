/** scss 变量，混入，函数  **/
// 供所有样式文件适用



//主色
$mainColor: #4876e6;
$darkColor: #101d4a;
$lightColor: #26d4ff;




//颜色
$red: #f56c6c;
$green: #67c23a;
$orange: #e6a23c;


//文字（大小,颜色,粗细）
@mixin font($fontSize, $color: #333, $weight: normal) {
  font-size: $fontSize;
  color: $color;
  font-weight: $weight;
}


@mixin shadow {
  box-shadow: 0 3px 8px rgba(#000, 0.05);
}



//flex布局
@mixin flex-center {
  display: flex;
  align-items: center;
}

@mixin flex-stretch {
  display: flex;
  align-items: stretch;
}

@mixin flex-baseline {
  display: flex;
  align-items: baseline;
}


@mixin flex-baseline-center {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

@mixin flex-center-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-null-between {
  display: flex;
  justify-content: space-between;
}

@mixin flex-center-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-stretch-between {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
}


@mixin flex-center-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

@mixin flex-center-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@mixin flex-center-y {
  display: flex;
  align-items: center;
  flex-flow: column;
}


@mixin flex-null-center-y {
  display: flex;
  justify-content: center;
  flex-flow: column;
}

@mixin flex-center-center-y {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-flow: column;
}

@mixin flex-null-between-y {
  display: flex;
  justify-content: space-between;
  flex-flow: column;
}

@mixin flex-center-between-y {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-flow: column;
}

@mixin flex-center-around-y {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-flow: column;
}



//文字超出隐藏-单行
@mixin text-overflow-one {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

//文字超出隐藏-多行
@mixin text-overflow-row($number) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $number;
  overflow: hidden;
  white-space: normal;
}


//before,after
@mixin before ($width,$height){
  content: "";
  width: $width;
  height: $height;
  display: block;
}

@mixin after ($width,$height){
  content: "";
  width: $width;
  height: $height;
  display: block;
}


// 转圈圈动画 逆时针转
@mixin keyframes-circle-left($name) {

  @keyframes #{$name} {
      from {
          transform: rotate(360deg);
      }

      to {
          transform: rotate(0deg);
      }
  }
}

// 转圈圈动画 顺时针转
@mixin keyframes-circle-right($name) {

  @keyframes #{$name} {
      from {
          transform: rotate(0deg);
      }

      to {
          transform: rotate(360deg);
      }
  }
}


//动画混入例子 
.animateDom {
  animation: circle02 6s infinite linear ;
  @include keyframes-circle-right(circle02);
}
