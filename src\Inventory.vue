<template>
  <div class="asset-container">
    <div class="left" style="height: 693px;">
      <div class="top">
        <div class="top-left item-block">
          <panel title="工作进度分析" title-width="450px">
            <div class="row-warp">
              <div class="center">
                <div class="item">
                  <div class="icon icon4" />
                  <div class="right-content">
                    <div>
                      <div style="color: #fff; margin-bottom: 4px; font-size: 14px">资产总宗数</div>
                      <div class="value">
                        {{ Number(workProgress.data?.ZZC).toLocaleString() }}
                        <font style="font-size: 12px">宗</font>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="item">
                  <div class="icon icon1" />
                  <div class="right-content">
                    <div>
                      <div style="color: #fff; font-size: 14px; margin-bottom: 4px">资源性资产数</div>
                      <div class="value">
                        {{ Number(workProgress.data?.Z).toLocaleString() }}
                        <font style="font-size: 12px">宗</font>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="item">
                  <div class="icon icon2" />
                  <div class="right-content">
                    <div>
                      <div style="color: #fff; font-size: 14px; margin-bottom: 4px">经营性资产宗数</div>
                      <div class="value">
                        {{ Number(workProgress.data?.J).toLocaleString() }}
                        <font style="font-size: 12px">宗</font>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="item">
                  <div class="icon icon3" />
                  <div class="right-content">
                    <div>
                      <div style="color: #fff; font-size: 14px; margin-bottom: 4px">非经营性资产宗数</div>
                      <div class="value">
                        {{ Number(workProgress.data?.F).toLocaleString() }}
                        <font style="font-size: 12px">宗</font>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <gauge-chart class="left mt20" title="完成率" :rate="totalRate" />
              <div v-if="JSON.stringify(data) !== '{}'" class="right">
                <div class="progress-item">
                  <div class="item-name">发起公示</div>
                  <div v-if="barShow" class="battery-progress">
                    <el-progress class="name-progress" :text-inside="true" :stroke-width="19" :percentage="Number(((workProgress.data?.publicRate || 0) * 100).toFixed(2))" />
                  </div>
                  <div class="value">{{ transNumberToShort(workProgress.data?.publicNum) }}宗</div>
                </div>
                <div class="progress-item">
                  <div class="item-name">资产描边</div>
                  <div v-if="barShow" class="battery-progress">
                    <el-progress class="name-progress" :text-inside="true" :stroke-width="19" :percentage="Number(((workProgress.data?.geoRate || 0) * 100).toFixed(2))" />
                  </div>
                  <div class="value">{{ transNumberToShort(workProgress.data?.geoNum) }}宗</div>
                </div>
                <div class="progress-item">
                  <div class="item-name">资产拍照</div>
                  <div v-if="barShow" class="battery-progress">
                    <el-progress class="name-progress" :text-inside="true" :stroke-width="19" :percentage="Number(((workProgress.data?.takePicRate || 0) * 100).toFixed(2))" />
                  </div>
                  <div class="value">{{ transNumberToShort(workProgress.data?.takePicNum) }}宗</div>
                </div>
                <div class="progress-item">
                  <div class="item-name">发起清查</div>
                  <div v-if="barShow" class="battery-progress">
                    <el-progress class="name-progress" :text-inside="true" :stroke-width="19" :percentage="Number(((workProgress.data?.taskRate || 0) * 100).toFixed(2))" />
                  </div>
                  <div class="value">{{ transNumberToShort(workProgress.data?.taskNum) }}宗</div>
                </div>
              </div>
            </div>
          </panel>
        </div>
        <div class="top-center item-block">
          <div class="area-info">
            <div class="area-org">
              <img class="area-img" src="./assets/image/icon1.png" alt="" />
              <span class="area-title" style="font-size: 18px">当前地区：</span>
              <span class="area" style="font-size: 24px">| {{ currentArea }} |</span>
            </div>
            <div class="area-time">
              <img class="area-img" src="./assets/image/date.png" alt="" />
              <span class="area-title" style="font-size: 18px">统计截止时间：</span>
              <span class="area" style="font-size: 18px">{{ currentDate }}</span>
            </div>
          </div>
          <div style="height: 47px"></div>
          <mapNav :key="currentId" class="map" :area-code="areaCode" @gotopath="gotopath" />
        </div>
      </div>
    </div>
    <div class="right" style="height: 693px;">
      <div class="top-right item-block">
        <panel title="各单位发起清查机构的情况" title-width="450px">
          <div id="rank-list-box">
            <vue-seamless-scroll :data="unitWork" class="rank-list" :class-option="scrollConfig" :style="{ height: rankBoxHeight + 'px', maxHeight: rankBoxHeight + 'px' }">
              <ul v-if="JSON.stringify(data) !== '{}'" class="box-content">
                <li v-for="(item, key) in unitWork" :key="key" style="cursor: pointer" @click="handleMap(item)">
                  <div class="sort"></div>
                  <div class="area-box">
                    <span class="area" :title="item.areaName" style="font-size: 18px">{{ item.areaName }}</span>
                  </div>
                  <div class="battery-progress">
                    <el-progress class="name-progress" :text-inside="true" :stroke-width="19" :percentage="Number(((item.rate || 0) * 100).toFixed(2))" />
                  </div>
                  <div class="battery-value">{{ transNumberToShort(item.inventoryOrgNum) }}个</div>
                </li>
              </ul>
            </vue-seamless-scroll>
          </div>
        </panel>
      </div>
      <div class="bottom-info">
        <div class="left">
          <panel title="闲置资产" title-width="200px">
            <div class="left-warp">
              <div class="left">
                <div class="image" />
                <div class="data">
                  <div class="value">
                    {{ (workDynamic.freeAssetNum || 0) | formatNumber }}
                    <span style="font-size: 14px">宗</span>
                  </div>
                </div>
              </div>
            </div>
          </panel>
        </div>
        <div class="right">
          <panel title="临期资产" title-width="400px">
            <pie-chart :data="contractData" />
          </panel>
        </div>
      </div>
<!--      <div class="bottom-right item-block">-->
<!--        <panel title="资产交易情况" title-width="450px">-->
<!--          <div class="asset-trading" @mouseenter.stop="handleMouseEnter" @mouseleave.stop="handleMouseLeave">-->
<!--            <pager ref="_tradingRef" :total-page="dateType == 'month' ? 1 : 3" @prevPage="getAssetTrendData()" @nextPage="getAssetTrendData()" />-->
<!--          </div>-->
<!--          <div slot="area" class="div-solt">-->
<!--            <div class="radio-btn" :class="{ selected: dateType === 'month' }" @click="handleDateSearch('month')">月</div>-->
<!--            <div class="radio-btn" :class="{ selected: dateType === 'day' }" @click="handleDateSearch('day')">日</div>-->
<!--          </div>-->
<!--          <div class="tranding">-->
<!--            <div v-for="(item, index) of assetTrading" :key="index" :class="tradingIndex == index ? 'selected' : ''" class="radio-btn1" @click="onChangeTrading(index)">-->
<!--              {{ item.label }}-->
<!--            </div>-->
<!--          </div>-->
<!--          <div class="line-box" @mouseenter.stop="handleMouseEnter" @mouseleave.stop="handleMouseLeave">-->
<!--            <lineChart v-if="tradingData.length > 0" :chart-data="tradingData" :font-size="11" :is-show-money-unit="isShowMoneyUnit" />-->
<!--          </div>-->
<!--        </panel>-->
<!--      </div>-->
    </div>
    <div class="bottom fullBottom" style="height: 237px;margin: 20px 0 0 20px;">
      <div class="bottom-left item-block">
        <panel title="每周工作动态" title-width="450px">
          <pager ref="_weekRef" :total-page="totalPage" @prevPage="getWorkTrendData()" @nextPage="getWorkTrendData()" />
          <div class="box-content" style="width: 100%; height: 100%">
            <bar-line-chart v-bind="workTrend" />
          </div>
        </panel>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import mapNav from './views/mapNav.vue'
import GaugeChart from '@/views/GaugeChart'
import BarLineChart from '@/views/BarLineChart'
import lineChart from '@/views/lineChart.vue'
import PieChart from '@/views/PieChart.vue'
import panel from '@/views/Panel.vue'
import pager from '@/views/pager.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'

import { getData, getProgress, dynamic, getProgressTop, getJindu } from '@/api/common.js'
export default {
  components: {
    mapNav,
    GaugeChart,
    BarLineChart,
    PieChart,
    panel,
    pager,
    lineChart,
    vueSeamlessScroll
  },
  filters: {
    formatNumber(val) { // add by ymk 添加千分位格式化
      return (val && val.toLocaleString) ? Number(val).toLocaleString() : val
    }
  },
  data() {
    return {
      currentId: Date.now(),
      currentArea: '佛山市',
      currentDate: '',
      currentAccount: 'fs',
      headerTitle: '佛山市农村集体资产清产核资工作',
      areaCode: 'D4406',
      barShow: true,
      allData: require('./views/data.json'),
      data: {},
      totalRate: 0,
      rankBoxHeight: 0,
      workTrend: {
        xAxisData: [],
        series: [],
        barMin: 0,
        barMax: 8,
        color: ['#fed130', '#13c2c2', '#368aff', '#fc5659', '#1fb5fc'],
        lineMin: 0,
        xAxisLabelRotate: 60
      },
      currentPage: 1,
      totalPage: 2,
      pageSize: 5,
      page_zcqr: [],
      page_zcmb: [],
      page_zcfh: [],
      page_zcqc: [],
      xAxisData: [],

      contractData: [],
      unitWork: [],
      percentage: 0,
      // 工作进度分析
      workProgress: {},
      // 每周工作动态下半部分
      workDynamic: {},
      // 各单位code
      jinduCode: '',
      // 各单位进度值
      jinduValue: {},
      tradingIndex: 0,
      tradingData: [],
      assetTrading: [
        {
          label: '交易公告发布宗数',
          value: 'transAnnounceNum'
        },
        {
          label: '交易成交宗数',
          value: 'successPrjNum'
        },
        {
          label: '成功交易比例',
          value: 'successRate'
        },
        {
          label: '成交金额',
          value: 'transAmount'
        },
        {
          label: '平均溢价率',
          value: 'avePreRate'
        }
      ],
      records: require('./views/mapData/foshan_area_data.json'),
      assetTradingDayData: [],
      dateType: 'day',
      playInterval: 5000,
      timeSetInterval: null,
      isShowMoneyUnit: false
    }
  },
  computed: {
    negativeRate() {
      return this.rate < 0
    }, // 是否为负数比率
    getCurrentPage() {
      return this.currentPage
    },
    scrollConfig() {
      return {
        step: 0.5,
        limitMoveNum: 7
      }
    }
  },
  watch: {
    currentAccount: {
      handler: function (val) {
        localStorage.setItem('currentAccount', val)
      },
      immediate: true
    },
    tradingIndex: {
      handler: function (val) {
        if (this.$refs['_tradingRef']?.currentPage) {
          this.$refs['_tradingRef'].currentPage = this.dateType == 'month' ? 1 : 3
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getdate1()
    this.timestampToTime()
    this.gotopath('4406')
    this.getDomHeight()
  },
  beforeDestroy() {
    clearInterval(this.timeSetInterval) // 清除定时器
    this.timeSetInterval = null
  },
  methods: {
    getDomHeight() {
      const dom = document.getElementById('rank-list-box')
      this.rankBoxHeight = dom.clientHeight - 86
    },
    handleMouseEnter() {
      this.pauseTimer()
    },
    handleMouseLeave() {
      this.startTimer()
    },
    pauseTimer() {
      if (this.timeSetInterval) {
        clearInterval(this.timeSetInterval)
        this.timeSetInterval = null
      }
    },
    startTimer() {
      if (this.playInterval <= 0 || this.timeSetInterval) return
      this.timeSetInterval = setInterval(this.playSlides, this.playInterval)
    },
    resetTimer() {
      this.pauseTimer()
      this.startTimer()
    },
    playSlides() {
      this.tradingIndex++
      if (this.tradingIndex > 4) {
        this.tradingIndex = 0
      }
      this.onChangeTrading(this.tradingIndex)
    },
    // 获取时间
    async getdate1() {
      const res = await getData()
      this.currentDate = res.data
    },
    goMapCloud() {
      const url = `${window.homeData.mapCloudUrl}?username=${this.currentAccount}_ys&token=${this.currentAccount}`
      window.open(url, '_blank')
    },
    handleMap(item) {
      const code = item.areaCode.substring(1, item.areaCode.length)
      this.gotopath(code)
    },
    // 点击地区时切换数据
    // eslint-disable-next-line complexity
    async gotopath(type) {
      this.currentArea = this.records.records.find((item) => {
        return item.areaCode == `D${type}`
      }).areaName
      this.areaCode = this.records.records.find((item) => {
        return item.areaCode == `D${type}`
      }).areaCode
      if (type != undefined) {
        this.areaCode = type
        this.areaCode = 'D' + this.areaCode
      }
      console.log(this.areaCode)
      if (this.areaCode == 'D440605') {
        this.headerTitle = '南海区农村集体资产清产核资工作'
        this.currentAccount = 'nh'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '三水区农村集体资产清产核资工作'
        this.currentAccount = 'ss'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '禅城区农村集体资产清产核资工作'
        this.currentAccount = 'cc'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '顺德区农村集体资产清产核资工作'
        this.currentAccount = 'sd'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '高明区农村集体资产清产核资工作'
        this.currentAccount = 'gm'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '佛山市农村集体资产清产核资工作'
        this.currentAccount = 'fs'
      }

      // 请求工作进度
      const date = new Date()
      const year = date.getFullYear() - 1

      this.data = this.allData[this.areaCode]
      // 判断data是否为空
      if (this.data == undefined) {
        this.data = {
          asset: {
            zyx: 9934,
            jyx: 1574,
            fjyx: 2732
          },
          zcqc: {
            asset: 0,
            rate: [
              {
                date: '2023年2月第1周',
                value: 0
              },
              {
                date: '2023年2月第2周',
                value: 0
              },
              {
                date: '2023年2月第3周',
                value: 0
              },
              {
                date: '2023年2月第4周',
                value: 0
              },
              {
                date: '2023年2月第5周',
                value: 0
              },
              {
                date: '2023年3月第1周',
                value: 0
              },
              {
                date: '2023年3月第2周',
                value: 0
              },
              {
                date: '2023年3月第3周',
                value: 0
              },
              {
                date: '2023年3月第4周',
                value: 0
              }
            ]
          },
          zcmb: {
            asset: 0,
            rate: [
              {
                date: '2023年2月第1周',
                value: 0
              },
              {
                date: '2023年2月第2周',
                value: 0
              },
              {
                date: '2023年2月第3周',
                value: 0
              },
              {
                date: '2023年2月第4周',
                value: 0
              },
              {
                date: '2023年2月第5周',
                value: 0
              },
              {
                date: '2023年3月第1周',
                value: 0
              },
              {
                date: '2023年3月第2周',
                value: 0
              },
              {
                date: '2023年3月第3周',
                value: 0
              },
              {
                date: '2023年3月第4周',
                value: 0
              }
            ]
          },
          zcfh: {
            asset: 0,
            rate: [
              {
                date: '2023年2月第1周',
                value: 0
              },
              {
                date: '2023年2月第2周',
                value: 0
              },
              {
                date: '2023年2月第3周',
                value: 0
              },
              {
                date: '2023年2月第4周',
                value: 0
              },
              {
                date: '2023年2月第5周',
                value: 0
              },
              {
                date: '2023年3月第1周',
                value: 0
              },
              {
                date: '2023年3月第2周',
                value: 0
              },
              {
                date: '2023年3月第3周',
                value: 0
              },
              {
                date: '2023年3月第4周',
                value: 0
              }
            ]
          },
          zcqr: {
            asset: 0,
            rate: [
              {
                date: '2023年2月第1周',
                value: 0
              },
              {
                date: '2023年2月第2周',
                value: 0
              },
              {
                date: '2023年2月第3周',
                value: 0
              },
              {
                date: '2023年2月第4周',
                value: 0
              },
              {
                date: '2023年2月第5周',
                value: 0
              },
              {
                date: '2023年3月第1周',
                value: 0
              },
              {
                date: '2023年3月第2周',
                value: 0
              },
              {
                date: '2023年3月第3周',
                value: 0
              },
              {
                date: '2023年3月第4周',
                value: 0
              }
            ]
          },
          contract: {
            period1: 0,
            period2: 0,
            period3: 0
          },
          trading: {
            project: 0,
            dealProject: 0,
            dealMoney: 0,
            rate: 0
          },
          unitWork: [
            {
              area: '荷城街道',
              rate: 0
            },
            {
              area: '杨和镇',
              rate: 0
            },
            {
              area: '明城镇',
              rate: 0
            },
            {
              area: '更合镇',
              rate: 0
            }
          ]
        }
      }

      this.jinduCode = ['D440605', 'D440607', 'D440604', 'D440606', 'D440608']
      if (this.areaCode.length < 12) {
        this.unitWork = []
        const res = await getJindu({ year: year, areaCode: this.areaCode })
        this.unitWork = res.data
      }

      // 请求工作进度数据
      const res1 = await getProgress({ year: year, areaCode: this.areaCode })
      this.workProgress = res1
      this.barShow = false
      setTimeout(() => {
        this.barShow = true
      }, 0)
      // 总进度
      this.totalRate = this.workProgress.data.H * 100
      // 每周工作动态下半部分
      const res2 = await dynamic({
        year: year,
        areaCode: this.areaCode,
        itemType: 3
      })
      this.workDynamic = res2.data
      this.onChangeTrading(0)

      // 每周工作动态上半部分
      const res3 = await getProgressTop({
        year: year,
        areaCode: this.areaCode
      })
      this.data.zcqr.rate = []
      this.data.zcqc.rate = []
      this.data.zcfh.rate = []
      this.data.zcmb.rate = []
      for (const key in res3.data) {
        const element = res3.data[key]
        const keys = key
        this.data.zcqr.rate.push({
          date: keys,
          value: element.takePicNum
        })
        this.data.zcmb.rate.push({
          date: keys,
          value: element.strokeNum
        })
      }
      this.getWorkTrendData(true)
      this.contractData = [
        { name: '3个月以内 ', value: this.workDynamic.advent1 },
        { name: '3~12个月', value: this.workDynamic.advent3 },
        { name: '6~12个月', value: this.workDynamic.advent6 }
      ]
      // 资产交易情况滚动播放
      if (!this.timeSetInterval) {
        this.timeSetInterval = setInterval(() => {
          this.tradingIndex++
          if (this.tradingIndex > 4) {
            this.tradingIndex = 0
          }
          this.onChangeTrading(this.tradingIndex)
        }, this.playInterval)
      }
    },
    backgroundImage(bar, rate) {
      const index = bar - 1
      const coloredBlock = Math.ceil(rate / 10)
      const shouldColor = bar <= coloredBlock
      return shouldColor
        ? `linear-gradient(to right,
      rgb(${(index * 70) / coloredBlock},${25 + (index * 200) / coloredBlock},${85 + (index * 160) / coloredBlock}),
       rgb(${((index + 1) * 70) / coloredBlock},${25 + ((index + 1) * 200) / coloredBlock},${85 + ((index + 1) * 160) / coloredBlock})
       )`
        : 'transparent'
    },
    getAssetTrendData() {
      if (this.dateType === 'day') {
        this.tradingData = this.assetTradingDayData[this.$refs['_tradingRef'].currentPage - 1]
      }
    },
    getWorkTrendData(falg = false) {
      const zcmb = this.data.zcmb.rate
      const zcqr = this.data.zcqr.rate
      this.totalPage = Math.ceil(Math.max(zcqr.length, zcmb.length) / this.pageSize)
      const currentPage = falg ? this.totalPage : this.$refs['_weekRef'].currentPage
      this.page_zcqr = []
      this.page_zcmb = []
      this.page_zcfh = []
      this.page_zcqc = []
      this.xAxisData = []
      for (let i = 0; i < this.pageSize; i++) {
        const index = (currentPage - 1) * this.pageSize + i
        if (this.data.zcqr.rate.length > index) {
          this.page_zcqr.push(this.data.zcqr.rate[index].value)
        }
        if (this.data.zcmb.rate.length > index) {
          this.page_zcmb.push(this.data.zcmb.rate[index].value)
        }
        if (this.data.zcqr.rate.length > index) {
          this.xAxisData.push(this.data.zcqr.rate[index].date)
        }
      }
      this.workTrend.xAxisData = this.xAxisData
      this.workTrend.series = [
        {
          name: '资产拍照',
          type: 'bar',
          data: this.page_zcqr
        },
        {
          name: '资产描边',
          type: 'bar',
          data: this.page_zcmb
        },
        {
          name: '提交复核',
          type: 'bar',
          data: this.page_zcfh
        }
      ]
    },
    getGaugeOption(title, data, config) {
      return {
        title: {
          text: title,
          x: 'center',
          y: '85%',
          textStyle: {
            fontWeight: 'normal'
          }
        },
        grid: {
          bottom: config?.grid.bottom || 60
        },
        series: [
          {
            type: 'gauge',
            startAngle: 90,
            endAngle: -270,
            pointer: {
              show: false
            },
            progress: {
              show: true,
              overlap: false,
              roundCap: true,
              clip: false,
              itemStyle: {
                borderWidth: 1,
                borderColor: '#464646'
              }
            },
            axisLine: {
              lineStyle: {
                width: 20
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            data: data,
            detail: {
              width: 10,
              height: 14,
              fontSize: 20,
              color: 'inherit',
              offsetCenter: [0, 0],
              formatter: '{value}%'
            }
          }
        ]
      }
    },
    getBarOption(xData, yData) {
      return {
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            rotate: 60
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100
        },
        series: [
          {
            data: yData,
            type: 'bar'
          }
        ],
        grid: {
          left: 100,
          bottom: 120
        }
      }
    },
    initZcqc() {
      const gaugeDom = document.getElementById('zcqc_gauge')
      const gaugeChart = echarts.init(gaugeDom)
      const gaugeData = [
        {
          value: ((this.data.zcqc.asset * 100) / (this.data.asset.zyx + this.data.asset.jyx + this.data.asset.fjyx)).toFixed(2)
        }
      ]
      const gaugeOption = this.getGaugeOption('进度', gaugeData)
      gaugeOption && gaugeChart.setOption(gaugeOption)

      const barDom = document.getElementById('zcqc_bar')
      const barChart = echarts.init(barDom)
      const barXData = this.data.zcqc.rate.map((item) => item.date)
      const barYData = this.data.zcqc.rate.map((item) => item.value)
      const barOption = this.getBarOption('环比增长', barXData, barYData)
      barOption && barChart.setOption(barOption)
    },
    initZcmb() {
      const gaugeDom = document.getElementById('zcmb_gauge')
      const gaugeChart = echarts.init(gaugeDom)
      const gaugeData = [
        {
          value: ((this.data.zcmb.asset * 100) / (this.data.asset.zyx + this.data.asset.jyx + this.data.asset.fjyx)).toFixed(2)
        }
      ]
      const gaugeOption = this.getGaugeOption('进度', gaugeData)
      gaugeOption && gaugeChart.setOption(gaugeOption)

      const barDom = document.getElementById('zcmb_bar')
      const barChart = echarts.init(barDom)
      const barXData = this.data.zcmb.rate.map((item) => item.date)
      const barYData = this.data.zcmb.rate.map((item) => item.value)
      const barOption = this.getBarOption('环比增长', barXData, barYData)
      barOption && barChart.setOption(barOption)
    },
    initZcfh() {
      const gaugeDom = document.getElementById('zcfh_gauge')
      const gaugeChart = echarts.init(gaugeDom)
      const gaugeData = [
        {
          value: ((this.data.zcfh.asset * 100) / (this.data.asset.zyx + this.data.asset.jyx + this.data.asset.fjyx)).toFixed(2)
        }
      ]
      const gaugeOption = this.getGaugeOption('进度', gaugeData)
      gaugeOption && gaugeChart.setOption(gaugeOption)

      const barDom = document.getElementById('zcfh_bar')
      const barChart = echarts.init(barDom)
      const barXData = this.data.zcfh.rate.map((item) => item.date)
      const barYData = this.data.zcfh.rate.map((item) => item.value)
      const barOption = this.getBarOption('环比增长', barXData, barYData)
      barOption && barChart.setOption(barOption)
    },
    initZcqr() {
      const gaugeDom = document.getElementById('zcqr_gauge')
      const gaugeChart = echarts.init(gaugeDom)
      const gaugeData = [
        {
          value: ((this.data.zcqr.asset * 100) / (this.data.asset.zyx + this.data.asset.jyx + this.data.asset.fjyx)).toFixed(2)
        }
      ]
      const gaugeOption = this.getGaugeOption('进度', gaugeData)
      gaugeOption && gaugeChart.setOption(gaugeOption)

      const barDom = document.getElementById('zcqr_bar')
      const barChart = echarts.init(barDom)
      const barXData = this.data.zcqr.rate.map((item) => item.date)
      const barYData = this.data.zcqr.rate.map((item) => item.value)
      const barOption = this.getBarOption('环比增长', barXData, barYData)
      barOption && barChart.setOption(barOption)
    },
    initContract() {
      const gaugeDom1 = document.getElementById('contract_1')
      const gaugeDom2 = document.getElementById('contract_2')
      const gaugeDom3 = document.getElementById('contract_3')
      const gaugeChart1 = echarts.init(gaugeDom1)
      const gaugeChart2 = echarts.init(gaugeDom2)
      const gaugeChart3 = echarts.init(gaugeDom3)
      const total = this.data.contract.period1 + this.data.contract.period2 + this.data.contract.period3
      const gaugeData1 = [{ value: ((this.data.contract.period1 * 100) / total).toFixed(2) }]
      const gaugeData2 = [{ value: ((this.data.contract.period2 * 100) / total).toFixed(2) }]
      const gaugeData3 = [
        {
          value: (100.0 - ((this.data.contract.period1 * 100) / total).toFixed(2) - ((this.data.contract.period2 * 100) / total).toFixed(2)).toFixed(2)
        }
      ]
      const gaugeOption1 = this.getGaugeOption('1个月到期', gaugeData1, {
        grid: { bottom: 1 }
      })
      const gaugeOption2 = this.getGaugeOption('3个月到期', gaugeData2, {
        grid: { bottom: 1 }
      })
      const gaugeOption3 = this.getGaugeOption('6个月到期', gaugeData3, {
        grid: { bottom: 1 }
      })
      gaugeOption1 && gaugeChart1.setOption(gaugeOption1)
      gaugeOption2 && gaugeChart2.setOption(gaugeOption2)
      gaugeOption3 && gaugeChart3.setOption(gaugeOption3)
    },
    onChangeTrading(index) {
      this.isShowMoneyUnit = index === 3
      this.tradingIndex = index
      this.tradingData = []
      this.getTradingChartData(this.assetTrading[index].value)
    },
    // 资产交易情况
    getTradingChartData(type) {
      const result = []
      for (const key in this.workDynamic) {
        if (Object.values(this.workDynamic[key]).length > 0) {
          this.workDynamic[key].forEach((element) => {
            result.push({
              label: element.itemName,
              value: element[type]
            })
          })
        }
      }
      if (this.dateType === 'day') {
        this.assetTradingDayData = this.division(result, 5)
        this.tradingData = this.assetTradingDayData[2]
      } else {
        this.tradingData = result
      }
    },
    division(arr, length) {
      var result = []
      for (var i = 0; i < arr.length; i += length) {
        result.push(arr.slice(i, i + length))
      }
      return result
    },
    async handleDateSearch(str) {
      this.dateType = str
      this.tradingIndex = 0
      // 重新查询
      // 请求工作进度
      const date = new Date()
      const year = date.getFullYear() - 1
      const res2 = await dynamic({
        year: year,
        areaCode: this.areaCode,
        itemType: str == 'month' ? 2 : 3,
        beginDate: '',
        endDate: ''
      })
      this.workDynamic = res2.data
      this.onChangeTrading(0)
    },
    timestampToTime() {
      // const date = new Date()
      // const year = date.getFullYear()
      // let month = (date.getMonth() + 1).toString()
      // let day = date.getDate().toString()
      // month = month.padStart(2, '0')
      // day = day.padStart(2, '0')
      // this.currentDate = year + '|' + month + '|' + day
    },
    /** 数字超过一万转换为万
     * @param { Number } value 数值
     * @param { Number } decimal 保留几位小数
     * @returns { String }
     */
    transNumberToShort(value, decimal = 2) {
      const k = 10000
      const sizes = ['', '万', '亿', '万亿']
      let i
      let str = ''
      if (value < k) {
        str = value
      } else {
        i = Math.floor(Math.log(value) / Math.log(k))
        str = (value / Math.pow(k, i)).toFixed(decimal) + '  ' + sizes[i]
      }
      return str.toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}

.mt20 {
  margin-top: 20px;
}
.asset-container {
  // display: grid;
  // grid-template-rows: 650px auto;
  // gap: 15px;
  display: flex;
  flex-wrap: wrap;
  //height: calc(100% - 110px);
  height: calc(100% - 213px);
  padding: 0 20px 20px 0;
  & > .left {
    flex: 2;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    .top {
      flex: 3;
      // height: 0;
      // width: 0;
      // display: grid;
      // grid-template-columns: 1fr 660px 1fr;
      // gap: 15px;
      display: flex;
      .top-left {
        // flex: 1 0 0;
        width: 613.33px;
        margin-bottom: 20px;
        margin: 0 20px 20px 20px;
        .box-content {
          .center {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            .item {
              display: flex;
              align-items: center;
              flex-direction: row;
              padding-left: 23px;
              height: 83px;
            }
            .icon {
              width: 50px;
              height: 50px;
              margin-right: 20px;
            }
            .icon1 {
              background: url('./assets/image/image2.png') no-repeat;
              background-size: 30px;
            }
            .icon2 {
              background: url('./assets/image/image3.png') no-repeat;
            }
            .icon3 {
              background: url('./assets/image/image4.png') no-repeat;
            }
          }
          .right {
            flex: 5;
            display: flex;
            flex-direction: column;
            .progress-item {
              flex: 1;
              display: flex;
              align-items: center;
              .item-name {
                color: #fff;
                font-size: 14px;
                font-weight: 600;
                line-height: 70px;
                font-size: 14px;
                margin: 10px 20px 0 80px;
              }
              .battery-progress {
                flex: 1;
                margin: 0 auto;
                height: 25px;
                display: flex;
                align-items: center;
                border: 1px solid rgba(134, 151, 233, 0.6);
                justify-content: flex-start;
                position: relative;

                ::v-deep .el-progress {
                  line-height: 0;
                  .el-progress-bar {
                    margin: 0 2px;
                    width: calc(100% - 4px);
                  }
                }
                ::v-deep .el-progress-bar__outer {
                  background: none;
                  border-radius: 0;
                }
                ::v-deep .el-progress-bar__inner {
                  border-radius: 0;
                  background-image: linear-gradient(to right, #1ce2dc, #2873eb);
                }
                ::v-deep .el-progress-bar__innerText {
                  font-size: 14px;
                  font-weight: bold;
                  margin: 2px 5px 0;
                }
                .name-progress {
                  width: 100%;
                }
              }
              .value {
                margin-left: 10px;
                color: #fff;
              }
              &:nth-child(1) {
                background: url('./assets/image/list1.png') 0 0 no-repeat;
              }
              &:nth-child(2) {
                background: url('./assets/image/list2.png') 0 0 no-repeat;
              }
              &:nth-child(3) {
                background: url('./assets/image/list3.png') 0 0 no-repeat;
              }
              &:nth-child(4) {
                background: url('./assets/image/list4.png') 0 0 no-repeat;
              }
            }
          }
        }
        .row-warp {
          height: 100%;
          width: 100%;
          display: grid;
          grid-template-rows: 200px 220px 220px;
          .center {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: 80px 80px;
            row-gap: 15px;
            column-gap: 30px;
            padding: 10px 30px;
            .item {
              display: flex;
              align-items: center;
              flex-direction: row;
              background: url('@/assets/image/asset/image1.png') 0 0 no-repeat;
              background-size: 100% 100%;
              padding-left: 15px;

              .value {
                color: #fff;
                font-size: 24px;
                font-weight: bolder;
                background: linear-gradient(to top, #0688f8, #fff);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
              }
            }
            .icon {
              width: 55px;
              height: 40px;
              margin-right: 10px;
            }
            .icon1 {
              background: url('./assets/image/image2.png') no-repeat;
              background-size: 55px;
            }
            .icon2 {
              background: url('./assets/image/image3.png') no-repeat;
              background-size: 55px;
            }
            .icon3 {
              background: url('./assets/image/image4.png') no-repeat;
              background-size: 55px;
            }
            .icon4 {
              background: url('./assets/image/total.png') no-repeat;
              background-size: 55px;
            }
          }
          .right {
            display: flex;
            flex-direction: column;
            height: 100%;
            margin: 0 20px;
            .progress-item {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 40px;
              margin-top: 10px;

              .item-name {
                color: #fff;
                font-weight: bold;
                font-size: 18px;
                text-align: right;
                width: 120px;
                margin-right: 20px;
              }
              .battery-progress {
                flex: 1;
                margin: 0 auto;
                height: 25px;
                display: flex;
                align-items: center;
                border: 1px solid rgba(134, 151, 233, 0.6);
                justify-content: flex-start;
                position: relative;
                ::v-deep .el-progress {
                  line-height: 0;
                  .el-progress-bar {
                    margin: 0 2px;
                    width: calc(100% - 4px);
                  }
                }
                ::v-deep .el-progress-bar__outer {
                  background: none;
                  border-radius: 0;
                }
                ::v-deep .el-progress-bar__inner {
                  border-radius: 0;
                  background-image: linear-gradient(to right, #06f2ff, #0688f8);
                }
                ::v-deep .el-progress-bar__innerText {
                  font-size: 14px;
                  font-weight: bold;
                  margin: 2px 5px 0;
                }
                .name-progress {
                  width: 100%;
                }
              }
              .value {
                color: #bff7ff;
                font-weight: bold;
                width: 100px;
                height: 20px;
                text-align: right;
              }
              &:nth-child(1) {
                background: url('./assets/image/list1.png') 0 0 no-repeat;
                background-size: 286px 40px;
              }
              &:nth-child(2) {
                background: url('./assets/image/list2.png') 0 0 no-repeat;
                background-size: 286px 40px;
              }
              &:nth-child(3) {
                background: url('./assets/image/list3.png') 0 0 no-repeat;
                background-size: 286px 40px;
              }
              &:nth-child(4) {
                background: url('./assets/image/list4.png') -4px 0 no-repeat;
                background-size: 286px 40px;
              }
            }
          }
        }
      }
      .top-center {
        // padding-left: 12px;
        // flex: 1;
        width: 613.33px;
        height: 713px;
        font-size: 18px;
        color: #fff;
        display: flex;
        flex-direction: column;
        .area-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .area-time {
          font-size: 14px;
          color: #8fd2fd;
          display: flex;
          flex-direction: row;
          align-items: center;
          .area-img {
            height: 18px;
            width: 18px;
            margin: auto;
          }
          .area-title {
            margin: 0 10px;
            letter-spacing: 1px;
          }
          .area {
            font-weight: bold;
          }
        }
        .area-org {
          display: flex;
          flex-direction: row;
          align-items: center;
          line-height: 24px;
          .area-img {
            height: 18px;
            width: 18px;
          }
          .area-title {
            margin: 0 10px;
            font-size: 14px;
            letter-spacing: 1px;
          }
          .area {
            font-weight: bold;
            background: linear-gradient(to top, #d3a27a, #fff);
            background-clip: text;
            color: transparent;
            padding-right: 15px;
          }
        }
      }
    }
  }

  & > .right {
    flex: 1;
    width: 613.33px;
    display: flex;
    flex-direction: column;
    .top-right {
      flex: 3;

      .box-content {
        flex: 1;
        padding: 0 30px 0 20px;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 10px !important;
          height: 100% !important;
        }
        &::-webkit-scrollbar-track {
          background-color: rgba(62, 167, 225, 0.1) !important;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 5px !important;
          background-color: rgba(62, 167, 225, 0.2) !important;
        }
        li {
          height: 38px;
          line-height: 36px;
          display: flex;
          // align-items: center;
          color: #fff;
          font-weight: bold;
          margin-top: 20px;
          background: url('./assets/image/fenleibg.png') no-repeat;
          .sort {
            color: #fff;
            font-size: 18px;
            text-align: center;
            width: 60px;
            margin-right: 10px;
          }
          .area-box {
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1; //行数
            width: 100px;
            position: relative;
            .area {
              font-size: 18px;
            }
          }
          .battery-progress {
            flex: 1;
            margin: 0 auto;
            height: 25px;
            display: flex;
            align-items: center;
            border: 1px solid rgba(134, 151, 233, 0.6);
            justify-content: flex-start;
            position: relative;
            top: 6px;
            ::v-deep .el-progress {
              line-height: 0;
              .el-progress-bar {
                margin: 0 2px;
                width: calc(100% - 4px);
              }
            }
            ::v-deep .el-progress-bar__outer {
              background: none;
              border-radius: 0;
            }
            ::v-deep .el-progress-bar__inner {
              border-radius: 0;
              background-image: linear-gradient(to right, #1ce2dc, #2873eb);
            }
            ::v-deep .el-progress-bar__innerText {
              font-size: 14px;
              font-weight: bold;
              margin: 2px 5px 0;
            }
            .name-progress {
              width: 100%;
            }
          }
          .battery-value {
            color: #bff7ff;
            font-weight: bold;
            width: 90px;
            max-width: 90px;
            height: 29px;
            text-align: right;
            margin-top: -3px;
          }
          .rate {
            margin: 0 10px 0 15px;
          }
        }
      }
    }
    .bottom-info {
      flex: 2;
      display: flex;
      justify-content: space-between;
      margin: 20px 0 0;
      .left {
        display: flex;
        flex-direction: column;
        .left-warp {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 20px;
          .left {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            box-shadow: none;
            .image {
              width: 90px;
              height: 80px;
              background: url('./assets/image/image1.png') no-repeat;
            }
            .data {
              color: #65ffff;
              .value {
                font-size: 30px;
              }
            }
          }
        }
      }
    }
    .bottom-right {
      flex: 3;
      .div-solt {
        display: flex;
        align-items: center;
        margin-right: 10px;
        padding: 5px 10px;
      }
      .asset-trading {
        position: relative;
        z-index: 999;
        .pager {
          margin-top: 15px;
        }
      }
      .line-box {
        height: 228px;
        width: 100%;
        margin-top: 20px;
      }
    }
  }

  .bottom {
    flex: 1;
    display: flex;
    margin: 0 0 0 20px;
    .bottom-left {
      flex: 2;
      display: flex;
      flex-direction: column;
      position: relative;
      .bar-warp {
        height: 100%;
      }
    }
  }
  .head-bg {
    width: 100%;
    height: 100px;
    position: relative;
    background: url('./assets/image/header.png') 0 0 no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px 0;

    .right-btn {
      position: absolute;
      top: 35px;
      right: 50px;
      cursor: pointer;
      height: 100%;
      width: 150px;
      min-width: 130px;
      min-height: 40px;
      max-height: 50px;
      max-width: 150px;
      background: url('./assets/image/btn2.png') 0 0 no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: -1.5%;
      .name {
        color: #61d3f7;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }

  .title {
    font-size: 18px;
    color: #fff;
    height: 50px;
    line-height: 50px;
    padding-left: 50px;
    background: url('./assets/image/title.png') no-repeat;
  }
  .tranding {
    font-size: 14px;
    font-weight: bold;
    margin-top: 15px;
    margin-left: 15px;
  }
}
</style>
