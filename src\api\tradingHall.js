import request from '@/utils/request'

// 获取分页列表
export function getData(data) {
  return request({
    url: '/bid/anon/bidHome/listBids/today',
    method: 'post',
    data,
    withCredentials: true,
    payload: true // json格式
  })
}

// 获取分页列表
export function getServerTime() {
  return request({
    url: '/bid/anon/bidOpt/time ',
    method: 'get',
    withCredentials: true,
    payload: true // json格式
  })
}

// 详情接口
export function getOne(data) {
  return request({
    url: '/bid/anon/bidHome/detail/getOne',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

// 验证是否登录
export function userDetail(data) {
  return request({
    url: '/bid/anon/userApi/userDetail',
    method: 'post',
    params: data,
    withCredentials: true
  })
}

// 验证是否报名
export function userInfo(data) {
  return request({
    url: '/bid/anon/bidOpt/userInfo',
    payload: true, // json格式
    method: 'post',
    data: data,
    withCredentials: true
  })
}

// 获取保证金账户信息
export function getMarginAccountInformation(data) {
  return request({
    url: '/bid/anon/bidHome/detail/getMarginAccountInformation',
    method: 'get',
    params: data,
    withCredentials: true
  })
}
// 报名并获取保证金账号
export function intentionRegistration(data) {
  return request({
    url: '/bid/anon/bidHome/detail/intentionRegistration',
    method: 'get',
    params: data,
    withCredentials: true
  })
}

// 出价验证
export function priceToken(data) {
  return request({
    url: '/bid/anon/bidOpt/priceToken',
    method: 'post',
    payload: true,
    data: data
  })
}

// 出价
export function price(data) {
  return request({
    url: '/bid/anon/bidOpt/price',
    method: 'post',
    payload: true,
    data: data
  })
}
// 更新出价记录
export function refresh(data) {
  console.log(data)
  return request({
    url: '/bid/anon/bidOpt/refresh',
    method: 'get',
    params: data
  })
}

// 围观次数
export function updateHitCount(data) {
  return request({
    url: '/bid/anon/bidHome/listBids/updateHitCount',
    method: 'get',
    params: data
  })
}

// 获取手机验证码
export function sendSmsCode(data) {
  return request({
    url: '/bid/anon/userApi/sendSmsCode',
    method: 'post',
    data: data
  })
}

// 验证验证码是否通过
export function validateCode(data) {
  return request({
    url: '/bid/anon/bidOpt/validateCode',
    method: 'post',
    data: data
  })
}
