<template>
  <div class="asset-container">
    <!-- <div class="head-bg">
      <div class="left-btn">
        <div class="area-time">
          <img class="area-img" src="./assets/image/date.png" alt="" />
          <span class="area-title" style="font-size: 18px">统计截止时间：</span>
          <span class="area" style="font-size: 18px">{{ currentDate }}</span>
        </div>
        <div class="area-org">
          <img class="area-img" src="./assets/image/icon1.png" alt="" />
          <span class="area-title" style="font-size: 18px">当前地区：</span>
          <span class="area" style="font-size: 25px">| {{ currentArea }} |</span>
        </div>
      </div>
      <div class="right-btn" style="right: 350px" @click="$router.push({ path: '/trading' })">
        <span class="name">资产交易</span>
      </div>
      <div class="right-btn" style="right: 200px" @click="$router.push({ path: '/asset' })">
        <span class="name">资产画像</span>
      </div>
      <div class="right-btn" @click="goMapCloud">
        <span class="name">资产云图</span>
      </div>
    </div> -->
    <div class="left">
      <div class="left-btn">
        <div class="area-time">
          <img class="area-img" src="./assets/image/date.png" alt="" />
          <span class="area-title" style="font-size: 18px">统计截止时间：</span>
          <span class="area" style="font-size: 18px">{{ currentDate }}</span>
        </div>
        <div class="area-org">
          <img class="area-img" src="./assets/image/icon1.png" alt="" />
          <span class="area-title" style="font-size: 18px">当前地区：</span>
          <span class="area" style="font-size: 25px">| {{ currentArea }} |</span>
        </div>
      </div>
      <mapNav :key="currentId" class="map" :area-code="areaCode" @gotopath="gotopath" />
      <div class="progress">
        <panel title="各单位发起清查机构的情况" title-width="450px">
          <div :data="unitWork" class="seamless-warp" style="height: 100%; overflow: hidden">
            <ul v-if="JSON.stringify(data) !== '{}'" class="box-content">
              <li v-for="(item, key) in unitWork" :key="key" style="cursor: pointer" @click="handleMap(item)">
                <div class="sort"></div>
                <div class="area-box">
                  <span class="area" :title="item.areaName" style="font-size: 17px">{{ item.areaName }}</span>
                </div>
                <div class="battery-progress">
                  <el-progress class="name-progress" :text-inside="true" :stroke-width="18" :percentage="Number((item.rate * 100).toFixed(2))" />
                </div>
                <div class="battery-value" style="margin-left: 20px">{{ transNumberToShort(item.inventoryOrgNum) }}个</div>
              </li>
            </ul>
          </div>
        </panel>
      </div>
    </div>
    <div class="right">
      <div class="top">
        <panel title="工作进度分析" title-width="450px">
          <div class="row-warp">
            <div class="center">
              <div class="item" style="flex: 1">
                <div class="icon icon4" />
                <div class="right-content">
                  <div>
                    <div style="color: #fff; margin-bottom: 4px; font-size: 14px">总资产(宗)</div>
                    <number-flash :number="workProgress.data?.ZZC" :precision="0" />
                  </div>
                </div>
              </div>
              <div class="item" style="flex: 1">
                <div class="icon icon1" />
                <div class="right-content">
                  <div>
                    <div style="color: #fff; font-size: 14px; margin-bottom: 4px">资源性资产(宗)</div>
                    <number-flash :number="workProgress.data?.Z" :precision="0" />
                  </div>
                </div>
              </div>
              <div class="item" style="flex: 1">
                <div class="icon icon2" />
                <div class="right-content">
                  <div>
                    <div style="color: #fff; font-size: 14px; margin-bottom: 4px">经营性资产(宗)</div>
                    <number-flash :number="workProgress.data?.J" :precision="0" />
                  </div>
                </div>
              </div>
              <div class="item" style="flex: 1">
                <div class="icon icon3" />
                <div class="right-content">
                  <div>
                    <div style="color: #fff; font-size: 14px; margin-bottom: 4px">非经营性资产(宗)</div>
                    <number-flash :number="workProgress.data?.F" :precision="0" />
                  </div>
                </div>
              </div>
            </div>
            <gauge-chart class="left" title="完成率" style="padding-top: 15px" :rate="totalRate" />
            <div v-if="JSON.stringify(data) !== '{}'" class="right">
              <div class="progress-item">
                <div class="item-name">发起公示</div>
                <div v-if="barShow" class="battery-progress">
                  <!-- <div
                      v-for="bar in 10"
                      :key="bar"
                      :style="{
                        backgroundImage: backgroundImage(
                          bar,
                          Number((workProgress.data?.publicRate * 100).toFixed(2))
                        )
                      }"
                      class="battery-bar"
                    />
                    <span class="bar-text"> {{ (workProgress.data?.publicRate * 100).toFixed(2)+ "%" }}</span> -->
                  <el-progress class="name-progress" :text-inside="true" :stroke-width="18" :percentage="Number((workProgress.data?.publicRate * 100).toFixed(2))" />
                </div>
                <div class="value" style="margin-right: 20px">{{ transNumberToShort(workProgress.data?.publicNum) }}宗</div>
              </div>
              <div class="progress-item">
                <div class="item-name">资产描边</div>
                <div v-if="barShow" class="battery-progress">
                  <!-- <div
                      v-for="bar in 10"
                      :key="bar"
                      :style="{
                        backgroundImage: backgroundImage(
                          bar,
                          Number((workProgress.data?.geoRate * 100).toFixed(2))
                        )
                      }"
                      class="battery-bar"
                    />
                    <span class="bar-text">
                      {{
                        (workProgress.data?.geoRate * 100).toFixed(2) + '%'
                      }}</span> -->
                  <el-progress class="name-progress" :text-inside="true" :stroke-width="18" :percentage="Number((workProgress.data?.geoRate * 100).toFixed(2))" />
                </div>
                <div class="value" style="margin-right: 20px">{{ transNumberToShort(workProgress.data?.geoNum) }}宗</div>
                <!-- <div class="value">
                    <template>
                    {{
                      (
                        (data.zcmb.asset * 100) /
                        (data.asset.zyx + data.asset.jyx + data.asset.fjyx)
                      ).toFixed(2) + "%"
                    }}
                    </template>
                  </div> -->
              </div>
              <div class="progress-item">
                <div class="item-name">资产拍照</div>
                <div v-if="barShow" class="battery-progress">
                  <!-- <div
                      v-for="bar in 10"
                      :key="bar"
                      :style="{
                        backgroundImage: backgroundImage(
                          bar,
                          Number(
                            (workProgress.data?.takePicRate * 100).toFixed(2)
                          )
                        )
                      }"
                      class="battery-bar"
                    />
                    <span class="bar-text">
                      {{
                        (workProgress.data?.takePicRate * 100).toFixed(2) + '%'
                      }}</span> -->
                  <el-progress class="name-progress" :text-inside="true" :stroke-width="18" :percentage="Number((workProgress.data?.takePicRate * 100).toFixed(2))" />
                </div>
                <div class="value" style="margin-right: 20px">{{ transNumberToShort(workProgress.data?.takePicNum) }}宗</div>
              </div>
              <div class="progress-item">
                <div class="item-name">发起清查</div>
                <div v-if="barShow" class="battery-progress">
                  <!-- <div
                      v-for="bar in 10"
                      :key="bar"
                      :style="{
                        backgroundImage: backgroundImage(
                          bar,
                          Number((workProgress.data?.taskRate * 100).toFixed(2))
                        )
                      }"
                      class="battery-bar"
                    />
                    <span class="bar-text">
                      {{
                        (workProgress.data?.taskRate * 100).toFixed(2) + '%'
                      }}</span> -->
                  <el-progress class="name-progress" :text-inside="true" :stroke-width="18" :percentage="Number((workProgress.data?.taskRate * 100).toFixed(2))" />
                </div>
                <div class="value" style="margin-right: 20px">{{ transNumberToShort(workProgress.data?.taskNum) }}宗</div>
              </div>
            </div>
          </div>
        </panel>
      </div>
      <div style="height: 10px" />
      <div class="center">
        <panel title="每周工作动态" title-width="450px">
          <pager ref="_weekRef" :total-page="totalPage" @prevPage="getWorkTrendData()" @nextPage="getWorkTrendData()" />
          <div class="box-content" style="width: 100%; height: 100%">
            <bar-line-chart v-bind="workTrend" />
          </div>
        </panel>
      </div>
      <div style="height: 10px" />
      <div class="bottom">
        <div class="left">
          <panel title="闲置资产 ( 宗数 )" title-width="250px">
            <div class="left-warp">
              <div class="left">
                <div class="image" />
                <div class="data">
                  <div class="value">
                    {{ workDynamic.freeAssetNum }}
                  </div>
                </div>
              </div>
            </div>
          </panel>
        </div>
        <div class="center">
          <panel title="临期资产 ( 宗数 )" title-width="450px">
            <pie-chart style="margin-top: 0px" :data="contractData" />
          </panel>
        </div>
        <div class="right">
          <panel title="资产交易情况" title-width="450px">
            <div class="asset-trading" @mouseenter.stop="handleMouseEnter" @mouseleave.stop="handleMouseLeave">
              <pager ref="_tradingRef" :total-page="dateType == 'month' ? 1 : 3" @prevPage="getAssetTrendData()" @nextPage="getAssetTrendData()" />
            </div>
            <div slot="area" class="div-solt">
              <div class="date-btn" :class="{ 'active-tab': dateType === 'month' }" @click="handleDateSearch('month')">月</div>
              <div class="date-btn" :class="{ 'active-tab': dateType === 'day' }" @click="handleDateSearch('day')">日</div>
            </div>
            <div class="tranding">
              <span v-for="(item, index) of assetTrading" :key="index" :class="tradingIndex == index ? 'active' : ''" @click="onChangeTrading(index)">{{ item.label }}</span>
            </div>
            <div class="line-box" @mouseenter.stop="handleMouseEnter" @mouseleave.stop="handleMouseLeave">
              <lineChart v-if="tradingData.length > 0" :chart-data="tradingData" :font-size="11" :is-show-money-unit="isShowMoneyUnit" />
            </div>
          </panel>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import vueSeamlessScroll from 'vue-seamless-scroll'
import * as echarts from 'echarts'
import mapNav from './views/mapNav.vue'
import GaugeChart from '@/views/GaugeChart'
import NumberFlash from '@/views/NumberFlash'
import BarLineChart from '@/views/BarLineChart'
import lineChart from '@/views/lineChart.vue'
// import PieChart from '@/views/PieChart/index.vue'
import PieChart from '@/views/PieChart.vue'
import panel from '@/views/Panel.vue'
import pager from '@/views/pager.vue'

import { getData, getProgress, dynamic, getProgressTop, getJindu } from '@/api/common.js'
export default {
  components: {
    // vueSeamlessScroll,
    mapNav,
    GaugeChart,
    NumberFlash,
    BarLineChart,
    PieChart,
    panel,
    pager,
    lineChart
  },
  data() {
    return {
      currentId: Date.now(),
      currentArea: '佛山市',
      currentDate: '',
      currentAccount: 'fs',
      headerTitle: '佛山市农村集体资产清产核资工作',
      areaCode: 'D4406',
      barShow: true,
      allData: require('./views/data.json'),
      data: {},
      totalRate: 0,
      workTrend: {
        xAxisData: [],
        series: [],
        barMin: 0,
        barMax: 8,
        color: ['#4927ed', '#5bec37', '#e63a79', '#f67c37', '#ddec37'],
        lineMin: 0,
        xAxisLabelRotate: 60
      },
      currentPage: 1,
      totalPage: 2,
      pageSize: 5,
      page_zcqr: [],
      page_zcmb: [],
      page_zcfh: [],
      page_zcqc: [],
      xAxisData: [],

      contractData: [],
      unitWork: [],
      percentage: 0,
      // 工作进度分析
      workProgress: {},
      // 每周工作动态下半部分
      workDynamic: {},
      // 各单位code
      jinduCode: '',
      // 各单位进度值
      jinduValue: {},
      tradingIndex: 0,
      tradingData: [],
      assetTrading: [
        {
          label: '交易公告发布宗数',
          value: 'transAnnounceNum'
        },
        {
          label: '交易成交宗数',
          value: 'successPrjNum'
        },
        {
          label: '成功交易比例',
          value: 'successRate'
        },
        {
          label: '成交金额',
          value: 'transAmount'
        },
        {
          label: '平均溢价率',
          value: 'avePreRate'
        }
      ],
      records: require('./views/mapData/foshan_area_data.json'),
      assetTradingDayData: [],
      dateType: 'day',
      playInterval: 5000,
      timeSetInterval: null,
      isShowMoneyUnit: false
    }
  },
  computed: {
    negativeRate() {
      return this.rate < 0
    }, // 是否为负数比率
    getCurrentPage() {
      return this.currentPage
    }
  },
  watch: {
    currentAccount: {
      handler: function (val) {
        localStorage.setItem('currentAccount', val)
      },
      immediate: true
    },
    tradingIndex: {
      handler: function (val) {
        this.$refs['_tradingRef'].currentPage = this.dateType == 'month' ? 1 : 3
      },
      immediate: true
    }
  },
  created() {
    this.getdate1()
    this.timestampToTime()
    this.gotopath('4406')
  },
  beforeDestroy() {
    clearInterval(this.timeSetInterval) // 清除定时器
    this.timeSetInterval = null
  },
  methods: {
    handleMouseEnter() {
      this.pauseTimer()
    },
    handleMouseLeave() {
      this.startTimer()
    },
    pauseTimer() {
      if (this.timeSetInterval) {
        clearInterval(this.timeSetInterval)
        this.timeSetInterval = null
      }
    },
    startTimer() {
      if (this.playInterval <= 0 || this.timeSetInterval) return
      this.timeSetInterval = setInterval(this.playSlides, this.playInterval)
    },
    resetTimer() {
      this.pauseTimer()
      this.startTimer()
    },
    playSlides() {
      this.tradingIndex++
      if (this.tradingIndex > 4) {
        this.tradingIndex = 0
      }
      this.onChangeTrading(this.tradingIndex)
    },
    // 获取时间
    async getdate1() {
      const res = await getData()
      this.currentDate = res.data
    },
    goMapCloud() {
      const url = `${window.homeData.mapCloudUrl}?username=${this.currentAccount}_ys&token=${this.currentAccount}`
      window.open(url, '_blank')
    },
    handleMap(item) {
      console.log('🚀 ~ file: App.vue:436 ~ handleMap ~ item:', item)
      const code = item.areaCode.substring(1, item.areaCode.length)
      this.gotopath(code)
    },
    // 点击地区时切换数据
    // eslint-disable-next-line complexity
    async gotopath(type) {
      this.currentArea = this.records.records.find((item) => {
        return item.areaCode == `D${type}`
      }).areaName
      this.areaCode = this.records.records.find((item) => {
        return item.areaCode == `D${type}`
      }).areaCode
      if (type != undefined) {
        this.areaCode = type
        this.areaCode = 'D' + this.areaCode
      }
      console.log(this.areaCode)
      if (this.areaCode == 'D440605') {
        this.headerTitle = '南海区农村集体资产清产核资工作'
        this.currentAccount = 'nh'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '三水区农村集体资产清产核资工作'
        this.currentAccount = 'ss'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '禅城区农村集体资产清产核资工作'
        this.currentAccount = 'cc'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '顺德区农村集体资产清产核资工作'
        this.currentAccount = 'sd'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '高明区农村集体资产清产核资工作'
        this.currentAccount = 'gm'
      }
      if (this.areaCode == 'D440605') {
        this.headerTitle = '佛山市农村集体资产清产核资工作'
        this.currentAccount = 'fs'
      }
      // switch (type) {
      // case '440605':
      //   this.areaCode = 'D440605'
      //   this.headerTitle = '南海区农村集体资产清产核资工作'
      //   this.currentArea = '南海区'
      //   this.currentAccount = 'nh'
      //   break
      // case '440607':
      //   this.areaCode = 'D440607'
      //   this.headerTitle = '三水区农村集体资产清产核资工作'
      //   this.currentArea = '三水区'
      //   this.currentAccount = 'ss'
      //   break
      // case '440604':
      //   this.areaCode = 'D440604'
      //   this.headerTitle = '禅城区农村集体资产清产核资工作'
      //   this.currentArea = '禅城区'
      //   this.currentAccount = 'cc'
      //   break
      // case '440606':
      //   this.areaCode = 'D440606'
      //   this.headerTitle = '顺德区农村集体资产清产核资工作'
      //   this.currentArea = '顺德区'
      //   this.currentAccount = 'sd'
      //   break
      // case '440608':
      //   this.areaCode = 'D440608'
      //   this.headerTitle = '高明区农村集体资产清产核资工作'
      //   this.currentArea = '高明区'
      //   this.currentAccount = 'gm'
      //   break
      // case '4406':
      //   this.areaCode = 'D4406'
      //   this.headerTitle = '佛山市农村集体资产清产核资工作'
      //   this.currentArea = '佛山市'
      //   this.currentAccount = 'fs'
      //   this.currentId = Date.now()
      //   break
      // }

      // 请求工作进度
      const date = new Date()
      const year = date.getFullYear() - 1

      this.data = this.allData[this.areaCode]
      // 判断data是否为空
      if (this.data == undefined) {
        this.data = {
          asset: {
            zyx: 9934,
            jyx: 1574,
            fjyx: 2732
          },
          zcqc: {
            asset: 0,
            rate: [
              {
                date: '2023年2月第1周',
                value: 0
              },
              {
                date: '2023年2月第2周',
                value: 0
              },
              {
                date: '2023年2月第3周',
                value: 0
              },
              {
                date: '2023年2月第4周',
                value: 0
              },
              {
                date: '2023年2月第5周',
                value: 0
              },
              {
                date: '2023年3月第1周',
                value: 0
              },
              {
                date: '2023年3月第2周',
                value: 0
              },
              {
                date: '2023年3月第3周',
                value: 0
              },
              {
                date: '2023年3月第4周',
                value: 0
              }
            ]
          },
          zcmb: {
            asset: 0,
            rate: [
              {
                date: '2023年2月第1周',
                value: 0
              },
              {
                date: '2023年2月第2周',
                value: 0
              },
              {
                date: '2023年2月第3周',
                value: 0
              },
              {
                date: '2023年2月第4周',
                value: 0
              },
              {
                date: '2023年2月第5周',
                value: 0
              },
              {
                date: '2023年3月第1周',
                value: 0
              },
              {
                date: '2023年3月第2周',
                value: 0
              },
              {
                date: '2023年3月第3周',
                value: 0
              },
              {
                date: '2023年3月第4周',
                value: 0
              }
            ]
          },
          zcfh: {
            asset: 0,
            rate: [
              {
                date: '2023年2月第1周',
                value: 0
              },
              {
                date: '2023年2月第2周',
                value: 0
              },
              {
                date: '2023年2月第3周',
                value: 0
              },
              {
                date: '2023年2月第4周',
                value: 0
              },
              {
                date: '2023年2月第5周',
                value: 0
              },
              {
                date: '2023年3月第1周',
                value: 0
              },
              {
                date: '2023年3月第2周',
                value: 0
              },
              {
                date: '2023年3月第3周',
                value: 0
              },
              {
                date: '2023年3月第4周',
                value: 0
              }
            ]
          },
          zcqr: {
            asset: 0,
            rate: [
              {
                date: '2023年2月第1周',
                value: 0
              },
              {
                date: '2023年2月第2周',
                value: 0
              },
              {
                date: '2023年2月第3周',
                value: 0
              },
              {
                date: '2023年2月第4周',
                value: 0
              },
              {
                date: '2023年2月第5周',
                value: 0
              },
              {
                date: '2023年3月第1周',
                value: 0
              },
              {
                date: '2023年3月第2周',
                value: 0
              },
              {
                date: '2023年3月第3周',
                value: 0
              },
              {
                date: '2023年3月第4周',
                value: 0
              }
            ]
          },
          contract: {
            period1: 0,
            period2: 0,
            period3: 0
          },
          trading: {
            project: 0,
            dealProject: 0,
            dealMoney: 0,
            rate: 0
          },
          unitWork: [
            {
              area: '荷城街道',
              rate: 0
            },
            {
              area: '杨和镇',
              rate: 0
            },
            {
              area: '明城镇',
              rate: 0
            },
            {
              area: '更合镇',
              rate: 0
            }
          ]
        }
      }

      this.jinduCode = ['D440605', 'D440607', 'D440604', 'D440606', 'D440608']
      if (this.areaCode.length < 12) {
        this.unitWork = []
        const res = await getJindu({ year: year, areaCode: this.areaCode })
        this.unitWork = res.data
      }

      // 请求工作进度数据
      const res1 = await getProgress({ year: year, areaCode: this.areaCode })
      this.workProgress = res1
      this.barShow = false
      setTimeout(() => {
        this.barShow = true
      }, 0)
      // 总进度
      this.totalRate = this.workProgress.data.H * 100
      // 每周工作动态下半部分
      const res2 = await dynamic({
        year: year,
        areaCode: this.areaCode,
        itemType: 3
      })
      this.workDynamic = res2.data
      this.onChangeTrading(0)

      // 每周工作动态上半部分
      const res3 = await getProgressTop({
        year: year,
        areaCode: this.areaCode
      })
      this.data.zcqr.rate = []
      this.data.zcqc.rate = []
      this.data.zcfh.rate = []
      this.data.zcmb.rate = []
      for (const key in res3.data) {
        const element = res3.data[key]
        const keys = key
        this.data.zcqr.rate.push({
          date: keys,
          value: element.takePicNum
        })
        this.data.zcmb.rate.push({
          date: keys,
          value: element.strokeNum
        })
      }
      this.getWorkTrendData(true)
      this.contractData = [
        { name: '3个月以内 ', value: this.workDynamic.advent1 },
        { name: '3~12个月', value: this.workDynamic.advent3 },
        { name: '6~12个月', value: this.workDynamic.advent6 }
      ]
      // 资产交易情况滚动播放
      if (!this.timeSetInterval) {
        this.timeSetInterval = setInterval(() => {
          this.tradingIndex++
          if (this.tradingIndex > 4) {
            this.tradingIndex = 0
          }
          this.onChangeTrading(this.tradingIndex)
        }, this.playInterval)
      }
    },
    backgroundImage(bar, rate) {
      const index = bar - 1
      const coloredBlock = Math.ceil(rate / 10)
      const shouldColor = bar <= coloredBlock
      return shouldColor
        ? `linear-gradient(to right,
      rgb(${(index * 70) / coloredBlock},${25 + (index * 200) / coloredBlock},${85 + (index * 160) / coloredBlock}),
       rgb(${((index + 1) * 70) / coloredBlock},${25 + ((index + 1) * 200) / coloredBlock},${85 + ((index + 1) * 160) / coloredBlock})
       )`
        : 'transparent'
    },
    getAssetTrendData() {
      if (this.dateType === 'day') {
        this.tradingData = this.assetTradingDayData[this.$refs['_tradingRef'].currentPage - 1]
      }
    },
    getWorkTrendData(falg = false) {
      const zcmb = this.data.zcmb.rate
      const zcqr = this.data.zcqr.rate
      this.totalPage = Math.ceil(Math.max(zcqr.length, zcmb.length) / this.pageSize)
      const currentPage = falg ? this.totalPage : this.$refs['_weekRef'].currentPage
      this.page_zcqr = []
      this.page_zcmb = []
      this.page_zcfh = []
      this.page_zcqc = []
      this.xAxisData = []
      for (let i = 0; i < this.pageSize; i++) {
        const index = (currentPage - 1) * this.pageSize + i
        if (this.data.zcqr.rate.length > index) {
          this.page_zcqr.push(this.data.zcqr.rate[index].value)
        }
        if (this.data.zcmb.rate.length > index) {
          this.page_zcmb.push(this.data.zcmb.rate[index].value)
        }
        if (this.data.zcqr.rate.length > index) {
          this.xAxisData.push(this.data.zcqr.rate[index].date)
        }
      }
      this.workTrend.xAxisData = this.xAxisData
      this.workTrend.series = [
        {
          name: '资产拍照',
          type: 'bar',
          data: this.page_zcqr
        },
        {
          name: '资产描边',
          type: 'bar',
          data: this.page_zcmb
        },
        {
          name: '提交复核',
          type: 'bar',
          data: this.page_zcfh
        }
        // {
        //   name: "发起清查",
        //   type: "bar",
        //   data: this.page_zcqc,
        // },
      ]
    },
    getGaugeOption(title, data, config) {
      return {
        title: {
          text: title,
          x: 'center',
          y: '85%',
          textStyle: {
            fontWeight: 'normal'
          }
        },
        grid: {
          bottom: config?.grid.bottom || 60
        },
        series: [
          {
            type: 'gauge',
            startAngle: 90,
            endAngle: -270,
            pointer: {
              show: false
            },
            progress: {
              show: true,
              overlap: false,
              roundCap: true,
              clip: false,
              itemStyle: {
                borderWidth: 1,
                borderColor: '#464646'
              }
            },
            axisLine: {
              lineStyle: {
                width: 20
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            data: data,
            detail: {
              width: 10,
              height: 14,
              fontSize: 20,
              color: 'inherit',
              offsetCenter: [0, 0],
              formatter: '{value}%'
            }
          }
        ]
      }
    },
    getBarOption(xData, yData) {
      return {
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: {
            rotate: 60
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100
        },
        series: [
          {
            data: yData,
            type: 'bar'
          }
        ],
        grid: {
          left: 100,
          bottom: 120
        }
      }
    },
    initZcqc() {
      const gaugeDom = document.getElementById('zcqc_gauge')
      const gaugeChart = echarts.init(gaugeDom)
      const gaugeData = [
        {
          value: ((this.data.zcqc.asset * 100) / (this.data.asset.zyx + this.data.asset.jyx + this.data.asset.fjyx)).toFixed(2)
        }
      ]
      const gaugeOption = this.getGaugeOption('进度', gaugeData)
      gaugeOption && gaugeChart.setOption(gaugeOption)

      const barDom = document.getElementById('zcqc_bar')
      const barChart = echarts.init(barDom)
      const barXData = this.data.zcqc.rate.map((item) => item.date)
      const barYData = this.data.zcqc.rate.map((item) => item.value)
      const barOption = this.getBarOption('环比增长', barXData, barYData)
      barOption && barChart.setOption(barOption)
    },
    initZcmb() {
      const gaugeDom = document.getElementById('zcmb_gauge')
      const gaugeChart = echarts.init(gaugeDom)
      const gaugeData = [
        {
          value: ((this.data.zcmb.asset * 100) / (this.data.asset.zyx + this.data.asset.jyx + this.data.asset.fjyx)).toFixed(2)
        }
      ]
      const gaugeOption = this.getGaugeOption('进度', gaugeData)
      gaugeOption && gaugeChart.setOption(gaugeOption)

      const barDom = document.getElementById('zcmb_bar')
      const barChart = echarts.init(barDom)
      const barXData = this.data.zcmb.rate.map((item) => item.date)
      const barYData = this.data.zcmb.rate.map((item) => item.value)
      const barOption = this.getBarOption('环比增长', barXData, barYData)
      barOption && barChart.setOption(barOption)
    },
    initZcfh() {
      const gaugeDom = document.getElementById('zcfh_gauge')
      const gaugeChart = echarts.init(gaugeDom)
      const gaugeData = [
        {
          value: ((this.data.zcfh.asset * 100) / (this.data.asset.zyx + this.data.asset.jyx + this.data.asset.fjyx)).toFixed(2)
        }
      ]
      const gaugeOption = this.getGaugeOption('进度', gaugeData)
      gaugeOption && gaugeChart.setOption(gaugeOption)

      const barDom = document.getElementById('zcfh_bar')
      const barChart = echarts.init(barDom)
      const barXData = this.data.zcfh.rate.map((item) => item.date)
      const barYData = this.data.zcfh.rate.map((item) => item.value)
      const barOption = this.getBarOption('环比增长', barXData, barYData)
      barOption && barChart.setOption(barOption)
    },
    initZcqr() {
      const gaugeDom = document.getElementById('zcqr_gauge')
      const gaugeChart = echarts.init(gaugeDom)
      const gaugeData = [
        {
          value: ((this.data.zcqr.asset * 100) / (this.data.asset.zyx + this.data.asset.jyx + this.data.asset.fjyx)).toFixed(2)
        }
      ]
      const gaugeOption = this.getGaugeOption('进度', gaugeData)
      gaugeOption && gaugeChart.setOption(gaugeOption)

      const barDom = document.getElementById('zcqr_bar')
      const barChart = echarts.init(barDom)
      const barXData = this.data.zcqr.rate.map((item) => item.date)
      const barYData = this.data.zcqr.rate.map((item) => item.value)
      const barOption = this.getBarOption('环比增长', barXData, barYData)
      barOption && barChart.setOption(barOption)
    },
    initContract() {
      const gaugeDom1 = document.getElementById('contract_1')
      const gaugeDom2 = document.getElementById('contract_2')
      const gaugeDom3 = document.getElementById('contract_3')
      const gaugeChart1 = echarts.init(gaugeDom1)
      const gaugeChart2 = echarts.init(gaugeDom2)
      const gaugeChart3 = echarts.init(gaugeDom3)
      const total = this.data.contract.period1 + this.data.contract.period2 + this.data.contract.period3
      const gaugeData1 = [{ value: ((this.data.contract.period1 * 100) / total).toFixed(2) }]
      const gaugeData2 = [{ value: ((this.data.contract.period2 * 100) / total).toFixed(2) }]
      const gaugeData3 = [
        {
          value: (100.0 - ((this.data.contract.period1 * 100) / total).toFixed(2) - ((this.data.contract.period2 * 100) / total).toFixed(2)).toFixed(2)
        }
      ]
      const gaugeOption1 = this.getGaugeOption('1个月到期', gaugeData1, {
        grid: { bottom: 1 }
      })
      const gaugeOption2 = this.getGaugeOption('3个月到期', gaugeData2, {
        grid: { bottom: 1 }
      })
      const gaugeOption3 = this.getGaugeOption('6个月到期', gaugeData3, {
        grid: { bottom: 1 }
      })
      gaugeOption1 && gaugeChart1.setOption(gaugeOption1)
      gaugeOption2 && gaugeChart2.setOption(gaugeOption2)
      gaugeOption3 && gaugeChart3.setOption(gaugeOption3)
    },
    onChangeTrading(index) {
      this.isShowMoneyUnit = index === 3
      this.tradingIndex = index
      this.tradingData = []
      this.getTradingChartData(this.assetTrading[index].value)
    },
    // 资产交易情况
    getTradingChartData(type) {
      const result = []
      for (const key in this.workDynamic) {
        if (Object.values(this.workDynamic[key]).length > 0) {
          this.workDynamic[key].forEach((element) => {
            result.push({
              label: element.itemName,
              value: element[type]
            })
          })
        }
      }
      if (this.dateType === 'day') {
        this.assetTradingDayData = this.division(result, 5)
        this.tradingData = this.assetTradingDayData[2]
      } else {
        this.tradingData = result
      }
    },
    division(arr, length) {
      var result = []
      for (var i = 0; i < arr.length; i += length) {
        result.push(arr.slice(i, i + length))
      }
      return result
    },
    async handleDateSearch(str) {
      this.dateType = str
      this.tradingIndex = 0
      // 重新查询
      // 请求工作进度
      const date = new Date()
      const year = date.getFullYear() - 1
      const res2 = await dynamic({
        year: year,
        areaCode: this.areaCode,
        itemType: str == 'month' ? 2 : 3,
        beginDate: '',
        endDate: ''
      })
      this.workDynamic = res2.data
      this.onChangeTrading(0)
    },
    timestampToTime() {
      const date = new Date()
      const year = date.getFullYear()
      let month = (date.getMonth() + 1).toString()
      let day = date.getDate().toString()
      month = month.padStart(2, '0')
      day = day.padStart(2, '0')
      this.currentDate = year + '|' + month + '|' + day
    },
    /** 数字超过一万转换为万
     * @param { Number } value 数值
     * @param { Number } decimal 保留几位小数
     * @returns { String }
     */
    transNumberToShort(value, decimal = 2) {
      const k = 10000
      const sizes = ['', '万', '亿', '万亿']
      let i
      let str = ''
      if (value < k) {
        str = value
      } else {
        i = Math.floor(Math.log(value) / Math.log(k))
        str = (value / Math.pow(k, i)).toFixed(decimal) + '  ' + sizes[i]
      }
      return str
    }
  }
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.asset-container {
  height: calc(100% - 100px);
  display: flex;
  padding-bottom: 10px;
  .head-bg {
    width: 100%;
    height: 100px;
    position: relative;
    background: url('./assets/image/header.png') 0 0 no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px 0;

    .right-btn {
      position: absolute;
      top: 35px;
      right: 50px;
      cursor: pointer;
      height: 100%;
      width: 150px;
      min-width: 130px;
      min-height: 40px;
      max-height: 50px;
      max-width: 150px;
      background: url('./assets/image/btn2.png') 0 0 no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: -1.5%;
      .name {
        color: #61d3f7;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
  .left-btn {
    font-size: 18px;
    color: #fff;
    padding: 0 10px;
    // height: 100px;
    display: flex;
    justify-content: space-between;
    .area-time {
      font-size: 14px;
      color: #8fd2fd;
      // margin: 20px 0 40px 0;
      .area-img {
        height: 18px;
        width: 18px;
        margin: auto;
      }
      .area-title {
        margin: 0 10px;
        letter-spacing: 1px;
      }
      .area {
        font-weight: bold;
      }
    }
    .area-org {
      display: flex;
      flex-direction: row;
      font-style: italic;
      // line-height: 24px;
      .area-img {
        height: 18px;
        width: 18px;
        margin-top: 4px;
      }
      .area-title {
        margin: 0 10px;
        font-size: 14px;
        letter-spacing: 1px;
      }
      .area {
        font-weight: bold;
        background: linear-gradient(to top, #d3a27a, #fff);
        -webkit-background-clip: text;
        color: transparent;
        padding-right: 15px;
      }
    }
  }
  .current-area {
    position: absolute;
    left: 100px;
    top: 20px;
    color: #ffffff;
    font-weight: bold;
    text-shadow: 0px 0px 7px rgba(52, 255, 204, 0.1);
    background: linear-gradient(180deg, #fefbf8 16%, c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .title {
    font-size: 18px;
    color: #fff;
    height: 50px;
    line-height: 50px;
    padding-left: 50px;
    background: url('./assets/image/title.png') no-repeat;
  }
  .box-content {
    flex: 1;
  }
  & > .left {
    margin: 0 20px 10px 0;
    flex: 3;
    height: 100%;
    display: flex;
    flex-direction: column;

    .map {
      flex: 3.2;
      height: 500px;
      margin-left: 20px;
    }
    .progress {
      flex: 3;
      display: flex;
      flex-direction: column;
      overflow-y: hidden;
      margin-left: 20px;
      .box-content {
        flex: 1;
        max-height: calc(100% - 10px);
        padding: 20px 30px 20px 20px;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 0 !important;
        }
        li {
          height: 40px;
          line-height: 40px;
          display: flex;
          align-items: center;
          color: #b4e7ee;
          margin-top: 10px;
          background: url('./assets/image/fenleibg.png') no-repeat;
          .sort {
            color: #fff;
            font-size: 18px;
            text-align: center;
            width: 74px;
            margin-right: 10px;
          }
          .area-box {
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1; //行数
            width: 30%;
            position: relative;
            left: -15px;
            .area {
              font-size: 20px;
              margin: 0 15px 0 10px;
            }
          }
          .battery-progress {
            flex: 1;
            margin: 0 auto;
            height: 22px;
            display: flex;
            align-items: center;
            border: 1px solid #8aebfc;
            justify-content: flex-start;
            position: relative;
            ::v-deep .el-progress-bar__outer {
              background: none;
              border-radius: 0;
            }
            ::v-deep .el-progress-bar__inner {
              border-radius: 0;
              background-image: linear-gradient(to right, #06f2ff, #0688f8);
            }
            ::v-deep .el-progress-bar__innerText {
              font-size: 14px;
              font-weight: 550;
            }
            .name-progress {
              width: 100%;
            }
          }
          .battery-value {
            color: #bff7fe;
            width: 90px;
            max-width: 90px;
            height: 29px;
            text-align: right;
            margin-top: -3px;
          }
          .rate {
            margin: 0 10px 0 15px;
          }
        }
        // & li:nth-child(1) {
        //   background: url('./assets/image/no1.png') no-repeat;
        // }
        // & li:nth-child(2),
        // & li:nth-child(3) {
        //   background: url('./assets/image/no2-3.png') no-repeat;
        // }
      }
    }
  }
  .right {
    flex: 6;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .top {
      flex: 5;
      display: flex;
      flex-direction: column;
      min-height: 290px;
      margin-right: 20px;
      .box-content {
        flex: 1;
        display: flex;
        padding: 10px 20px 10px 20px;
        .left {
          flex: 3;
        }
        .center {
          flex: 5;
          display: flex;
          flex-direction: column;
          box-shadow: none;
          .item {
            display: flex;
            align-items: center;
            flex-direction: row;
            padding-left: 23px;
          }
          .icon {
            width: 50px;
            height: 50px;
            margin-right: 20px;
          }
          .icon1 {
            background: url('./assets/image/image2.png') no-repeat;
            background-size: 30px;
          }
          .icon2 {
            background: url('./assets/image/image3.png') no-repeat;
          }
          .icon3 {
            background: url('./assets/image/image4.png') no-repeat;
          }
        }
        .right {
          flex: 5;
          display: flex;
          flex-direction: column;
          .progress-item {
            flex: 1;
            display: flex;
            align-items: center;
            .item-name {
              color: #fff;
              font-size: 14px;
              font-weight: 600;
              line-height: 70px;
              font-size: 14px;
              margin: 10px 20px 0 80px;
            }
            .battery-progress {
              flex: 1;
              margin: 0 auto;
              height: 22px;
              display: flex;
              align-items: center;
              border: 2px solid #8aebfc;
              justify-content: flex-start;
              position: relative;
              ::v-deep .el-progress-bar__outer {
                background: none;
                border-radius: 0;
              }
              ::v-deep .el-progress-bar__inner {
                border-radius: 0;
                background-image: linear-gradient(to right, #06f2ff, #0688f8);
              }
              ::v-deep .el-progress-bar__innerText {
                font-size: 14px;
                font-weight: 550;
              }
              .name-progress {
                width: 100%;
              }
            }
            .value {
              margin-left: 10px;
              color: #bff7fe;
            }
            &:nth-child(1) {
              background: url('./assets/image/list1.png') 0 0 no-repeat;
            }
            &:nth-child(2) {
              background: url('./assets/image/list2.png') 0 0 no-repeat;
            }
            &:nth-child(3) {
              background: url('./assets/image/list3.png') 0 0 no-repeat;
            }
            &:nth-child(4) {
              background: url('./assets/image/list4.png') 0 0 no-repeat;
            }
          }
        }
      }
      .row-warp {
        height: 92%;
        width: 100%;
        flex: 1;
        display: flex;
        .left {
          flex: 3;
        }
        .center {
          flex: 5;
          display: flex;
          flex-direction: column;
          box-shadow: none;
          .item {
            display: flex;
            align-items: center;
            flex-direction: row;
            padding-left: 50px;
          }
          .icon {
            width: 40px;
            height: 48px;
            margin-right: 20px;
          }
          .icon1 {
            background: url('./assets/image/image2.png') no-repeat;
            background-size: 40px;
          }
          .icon2 {
            background: url('./assets/image/image3.png') no-repeat;
            background-size: 40px;
          }
          .icon3 {
            background: url('./assets/image/image4.png') no-repeat;
            background-size: 40px;
          }
          .icon4 {
            background: url('./assets/image/total.png') no-repeat;
            background-size: 40px;
          }
        }
        .right {
          flex: 5;
          display: flex;
          flex-direction: column;
          height: 100%;
          margin: 0 20px 0 20px;
          .progress-item {
            flex: 1;
            display: flex;
            padding-top: 24px;
            .item-name {
              color: #fff;
              font-size: 14px;
              font-weight: 500;
              margin: 0px 20px 0 60px;
            }
            .battery-progress {
              width: 170.53px;
              flex: 1;
              margin: 0 auto;
              height: 22px;
              display: flex;
              align-items: center;
              border: 1px solid #8aebfc;
              justify-content: flex-start;
              position: relative;
              ::v-deep .el-progress-bar__outer {
                background: none;
                border-radius: 0;
              }
              ::v-deep .el-progress-bar__inner {
                border-radius: 0;
                background-image: linear-gradient(to right, #06f2ff, #0688f8);
              }
              ::v-deep .el-progress-bar__innerText {
                font-size: 14px;
                font-weight: 550;
              }
              .name-progress {
                width: 100%;
              }
            }
            .value {
              margin-left: 10px;
              color: #bff7fe;
              width: 90px;
              max-width: 90px;
              height: 29px;
              text-align: right;
              margin-top: -3px;
            }
            &:nth-child(1) {
              background: url('./assets/image/list1.png') 0 0 no-repeat;
              background-size: 286px 58px;
            }
            &:nth-child(2) {
              background: url('./assets/image/list2.png') 0 0 no-repeat;
              background-size: 286px 58px;
            }
            &:nth-child(3) {
              background: url('./assets/image/list3.png') 0 0 no-repeat;
              background-size: 286px 58px;
            }
            &:nth-child(4) {
              background: url('./assets/image/list4.png') 0 0 no-repeat;
              background-size: 286px 58px;
            }
          }
        }
      }
    }
    .center {
      flex: 5;
      display: flex;
      flex-direction: column;
      position: relative;
      margin-right: 20px;
      .bar-warp {
        height: 100%;
      }
    }
    .bottom {
      flex: 4;
      display: flex;
      flex-wrap: nowrap;
      min-height: 150px;
      margin-right: 20px;
      .left {
        flex: 1.3;
        display: flex;
        flex-direction: column;
        .left-warp {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 0 20px;
          .left {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            box-shadow: none;
            .image {
              width: 90px;
              height: 80px;
              background: url('./assets/image/image1.png') no-repeat;
            }
            .data {
              color: #65ffff;
              .value {
                font-size: 30px;
              }
            }
          }
        }
      }
      .center {
        margin: 0;
        margin-left: 10px;
        flex: 2;
        .box-content {
          padding: 10px 20px;
          display: flex;
        }
      }
      .right {
        margin: 0;
        margin-left: 10px;
        flex: 2.7;
        .right-warp {
          height: 100%;
          padding: 10px;
          display: flex;
          flex-wrap: wrap;
          .cell {
            width: 49%;
            height: 50%;
            display: flex;
            .data {
              margin-left: 50px;
              justify-content: center;
              flex: 1;
              display: flex;
              flex-direction: column;
              align-items: center;
              .value {
                font-size: 24px;
                color: #bff7fe;
              }
              .prop {
                color: #fff;
                font-size: 14px;
              }
            }
          }
        }
        & .cell:nth-child(1) {
          background: url('./assets/image/image5.png') no-repeat;
          background-position-y: center;
          background-size: 48px 48px;
        }
        & .cell:nth-child(2) {
          background: url('./assets/image/image6.png') no-repeat;
          background-position-y: center;
          background-size: 48px 48px;
        }
        & .cell:nth-child(3) {
          background: url('./assets/image/image7.png') no-repeat;
          background-position-y: center;
          background-size: 48px 48px;
        }
        & .cell:nth-child(4) {
          background: url('./assets/image/image8.png') no-repeat;
          background-position-y: center;
          background-size: 48px 48px;
        }
        .div-solt {
          display: flex;
          align-items: center;
          margin-right: 10px;
          padding: 5px 10px;
          .date-btn {
            height: 24px;
            width: 24px;
            background: url('./assets/image/tab.png') 0 0 no-repeat;
            background-size: 100% 100%;
            text-align: center;
            color: #c6e6ff;
            cursor: pointer;
            &:first-child {
              margin-right: 8px;
            }
          }
          .active-tab {
            background: url('./assets/image/tab-pre.png') 0 0 no-repeat;
            background-size: 100% 100%;
            color: #43daeb;
          }
        }
        .asset-trading {
          position: relative;
          z-index: 999;
          .pager {
            top: 20px;
            margin-top: 10px;
          }
        }
        .line-box {
          height: 200px;
          width: 100%;
          margin-top: 20px;
        }
      }
    }
  }
  .tranding {
    font-size: 12px;
    margin-right: 6px;
    margin-top: 15px;
    span {
      cursor: pointer;
      color: #c6e6ff;
      margin-left: 10px;
      display: inline-block;
      border: 1px solid #768cd5;
      border-radius: 5px;
      padding: 1px 6px;
      background: linear-gradient(to bottom, #778bd2, #4b6f87 20px);
    }
    .active {
      color: #43daeb;
      border: 1px solid #768cd5;
      border-radius: 5px;
      padding: 1px 6px;
      background: linear-gradient(to bottom, #2c878c, #35585a 20px);
    }
  }
}
</style>
