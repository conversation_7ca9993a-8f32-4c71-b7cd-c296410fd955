<template>
  <div id="app">
    <header-box></header-box>
    <router-view />
  </div>
</template>

<script>
import headerBox from './header.vue'
export default {
  components: { headerBox },
  created() {
    console.log(this.$router)
    console.log(this.$route)
  }
}
</script>

<style>
#app {
  width: 1920px;
  height: 1080px;
  box-sizing: border-box;
  margin: 0 auto;
  background: url('./assets/image/bj.jpg') 0 0 no-repeat;
  background-size: cover;
}
</style>
