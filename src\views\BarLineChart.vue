<!--
  * @Author: <PERSON><PERSON><PERSON><PERSON>
  * @Date: 2022-11-08 16:54:16
  * @LastEditTime: 2023-01-06 19:51:32
  * @LastEditors: Pengxiao
  * @Description:折线柱状混合图
  * @FilePath: \b-ui\src\views\leaders-view\components\BarLineChart.vue
  * ^-^
 -->

<template>
  <div :id="chartId" class="chart" canvas="3D" />
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', //
  props: {
    xAxisData: {
      type: Array,
      default: () => []
    }, // x轴数据
    xAxisLabelRotate: {
      type: Number,
      default: 0
    }, // x 轴标签旋转角度
    series: {
      type: Array,
      default: () => []
    },
    barMin: {
      type: Number,
      default: 0
    },
    lineMin: {
      type: Number,
      default: 0
    },
    barWidth: {
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  computed: {
    chartId() {
      return `barLine<PERSON>hart-${Math.floor(Math.random() * 10000)}`
    }
  },
  watch: {
    series: {
      deep: true,
      handler: function () {
        this.threeDimensionalMoreBar()
      }
    }
  },
  mounted() {
    this.threeDimensionalMoreBar()
  },
  created() {
    console.log(this.series)
  },
  methods: {
    threeDimensionalMoreBar() {
      console.log(123123121)
      const divId = document.getElementById(this.chartId)
      if (this.myChart) {
        this.myChart.dispose()
      }
      this.myChart = echarts.init(divId)
      // 绘制左侧面
      const wid = 14
      const w1 = Math.sin(Math.PI / 6) * wid // 4
      const w2 = Math.sin(Math.PI / 3) * wid // 6.8
      const snapHeight = wid / 4
      const CubeLeft = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0
        },
        buildPath: function (ctx, shape) {
          // 会canvas的应该都能看得懂，shape是从custom传入的
          const xAxisPoint = shape.xAxisPoint
          const c0 = [shape.x, shape.y - 2]
          const c1 = [shape.x - w2, shape.y - w1 + snapHeight - 2]
          const c2 = [shape.x - w2, xAxisPoint[1] - w1 + snapHeight - 2]
          const c3 = [shape.x, xAxisPoint[1] - 2]
          ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath()
        }
      })
      // 绘制右侧面
      const CubeRight = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0
        },
        buildPath: function (ctx, shape) {
          const xAxisPoint = shape.xAxisPoint
          const c1 = [shape.x, shape.y - 2]
          const c2 = [shape.x, xAxisPoint[1] - 2]
          const c3 = [shape.x + w1, xAxisPoint[1] - w2 + snapHeight - 2]
          const c4 = [shape.x + w1, shape.y - w2 + snapHeight - 2]
          ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
        }
      })
      // 绘制顶面
      const CubeTop = echarts.graphic.extendShape({
        shape: {
          x: 0,
          y: 0
        },
        buildPath: function (ctx, shape) {
          const c1 = [shape.x, shape.y - 2]
          const c2 = [shape.x + w1, shape.y - w2 + snapHeight - 2] // 右点
          const c3 = [shape.x - w2 + w1, shape.y - w1 - w2 + snapHeight * 2 - 2]
          const c4 = [shape.x - w2, shape.y - w1 + snapHeight - 2]
          ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
        }
      })
      // 注册三个面图形
      echarts.graphic.registerShape('CubeLeft', CubeLeft)
      echarts.graphic.registerShape('CubeRight', CubeRight)
      echarts.graphic.registerShape('CubeTop', CubeTop)

      // const VALUE = [{ value: 100, rate: 20 }, { value: 70, rate: 70 }, { value: 200, rate: 90 }, { value: 200, rate: 90 }]
      // const VALUE2 = [{ value: 30, rate: 40 }, { value: 60, rate: 30 }, { value: 100, rate: 80 }, { value: 200, rate: 90 }]
      // const VALUE3 = [{ value: 300, rate: 60 }, { value: 100, rate: 60 }, { value: 150, rate: 70 }, { value: 200, rate: 90 }]
      // const VALUE4 = [{ value: 200, rate: 60 }, { value: 100, rate: 60 }, { value: 20, rate: 70 }, { value: 200, rate: 90 }]
      const VALUE = (this.series[0]?.data || []).map((item) => {
        return { value: item }
      })
      const VALUE2 = (this.series[1]?.data || []).map((item) => {
        return { value: item }
      })
      // const VALUE3 = this.series[2].data.map(item => { return { 'value': item } })
      // const VALUE4 = this.series[3].data.map(item => { return { 'value': item } })
      const option = {
        legend: {
          data: this.series.map((item) => item.name),
          selectedMode: false,
          right: 'center',

          textStyle: {
            color: '#fff'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (data) => {
            const total = data.reduce(function (total, currentValue, currentIndex, arr) {
              return total + currentValue.value
            }, 0)
            let str = `<p style="font-size: 14px;font-weight: 600;color: #fff;text-align: center;margin-bottom: 5px;">总户数：${total}</p>`
            data.reverse().forEach((item) => {
              str += `<p style="margin-bottom: 5px;padding-left: 10px;padding-right: 10px;">${item.marker}${item.seriesName}：${item.value}</p>`
            })
            return str
          }
        },
        grid: {
          top: '30',
          left: '1%',
          right: '1%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.xAxisData,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#344761'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#67d7ff'
            },
            formatter: function (value) {
              // 自定义文字超出部分 ...
              const data = value.split('至')
              const str = data[0] + '至' + data[1]
              return str
            }
          }
        },
        yAxis: {
          min: 10,
          type: 'value',
          nameTextStyle: {
            color: '#fff'
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#344761'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#172749'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#67d7ff'
            }
          }
        },
        series: [
          {
            name: '资产拍照',
            type: 'bar',
            barWidth: '10%',
            renderItem: (params, api) => {
              const location = api.coord([api.value(0), api.value(1)])
              location[0] = location[0] + wid * -2
              const xlocation = api.coord([api.value(0), 0])
              xlocation[0] = xlocation[0] + wid * -2
              return {
                type: 'group',
                children: [
                  {
                    type: 'CubeLeft',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: xlocation
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#2888ff'
                        },
                        {
                          offset: 1,
                          color: '#052e44'
                        }
                      ])
                    }
                  },
                  {
                    type: 'CubeRight',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: xlocation
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#2888ff'
                        },
                        {
                          offset: 1,
                          color: '#052e44'
                        }
                      ])
                    }
                  },
                  {
                    type: 'CubeTop',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: xlocation
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#2888ff'
                        },
                        {
                          offset: 1,
                          color: '#2888ff'
                        }
                      ])
                    }
                  }
                ]
              }
            },
            color: '#2888ff', // '#22c6fc',
            data: VALUE
          },
          {
            name: '资产描边',
            type: 'bar',
            barWidth: '10%',
            renderItem: (params, api) => {
              const location = api.coord([api.value(0), api.value(1)])
              location[0] = location[0] + wid * 0
              const xlocation = api.coord([api.value(0), 0])
              xlocation[0] = xlocation[0] + wid * 0
              return {
                type: 'group',
                children: [
                  {
                    type: 'CubeLeft',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: xlocation
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#2cd1c0'
                        },
                        {
                          offset: 1,
                          color: '#0c3632'
                        }
                      ])
                    }
                  },
                  {
                    type: 'CubeRight',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: xlocation
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#2cd1c0'
                        },
                        {
                          offset: 1,
                          color: '#0c3632'
                        }
                      ])
                    }
                  },
                  {
                    type: 'CubeTop',
                    shape: {
                      api,
                      xValue: api.value(0),
                      yValue: api.value(1),
                      x: location[0],
                      y: location[1],
                      xAxisPoint: xlocation
                    },
                    style: {
                      fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: '#2cd1c0'
                        },
                        {
                          offset: 1,
                          color: '#2cd1c0'
                        }
                      ])
                    }
                  }
                ]
              }
            },
            color: '#2cd1c0', // '#fdc81e',
            data: VALUE2
          }
          // {
          //   name: '提交复核',
          //   type: 'custom',
          //   renderItem: (params, api) => {
          //     const location = api.coord([api.value(0), api.value(1)])
          //     location[0] = location[0] + wid * 2
          //     const xlocation = api.coord([api.value(0), 0])
          //     xlocation[0] = xlocation[0] + wid * 2
          //     return {
          //       type: 'group',
          //       children: [{
          //         type: 'CubeLeft',
          //         shape: {
          //           api,
          //           xValue: api.value(0),
          //           yValue: api.value(1),
          //           x: location[0],
          //           y: location[1],
          //           xAxisPoint: xlocation
          //         },
          //         style: {
          //           fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          //             offset: 0,
          //             color: '#ffa06d'
          //           }, {
          //             offset: 1,
          //             color: '#662400'
          //           }])
          //         }
          //       }, {
          //         type: 'CubeRight',
          //         shape: {
          //           api,
          //           xValue: api.value(0),
          //           yValue: api.value(1),
          //           x: location[0],
          //           y: location[1],
          //           xAxisPoint: xlocation
          //         },
          //         style: {
          //           fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          //             offset: 0,
          //             color: '#ffa06d'
          //           }, {
          //             offset: 1,
          //             color: '#662400'
          //           }])
          //         }
          //       }, {
          //         type: 'CubeTop',
          //         shape: {
          //           api,
          //           xValue: api.value(0),
          //           yValue: api.value(1),
          //           x: location[0],
          //           y: location[1],
          //           xAxisPoint: xlocation
          //         },
          //         style: {
          //           fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          //             offset: 0,
          //             color: '#ffa06d'
          //           }, {
          //             offset: 1,
          //             color: '#ffa06d'
          //           }])
          //         }
          //       }]
          //     }
          //   },
          //   color: '#ffa06d', // '#27e6ab',
          //   data: VALUE3
          // }
          // {
          //   name: '发起清查',
          //   type: 'custom',
          //   renderItem: (params, api) => {
          //     const location = api.coord([api.value(0), api.value(1)])
          //     location[0] = location[0] + wid * 4
          //     const xlocation = api.coord([api.value(0), 0])
          //     xlocation[0] = xlocation[0] + wid * 4
          //     return {
          //       type: 'group',
          //       children: [{
          //         type: 'CubeLeft',
          //         shape: {
          //           api,
          //           xValue: api.value(0),
          //           yValue: api.value(1),
          //           x: location[0],
          //           y: location[1],
          //           xAxisPoint: xlocation
          //         },
          //         style: {
          //           fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          //             offset: 0,
          //             color: '#fa7876'
          //           }, {
          //             offset: 1,
          //             color: '#a20b06'
          //           }])
          //         }
          //       }, {
          //         type: 'CubeRight',
          //         shape: {
          //           api,
          //           xValue: api.value(0),
          //           yValue: api.value(1),
          //           x: location[0],
          //           y: location[1],
          //           xAxisPoint: xlocation
          //         },
          //         style: {
          //           fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          //             offset: 0,
          //             color: '#fa7876'
          //           }, {
          //             offset: 1,
          //             color: '#a20b06'
          //           }])
          //         }
          //       }, {
          //         type: 'CubeTop',
          //         shape: {
          //           api,
          //           xValue: api.value(0),
          //           yValue: api.value(1),
          //           x: location[0],
          //           y: location[1],
          //           xAxisPoint: xlocation
          //         },
          //         style: {
          //           fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          //             offset: 0,
          //             color: '#fa7876'
          //           }, {
          //             offset: 1,
          //             color: '#fa7876'
          //           }])
          //         }
          //       }]
          //     }
          //   },
          //   color: '#fa7876', // '#27e6ab',
          //   data: VALUE4
          // }
        ]
      }
      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.chart {
  margin: 0 auto;
  width: calc(100% - 26px);
  height: 100%;
}
</style>
