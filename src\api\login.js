import request from '@/utils/request'

// 获取key
export function getRsaPublicKey() {
  return request({
    url: '/asymmetricCrypto/getPublicKey',
    method: 'get',
    withCredentials: true
  })
}

// 登录方法
export function login(data) {
  return request({
    url:
    process.env.VUE_APP_IS_BACKEND_MONOLITH === 'true'
      ? '/oauth/token'
      : '/pc/oauth/token',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    withCredentials: true
  })
}

// 发送短信验证码
export function sendSmsCode(data) {
  return request({
    url: '/bid/anon/userApi/sendSmsCode',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    withCredentials: true
  })
}
