<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-08 16:54:16
 * @LastEditTime: 2023-01-05 16:08:26
 * @LastEditors: Pengxiao
 * @Description:通用的仪表盘
 * @FilePath: \b-ui\src\views\leaders-view\components\GaugeChart\GaugeChart.vue
 * ^-^
-->

<template>
  <div :id="chartId" class="chart" />
</template>

<script>
import { init, graphic } from 'echarts'
import resize from './resize.js'
export default {
  mixins: [resize],
  props: {
    title: {
      type: String,
      default: '增长率'
    },
    rate: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      myChart: null
    }
  }, //
  computed: {
    chartId() {
      return `gaugeChart-${Math.floor(Math.random() * 10000)}`
    },
    negativeRate() {
      return this.rate < 0
    } // 是否为负数比率
  },
  watch: {
    rate() {
      if (this.myChart) {
        this.myChart.clear()
      }
      this.setChart()
    }
  },
  mounted() {
    this.setChart()
  },
  methods: {
    setChart() {
      const chart = init(document.getElementById(this.chartId))
      const gradientRight = new graphic.LinearGradient(0, 0, 1, 0, [
        {
          offset: 0,
          color: '#5c53de'
        },
        {
          offset: 1,
          color: '#18c8ff'
        }
      ])
      const gradientLeft = new graphic.LinearGradient(0, 0, 1, 0, [
        {
          offset: 1,
          color: '#5c53de'
        },
        {
          offset: 1,
          color: '#18c8ff'
        }
      ])
      const dataRate = this.rate.toFixed()
      // console.log('dataRate===>', dataRate)
      // console.log('this.rate===>', this.rate)
      const color =
        dataRate >= 0
          ? [[dataRate / 100, gradientRight]]
          : [
              [1 + dataRate / 100, '#233a6c'],
              [1, gradientLeft]
            ]
      const options = {
        // grid: {},
        series: [
          {
            name: '外部刻度仪表盘',
            type: 'gauge',
            radius: '100%',
            min: dataRate < 0 ? 100 : 0, // 最小刻度
            max: dataRate < 0 ? 0 : 100, // 最大刻度
            splitNumber: 8, // 刻度数量
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              show: true,
              roundCap: true,
              lineStyle: {
                width: 0,
                shadowBlur: 0,
                // color: this.negativeRate ? [[1, gradientLeft]] : [[1, gradientRight]]
                color: [
                  [0.1, '#0175ff'],
                  [0.2, '#3491ff'],
                  [0.3, '#67acff'],
                  [0.4, '#9ac8ff'],
                  [0.5, '#cde4ff'],
                  [0.6, '#d2f9ff'],
                  [0.7, '#9ff2ff'],
                  [0.8, '#6cecff'],
                  [0.9, '#39e5ff'],
                  [1, '#06deff']
                ]
              }
            }, // 仪表盘轴线 当比率为负数时 将刻度倒转
            axisTick: {
              show: true,
              splitNumber: 8,
              length: 3,
              lineStyle: {
                width: 3,
                color: 'auto'
              }
            }, // 刻度样式
            splitLine: {
              show: true,
              distance: 6,
              lineStyle: {
                width: 3,
                color: 'auto'
              }
            }, // 分隔线样式
            axisLabel: {
              show: true,
              fontSize: 8,
              color: 'whitesmoke',
              distance: 5,
              formatter: function (v) {
                const map = new Map([
                  [0, 0],
                  [25, 25],
                  [50, 50],
                  [75, 75],
                  [100, 100]
                ])
                const value = map.get(v) || ''
                return value === '' ? '' : dataRate >= 0 ? value : -value
              }
            }, // 刻度标签。
            detail: {
              show: false
            },
            pointer: {
              show: false
            }
          },
          {
            name: '数值显示仪表盘',
            type: 'gauge',
            radius: '70%',
            splitNumber: 0, // 刻度数量
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              show: true,
              lineStyle: {
                width: 15,
                color: [[1, '#233a6c']]
              }
            },
            // 分隔线样式。
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            pointer: {
              show: false
            },
            title: {
              show: false
            },
            // 仪表盘详情，用于显示数据。
            detail: { show: false },
            data: []
          }, // 内部进度条的背景
          {
            type: 'gauge',
            radius: '70%',
            splitNumber: 0, // 刻度数量
            startAngle: 225,
            endAngle: -45,
            axisLine: {
              show: this.negativeRate,
              roundCap: true, // 在两端显示成圆形默认false
              lineStyle: {
                width: 15,
                color
              }
            }, // 负数时 用刻度显示进度 无动画
            // 分隔线样式。
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            axisTick: {
              show: false
            },
            pointer: {
              show: false
            },
            title: {
              show: true,
              offsetCenter: [0, '110%'], // x, y，单位px
              textStyle: {
                color: '#ffffff',
                fontSize: 16
              }
            },
            // 仪表盘详情，用于显示数据。
            detail: {
              show: true,
              offsetCenter: [0, 0],
              formatter: function (params) {
                const unit = '%'
                return `{value| ${params}}{unit| ${unit}}`
              },
              textStyle: {
                rich: {
                  value: {
                    fontSize: 24,
                    fontWeight: 550,
                    color: '#04ebf3'
                  },
                  unit: {
                    fontSize: 14,
                    color: '#04ebf3'
                  }
                }
              }
            },
            progress: {
              show: !this.negativeRate, // 正数时 用进度条显示 有动画
              roundCap: true, // 在两端显示成圆形默认false
              width: 15,
              itemStyle: {
                color: gradientRight,
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                opacity: 1
              }
            },
            data: [{ name: this.title, value: this.rate.toFixed(2) }]
          } // 内部进度条圆环
        ]
      }
      chart.setOption(options)
      this.myChart = chart
    }
  }
}
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
