/* eslint-disable camelcase */
'use strict'
const path = require('path')

// const defaultSettings = require('./src/settings.js');
// const UglifyJsPlugin = require('uglifyjs-webpack-plugin') // 去掉注释
// const CompressionWebpackPlugin = require('compression-webpack-plugin') // 开启压缩

function resolve(dir) {
  return path.join(__dirname, dir)
}

// const name = process.env.VUE_APP_TITLE || '智慧乡村管理系统'; // 网页标题

// const isProduction = process.env.NODE_ENV === 'production'
const port = process.env.port || process.env.npm_config_port || 80 // 端口

// vue.config.js 配置说明
// 官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  publicPath: './',
  pwa: {
    iconPaths: {
      favicon32: 'favicon.ico',
      favicon16: 'favicon.ico',
      appleTouchIcon: 'favicon.ico',
      maskIcon: 'favicon.ico',
      msTileImage: 'favicon.ico'
    }
  },

  // publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'fs-bigscreen',
  assetsDir: 'static', // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  lintOnSave: process.env.NODE_ENV === 'development', // 开发环境是否开启eslint保存检测，有效值：true | false | 'error'
  productionSourceMap: false, // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  // webpack-dev-server 相关配置
  devServer: {
    port: port,
    open: true,
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: process.env.VUE_APP_BASE_URL,
        changeOrigin: true,
        logLevel: 'debug', // api重定向是否打印日志
        pathRewrite: {
          // ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    }
  },
  chainWebpack(config) {
    config.resolve.alias.set('@', resolve('src'))
    // config.plugins.delete('preload') // TODO: need test 移动端由于带宽限制需要禁止预加载 pc端不用
    // config.plugins.delete('prefetch') // TODO: need test

    // set svg-sprite-loader
    config.module.rule('ico').exclude.add(resolve('src/assets/icons')).end()
    config.module
      .rule('icons')
      .test(/\.ico$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // webpack 会默认给commonChunk打进chunk-vendors，所以需要对webpack的配置进行delete
    config.optimization.delete('splitChunks')

    config.when(process.env.NODE_ENV !== 'development', (config) => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/
          }
        ])
        .end()
      config.optimization.runtimeChunk('single')
    })
  },

  configureWebpack: (config) => {
    const plugins = []

    // if (isProduction) {
    // 开启分离js
    config.optimization = {
      runtimeChunk: 'single',
      splitChunks: {
        chunks: 'initial',
        maxInitialRequests: Infinity,
        // Size: 1000 * 60,
        // maxSize: 1000 * 30,
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/
            // name(module) {
            //   // 排除node_modules 然后吧 @ 替换为空 ,考虑到服务器的兼容
            //   const packageName = module.context.match(
            //     /[\\/]node_modules[\\/](.*?)([\\/]|$)/
            //   )[1]
            //   return `npm.${packageName.replace('@', '')}`
            // }
          }
        }
      }
    }

    // 取消webpack警告的性能提示
    config.performance = {
      hints: 'warning',
      // 入口起点的最大体积
      maxEntrypointSize: 1000 * 500,
      // 生成文件的最大体积
      maxAssetSize: 1000 * 1000,
      // 只给出 js 文件的性能提示
      assetFilter: function (assetFilename) {
        return assetFilename.endsWith('.js')
      }
    }
    // }

    return { plugins }
  }
}
