<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-17 19:44:21
 * @LastEditTime: 2023-01-12 19:11:03
 * @LastEditors: Pengxiao
 * @Description:
 * @FilePath: \b-ui\src\views\leaders-view\components\PieChart\index.vue
 * ^-^
-->
<template>
  <div ref="peiChart" class="chart" />
</template>

<script>
import { init } from 'echarts'
import { getOptions } from './chart.js'

// const color = ['#FFFF00', '#1E90FF', '#66CDAA', '#BA55D3', '#FF8C69']
export default {
  name: 'Pie<PERSON>hart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    wrapLegend: {
      type: Boolean,
      default: false
    }, // 是否折叠显示legend
    showValue: {
      type: Boolean,
      default: true
    }, // 是否显示legend 中的value
    internalDiameterRatio: {
      type: Number,
      default: 0.6
    }, // 透明空心所占比率0-1
    opacity: {
      type: Number,
      default: 0.8
    }, // 不透明度 0-1
    unit: {
      type: String,
      default: ''
    } // 数据的单位
  },
  data() {
    return {
      color: ['#0265CA', '#1E90FF', '#66CDAA', '#BA55D3', '#FF8C69'],
      myChart: null,
      option: {}
    }
  },
  computed: {
    total() {
      const total = this.data.reduce((total, { value = 0 }) => total + value, 0)
      return total
    }
  },
  watch: {
    data: {
      deep: true,
      handler(newVal) {
        if (this.myChart) {
          this.myChart.clear()
        }
        this.setChart()
      }
    }
  },
  mounted() {
    this.setChart()
    // 根据窗口变化自动调节图表大小
    const that = this
    window.onresize = function() {
      that.changeSize()
    }
  },
  methods: {
    // 图表初始化
    setChart() {
      const chart = init(this.$refs.peiChart)
      // 传入数据生成 option, 构建3d饼状图, 参数工具文件已经备注的很详细
      let data = this.data
      if (this.unit === '万元') {
        data = this.data.map(({ value, ...rest }) => ({
          ...rest,
          value: value / 10000
        }))
      }
      if (this.unit === '亿元') {
        data = this.data.map(({ value, ...rest }) => ({
          ...rest,
          value: value / 100000000
        }))
      }
      this.option = getOptions(
        data,
        this.unit,
        this.showLegend,
        this.internalDiameterRatio,
        this.opacity,
        this.wrapLegend,
        this.showValue
      )
      this.setLabel()
      chart.setOption(this.option)
      this.myChart = chart
      // bindMouseEvent(this.myChart, this.option)
    },
    // label 在3d图上添加一个2d图然后隐藏2d图只显示label
    setLabel() {
      if (this.showLabel) {
        this.option.series.push({
          name: 'pie2d',
          type: 'pie',
          tooltip: {
            show: false
          },
          top: -20,
          // labelLine: {
          //   length: 20,
          //   lineStyle: {
          //     color: '#ffffff',
          //     width: 1.5
          //   }
          // },
          startAngle: 360, // 起始角度，支持范围[0, 360]。 //重要
          clockwise: false, // 饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
          radius: [`${40 * this.internalDiameterRatio}%`, '40%'],
          center: ['50%', '50%'],
          data: this.data,
          itemStyle: {
            opacity: 0
          },
          avoidLabelOverlap: true // 防止标签重叠
        })
      }
    },
    // 自适应宽高
    changeSize() {
      this.myChart.resize()
    }
  }
}
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
