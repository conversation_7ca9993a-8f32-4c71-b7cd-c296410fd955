import request from '@/utils/request'
// 全页面数量数据
export function baseData (data) {
  return request({
    url: '/warehouse/da/asset/baseData',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 区域资产数量排名
export function getAssetRankList (data) {
  return request({
    url: '/warehouse/da/asset/getAssetRankList',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 区域资产数量排名-弹窗
export function getTotalAssetRankList (data) {
  return request({
    url: '/warehouse/da/asset/getTotalAssetRankList',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 图形数据
export function chartData (data) {
  return request({
    url: '/warehouse/da/asset/chartData',
    method: 'post',
    data,
    withCredentials: true
  })
}
// 年份
export function yearList (data) {
  return request({
    url: '/warehouse/da/asset/yearList',
    method: 'post',
    data,
    withCredentials: true
  })
}
/**
 * @name: 地区树
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
export function userTreeData (query) {
  return request({
    url: '/system/area/tree',
    method: 'get',
    params: query
  })
}

