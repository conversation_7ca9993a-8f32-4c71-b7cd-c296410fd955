/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-22 14:43:14
 * @LastEditTime: 2022-11-22 14:57:38
 * @LastEditors: Pengxiao
 * @Description:
 * @FilePath: \b-ui\src\views\leaders-view\components\resize.js
 * ^-^
 */
export default {
  mounted() {
    if (this.myChart) {
      this.$nextTick(() => {
        this.myChart.resize()
      })
      window.onresize = function() {
        this.myChart.resize()
      }
    }
  }
}
