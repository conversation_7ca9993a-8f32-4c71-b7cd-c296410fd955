<!--
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-08-18 13:44:33
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-01 17:18:44
-->
<template>
  <div id="barstackEcharts" class="barstack-box"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  components: {},
  props: {
    echartData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'DEAL_PROJECT_NUM'
    }
  },
  data() {
    return {}
  },
  watch: {
    echartData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.$nextTick(() => {
          this.initEcharts()
        })
      }
    }
  },
  created() {},
  methods: {
    initEcharts() {
      const unit = {
        DEAL_PROJECT_NUM: '宗',
        NO_PREMIUM_DEAL_PROJECT_NUM: '宗',
        PROJECT_AMOUNT: '万元',
        PREMIUM_RATE: '%'
      }
      const barstackEcharts = document.getElementById('barstackEcharts')
      this.myChart = echarts.init(barstackEcharts)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          position: (point, params, dom, rect, size) => {
            const options = {
              point,
              params,
              dom,
              rect,
              size
            }
            return this.position(options)
          },
          formatter: (params) => {
            let str = ''
            for (const key in params) {
              if (['PREMIUM_RATE', 'PROJECT_AMOUNT'].includes(this.type)) {
                str += `${params[key].axisValueLabel}<br />${params[key].marker}${params[key].seriesName}&nbsp;&nbsp;&nbsp;&nbsp;${
                  params[key].value.toFixed(2) + unit[this.type]
                }<br />`
              } else {
                str += `${params[key].axisValueLabel}<br />${params[key].marker}${params[key].seriesName}&nbsp;&nbsp;&nbsp;&nbsp;${params[key].value + unit[this.type]}<br />`
              }
            }
            return str
          }
        },
        color: ['#06e9ff', '#e4c748'],
        legend: {
          top: '5%',
          textStyle: {
            color: '#fff',
            align: 'right'
          },
          itemWidth: 15
        },
        grid: {
          left: '15%',
          bottom: '75'
        },
        xAxis: [
          {
            type: 'category',
            data: ['资源性资产', '房屋建筑资产', '非房屋建筑资产'],
            axisLabel: {
              textStyle: {
                color: '#65ABE7',
                fontSize: 14
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisPointer: {
              type: 'none'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#65ABE7',
                fontSize: 14
              }
            },
            splitLine: {
              lineStyle: {
                width: 2,
                color: '#123d72'
              }
            }
          }
        ],
        series: [
          {
            name: '公开协商',
            type: 'bar',
            stack: 'all',
            emphasis: {
              focus: 'series'
            },
            data: this.echartData.data2,
            barWidth: 10,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#06e9ff'
                  },
                  {
                    offset: 0.5,
                    color: '#0592fb'
                  }
                ]
              }
            }
          },
          {
            name: '网上竞投',
            type: 'bar',
            stack: 'all',
            emphasis: {
              focus: 'series'
            },
            data: this.echartData.data,
            barWidth: 10,
            itemStyle: {
              borderRadius: [5, 5, 0, 0],
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#e6df6a'
                  },
                  {
                    offset: 0.5,
                    color: '#e2b72a'
                  }
                ]
              }
            }
          }
        ]
      }
      this.myChart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.barstack-box {
  margin: 0 auto;
  width: 100%;
  height: 100%;
}
</style>
