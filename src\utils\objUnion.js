/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-08-22 14:59:19
 * @LastEditors: Andy
 * @LastEditTime: 2023-08-22 15:15:04
 */
export default function objUnion(obj, obj2) {
  for (const key in obj) {
    if (Object.hasOwnProperty.call(obj, key)) {
      for (const key2 in obj2) {
        if (Object.hasOwnProperty.call(obj2, key2)) {
          if (key === key2) {
            obj[key] = JSON.parse(JSON.stringify(obj2[key2]))
          }
        }
      }
    }
  }
  return obj
}
