/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-07-07 09:20:49
 * @LastEditors: Andy
 * @LastEditTime: 2023-08-30 11:34:05
 */
import Vue from 'vue'
import VueRouter from 'vue-router'

import Home from '../Home.vue'
import Asset from '../Asset.vue'
import Inventory from '../Inventory.vue'
import Trading from '../Trading.vue'
import Financial from '../Financial.vue'
import Shares from '../Shares.vue'

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}

// const scrollBehavior = (to, from, savedPosition) => {
//   if (savedPosition) {
//   // savedPosition仅可用于popstate导航。
//     return savedPosition
//   } else {
//     const position = {}
//     // 新的导航
//     // 通过返回选择器滚动到锚点
//     if (to.hash) {
//       position.selector = to.hash
//     }
//     // 检查匹配的路由配置是否有需要滚动到顶部的元素
//     if (to.matched.some(m => m.meta.scrollToTop)) {
//       // cords will be used if no selector is provided,
//       // or if the selector didn't match any element.
//       position.x = 0
//       position.y = 0
//     }
//     // 如果返回的位置是错误的或是空的对象，
//     // 保留当前滚动位置
//     return position
//   }
// }

const routes = [
  {
    path: '/',
    component: Asset,
    meta: {
      title: '佛山市农村集体资产“三资”智慧云平台资产情况'
    }
  },
  {
    path: '/trading',
    component: Trading,
    meta: {
      title: '佛山市农村集体资产“三资”智慧云平台交易情况'
    }
  },
  {
    path: '/financial',
    component: Financial,
    meta: {
      title: '佛山市农村集体资产“三资”智慧云平台财务情况'
    }
  },
  {
    path: '/inventory',
    component: Inventory,
    meta: {
      title: '佛山市农村集体资产清产核资工作动态'
    }
  },
  {
    path: '/shares',
    component: Shares,
    meta: {
      title: '佛山市农村集体资产“三资”智慧云平台成员情况'
    }
  }
]

Vue.use(VueRouter)

const router = new VueRouter({
  base: 'fs-bigscreen',
  mode: '',
  routes
})

router.beforeEach(async (to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})
export default router
