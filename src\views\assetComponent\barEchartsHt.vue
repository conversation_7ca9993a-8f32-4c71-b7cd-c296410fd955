<template>
  <div id="barEchartsHt" class="barBox"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  props: {
    contractData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'bar'
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    contractData: {
      deep: true,
      handler(newVal) {
        this.initializationEcharts()
      }
    }
  },
  created() {},
  mounted() {
    this.initializationEcharts()
  },
  methods: {
    initializationEcharts() {
      console.log(this.type)
      const barEchartsHt = document.getElementById('barEchartsHt')
      this.myChart = echarts.init(barEchartsHt)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.contractData.x || [],
            axisTick: {
              alignWithLabel: false,
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#66d6ff',
                fontSize: 14
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#2e9eff',
                fontSize: 14
              },
              formatter: this.type == 'bar' ? '{value} 万元' : '{value}'
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#123d72'
              }
            }
          }
        ],
        series: [
          {
            name: this.contractData.name,
            type: this.type,
            barGap: 0,
            data: this.contractData.y || [],
            barWidth: 12,
            itemStyle: {
              normal: {
                // 这里设置柱形图圆角 [左上角，右上角，右下角，左下角]
                barBorderRadius: [12, 12, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    // 四个数字分别对应 数组中颜色的开始位置，分别为 右，下，左，上。例如（1,0,0,0 ）代表从右边开始渐
                    // 变。offset取值为0~1，0代表开始时的颜色，1代表结束时的颜色，柱子表现为这两种颜色的渐变。
                    offset: 0,
                    color: '#06f2ff'
                  },
                  {
                    offset: 1,
                    color: '#0688f8'
                  }
                ])
              }
            }
          }
        ]
      }
      this.myChart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.barBox {
  height: 180px;
}
</style>
