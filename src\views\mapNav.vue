<template>
  <div id="sh-map" class="sh-map">
    <!-- <el-button
      v-show="currentArea !== '4406'"
      class="back"
      type="text"
      @click="clickFoshan"
    >返回全市<i class="el-icon-arrow-right" />
    </el-button> -->
    <img v-show="currentArea !== '4406'" class="back" src="../assets/image/back.png" alt="" @click="clickFoshan" />

    <div id="china_map" />
  </div>
</template>

<script>
import * as myEchart from 'echarts'
import axios from 'axios'
import 'echarts-gl'

export default {
  name: 'MapNav',
  components: {},
  props: {
    areaCode: {
      type: String,
      default: ''
    }
  },
  data() {
    this.regionsEnum = [
      {
        name: '三水区',
        code: 'D440607',
        color: '#759f1d'
      },
      {
        name: '南海区',
        code: 'D440605',
        color: '#e7af15'
      },
      {
        name: '禅城区',
        code: 'D440604',
        color: '#b05621'
      },
      {
        name: '顺德区',
        code: 'D440606',
        color: '#8c005f'
      },
      {
        name: '高明区',
        code: 'D440608',
        color: '#017673'
      }
    ]
    this.sanshuiArray = []
    return {
      areaMap: {
        4406: require('./mapData/D4406.json'),
        440604: require('./mapData/D440604.json'),
        440607: require('./mapData/D440607.json'),
        440605: require('./mapData/D440605.json'),
        440608: require('./mapData/D440608.json'),
        440606: require('./mapData/D440606.json'),
        440604010: require('./mapData/D440604010.json'),
        440604011: require('./mapData/D440604011.json'),
        440604012: require('./mapData/D440604012.json'),
        440604100: require('./mapData/D440604100.json'),
        440605011: require('./mapData/D440605011.json'),
        440605121: require('./mapData/D440605121.json'),
        440605122: require('./mapData/D440605122.json'),
        440605123: require('./mapData/D440605123.json'),
        440605124: require('./mapData/D440605124.json'),
        440605125: require('./mapData/D440605125.json'),
        440605126: require('./mapData/D440605126.json'),
        440606101: require('./mapData/D440606101.json'),
        440606102: require('./mapData/D440606102.json'),
        440606103: require('./mapData/D440606103.json'),
        440606104: require('./mapData/D440606104.json'),
        440606105: require('./mapData/D440606105.json'),
        440606106: require('./mapData/D440606106.json'),
        440606107: require('./mapData/D440606107.json'),
        440606108: require('./mapData/D440606108.json'),
        440606109: require('./mapData/D440606109.json'),
        440606110: require('./mapData/D440606110.json'),
        440607001: require('./mapData/D440607001.json'),
        440607101: require('./mapData/D440607101.json'),
        440607103: require('./mapData/D440607103.json'),
        440607104: require('./mapData/D440607104.json'),
        440607105: require('./mapData/D440607105.json'),
        440607106: require('./mapData/D440607106.json'),
        440607107: require('./mapData/D440607107.json'),
        440608004: require('./mapData/D440608004.json'),
        440608106: require('./mapData/D440608106.json'),
        440608107: require('./mapData/D440608107.json'),
        440608108: require('./mapData/D440608108.json')
        // '440604': require('./mapData/chancheng.json'),
        // '440607': require('./mapData/sanshui.json'),
        // '440605': require('./mapData/nanhai.json'),
        // '440608': require('./mapData/gaoming.json'),
        // '440606': require('./mapData/shunde.json')
      },
      myChart: null,
      currentArea: '4406',
      area: '4406'
    }
  },
  watch: {
    areaCode: {
      handler(newVal, oldVal) {
        if (newVal.length > 10) return
        this.currentArea = newVal.slice(1)
        if (this.currentArea !== '4406') {
          this.initEchartMap()
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initEchartMap()
    // if (localStorage.currentArea) {
    //   this.currentArea = localStorage.currentArea
    // }
    this.renderMap()
  },
  methods: {
    renderCurAreaText() {
      if (this.currentArea === '4406') return ''
      const cur = this.regionsEnum.find((item) => item.code === this.currentArea)
      return cur?.name ?? ''
    },
    // 渲染地图
    renderMap() {
      const options = {
        animation: true,
        geo: [
          {
            map: this.currentArea,
            top: '40',
            // left: '85',
            zlevel: 0,
            itemStyle: {
              color: '#90FFFE', // 背景
              borderWidth: '1' // 边框宽度
              // borderColor: 'red' // 边框颜色
            },
            zoom: 1
          },
          {
            show: true,
            map: this.currentArea,
            zlevel: 1,
            top: '36',
            // left: '80',
            label: {
              normal: {
                color: '#fff',
                fontSize: 16,
                align: 'center',
                // show: true
                show: this.areaCode.length < 9
              },
              emphasis: {
                show: true,
                color: '#000',
                fontSize: 14,
                textBorderWidth: '1',
                textBorderColor: '#fff'
              }
            },
            zoom: 1,
            regions: (this.regionsEnum || []).map((item) => ({
              name: item.name,
              color: ['#ee5452', '#ea9c20'],
              code: item.code, // 跟点击有关
              label: {
                normal: {
                  color: item.code === this.currentArea ? '#000' : '#fff'
                }
              },
              itemStyle: {
                normal: {
                  // areaColor: item.color,
                  borderWidth: item.code === this.currentArea ? 2 : 1,
                  areaColor: {
                    type: 'linear-gradient',
                    x: 0,
                    y: 1000,
                    x2: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0.5,
                        color: item.color // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: item.color // 100% 处的颜色
                      }
                    ],
                    global: true // 缺省为 false
                  },
                  borderColor: item.code === this.currentArea ? 'lightskyblue' : '#C0F5FF',
                  shadowColor: '#92fffd', // 阴影颜色
                  shadowOffsetX: 0 // 阴影偏移量
                  // shadowOffsetY: 5 // 阴影偏移量
                },
                emphasis: {
                  areaColor: 'yellow'
                  // borderWidth: 2, // 区域边框宽度
                  // borderColor: 'lightskyblue' // 区域边框颜色
                }
              }
            }))
          }
        ]
      }
      this.myChart.setOption(options, true)
    },

    // 初始化地图
    initEchartMap() {
      const mapDiv = document.getElementById('china_map')
      const shMap = document.getElementById('sh-map')
      myEchart.registerMap(this.currentArea, this.areaMap[this.currentArea])
      this.resizeMyChartContainer(mapDiv, shMap)
      this.myChart = myEchart.init(mapDiv)
      const arr = []
      this.areaMap[this.currentArea].features.forEach((item) => {
        const obj = {
          name: item.properties.name,
          code: item.properties.code,
          area: item.properties.area,
          color: item.properties.color
        }
        arr.push(obj)
      })
      this.regionsEnum = arr
      this.renderMap()
      window.onresize = () => {
        this.resizeMyChartContainer(mapDiv, shMap)
        this.myChart.resize()
      }

      // 注册点击事件
      this.myChartClick()
    },
    // 地图容器宽度
    resizeMyChartContainer(mapDiv, shMap) {
      // mapDiv.style.width = shMap.offsetWidth + 'px'
    },
    clickFoshan() {
      if (this.currentArea.length == 6) {
        this.area = this.currentArea.slice(0, this.currentArea.length - 2)
      }
      if (this.currentArea.length == 9) {
        this.area = this.currentArea.slice(0, this.currentArea.length - 3)
      }
      if (this.currentArea.length == 12) {
        this.area = this.currentArea.slice(0, this.currentArea.length - 3)
      }
      localStorage.setItem('currentArea', this.area)
      this.currentArea = '4406'
      this.initEchartMap()
      this.$emit('gotopath', this.area)
      this.renderMap()
    },
    myChartClick() {
      this.myChart.off('click')
      this.myChart.on('click', (params) => {
        console.log(params.region.code)
        const code = params.region.code.substring(1, params.region.code.length)
        // 获取当前点击区域名称code
        // this.currentArea = code
        if (code.length == 9) {
          this.currentArea = code.slice(0, 6)
        } else {
          this.currentArea = code
        }
        localStorage.setItem('currentArea', code)
        if (code.length < 10) {
          // 初始化
          this.initEchartMap()
        }
        this.$emit('gotopath', code)
      })
    }
  }
}
</script>

<style lang="scss">
.sh-map {
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 450px;
  min-height: 300px;
  .back {
    width: 40px;
    height: 40px;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    cursor: pointer;
  }

  #china_map {
    flex: 1;
    position: relative;
    z-index: 2;
  }
}
</style>
