<template>
  <div class="container">
    <div class="left-container">
      <div class="top block-box">
        <div class="block-title">重点资产类型成交分析</div>
        <div class="block-body">
          <div class="rank-header">
            <div class="header-item" style="flex: 1">类型</div>
            <div class="header-item" style="flex: 1">成交率</div>
            <div class="header-item" style="flex: 1">溢价率</div>
            <div class="header-item" style="flex: 1.3;text-align: center">综合成交价</div>
          </div>
          <div v-for="(item, key) in data1_1" :key="key" class="rank-item" :class="key % 2 == 1 ? 'stripe' : ''">
            <div style="flex: 1; font-weight: normal">
              <img :src="item.imgUrl" alt="" width="30px" height="30px" style="margin-right: 10px" />
              {{ item.type }}
            </div>
            <div style="flex: 1">{{ Number(item.cjl).toFixed(2) + '%' }}</div>
            <div style="flex: 1">{{ Number(item.yjl).toFixed(2) + '%' }}</div>
            <div style="flex: 1.3; justify-content: flex-start">
              <span>{{ Number(item.dealPrice).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
              <span style="font-size: 12px; font-weight: normal">{{ '&nbsp;' + item.unit }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="center block-box">
        <div class="block-title">交易模式成交分析</div>
        <div id="chart1_2" class="block-body"></div>
      </div>

      <div class="bottom block-box" @mouseenter.stop="handleMouseEnter('1_3')" @mouseleave.stop="handleMouseLeave('1_3')">
        <div class="block-title">成交趋势分析</div>
        <div class="switch-box">
          <span v-for="item in dateOptions" :key="item.id" class="radio-btn" :class="switch2 == item.id ? 'selected' : ''" @click="handleChangeSwitch2(item)">
            {{ item.text }}
          </span>
        </div>
        <div id="chart1_3" class="block-body p0"></div>
      </div>
    </div>

    <div class="center-container">
      <div class="top-container">
        <div class="tooltip">
          <div class="info">
            <div class="area-org">
              <img class="area-img" src="@/assets/image/icon1.png" alt="" />
              <span class="area-title" style="font-size: 18px">当前地区：</span>
              <span class="area" style="font-size: 24px">| {{ currentArea }} |</span>
            </div>

            <div class="area-time">
              <img class="area-img" src="./assets/image/date.png" alt="" />
              <span class="area-title" style="font-size: 18px">统计截止时间：</span>
              <span class="area" style="font-size: 18px">{{ currentDate }}</span>
            </div>
          </div>
          <div class="info" style="margin-top: 20px">
            <div></div>
            <div style="display: flex">
              <el-select v-model="queryParams.year" style="width: 100px" placeholder="" :popper-append-to-body="false" @change="handleDateChange">
                <el-option v-for="item in yearOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
              <el-select
                v-if="switch1 == '2'"
                v-model="queryParams.quarter"
                style="width: 120px; margin-left: 10px"
                placeholder=""
                :popper-append-to-body="false"
                @change="handleDateChange"
              >
                <el-option v-for="item in quarterOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
              <el-select
                v-if="switch1 == '3'"
                v-model="queryParams.month"
                style="width: 100px; margin-left: 10px"
                placeholder=""
                :popper-append-to-body="false"
                @change="handleDateChange"
              >
                <el-option v-for="item in monthOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>

              <span v-for="item in dateOptions" :key="item.id" class="radio-btn" :class="switch1 == item.id ? 'selected' : ''" @click="handleChangeSwitch1(item)">
                {{ item.text }}
              </span>
            </div>
          </div>
        </div>
        <mapNav class="map" :area-code="areaCode" @gotopath="gotopath" />
      </div>
      <div class="bottom-container">
        <div class="top">
          <div>成交总金额</div>
          <number-flash-copy :number="formatMoney(data2_1.totalAmount).num" :precision="2" />
          <div>{{formatMoney(data2_1.totalAmount).unit}}</div>
        </div>
        <div class="bottom">
          <div class="block-box">
            <div class="block-body">
              <img class="img" src="@/assets/image/trading/icon6.png" alt="" />
              <div class="detail-info">
                <div class="value">
                  {{ data2_1.onlineProjectNum ? Number(data2_1.onlineProjectNum).toLocaleString() : data2_1.onlineProjectNum }}
                  <font style="font-size: 16px">宗</font>
                </div>
                <div class="title">网上交易项目数</div>
              </div>
            </div>
          </div>
          <div class="block-box">
            <div class="block-body">
              <img class="img" src="@/assets/image/trading/icon7.png" alt="" />
              <div class="detail-info">
                <div class="value">
                  {{ data2_1.dealProjectNum ? Number(data2_1.dealProjectNum).toLocaleString() : data2_1.dealProjectNum }}
                  <font style="font-size: 16px">宗</font>
                </div>
                <div class="title">成交项目数</div>
              </div>
            </div>
          </div>
          <div class="block-box">
            <div class="block-body">
              <img class="img" src="@/assets/image/trading/icon8.png" alt="" />
              <div class="detail-info">
                <div class="value">{{ data2_1.cjl ? Number(data2_1.cjl).toFixed(2) : data2_1.cjl }}%</div>
                <div class="title">成交率</div>
              </div>
            </div>
          </div>
          <div class="block-box">
            <div class="block-body">
              <img class="img" src="@/assets/image/trading/icon9.png" alt="" />
              <div class="detail-info">
                <div class="value">{{ data2_1.yjl ? Number(data2_1.yjl).toFixed(2) : data2_1.yjl }}%</div>
                <div class="title">溢价率</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-container">
      <div class="top block-box" @mouseenter.stop="handleMouseEnter('3_1')" @mouseleave.stop="handleMouseLeave('3_1')">
        <div class="block-title">镇街交易之星</div>
        <div class="switch-box">
          <span v-for="item in typeOptions" :key="item.id" class="radio-btn" :class="switch3 == item.id ? 'selected' : ''" @click="handleChangeSwitch3(item)">
            {{ item.text }}
          </span>
        </div>
        <div id="rank-list-box" class="block-body">
          <div class="rank-header">
            <div class="header-item" style="width: 80px">名次</div>
            <div class="header-item" style="flex: 1">地区机构</div>
            <div class="header-item" style="flex: 1">{{ switch3 == '2' ? '成交项目数' : '成交总金额' }}</div>
          </div>
          <vue-seamless-scroll
            :data="data3_1"
            class="rank-list"
            :class-option="{
              step: 0.5,
              hover: true,
              hoverStop: true,
              scrollContainer: true,
              limitMoveNum: 7
            }"
            :style="{ height: rankBoxHeight + 'px', maxHeight: rankBoxHeight + 'px' }"
          >
            <ul ref="_rankListRef">
              <li
                v-for="(item, index) in switch3 == '2' ? setData.data3_1_1 : setData.data3_1_2"
                :key="switch3 == '2' ? item.areaName + item.value : item.areaName + item.sumDealAmount"
                class="rank-item"
                :class="index === 0 ? 'level1' : index <= 2 ? 'level2' : 'level3'"
              >
                <span class="rank-order">{{ index + 1 }}</span>
                <span class="area-name">{{ item.areaName }}</span>
                <span v-if="switch3 == '2'" class="value">{{ Number(item.value).toLocaleString() + '宗' }}</span>
                <span v-else class="value">{{ formateMoney(item.sumDealAmount) }}</span>
              </li>
            </ul>
          </vue-seamless-scroll>
        </div>
      </div>

      <div class="center block-box">
        <div class="block-title">访问流量分析</div>
        <el-button class="more-btn" type="text" @click="handleViewDetail">更多 ></el-button>

        <div class="block-body">
          <div class="left">
            <div class="info">
              <img class="img" src="@/assets/image/trading/icon10.png" alt="" />
              <div class="detail-info">
                <div class="title">总注册人数</div>
                <div class="value" style="font-size: 12px">
                  <font style="font-size: 24px; font-weight: bold">{{ data3_2.totalRegistry ? Number(data3_2.totalRegistry).toLocaleString() : 0 }}</font>
                  人
                </div>
              </div>
            </div>
            <div class="info">
              <img class="img" src="@/assets/image/trading/icon11.png" alt="" />
              <div class="detail-info">
                <div class="title">日平均访问人数</div>
                <div class="value" style="font-size: 12px">
                  <font style="font-size: 24px; font-weight: bold">{{ data3_2.averageAccessDay ? Number(data3_2.averageAccessDay).toLocaleString() : 0 }}</font>
                  人次
                </div>
              </div>
            </div>
          </div>
          <div id="chart3_2" class="right"></div>
        </div>
      </div>

      <div class="bottom block-box" @mouseenter.stop="handleMouseEnter('3_3')" @mouseleave.stop="handleMouseLeave('3_3')">
        <div class="block-title">交易保证金入账趋势分析</div>
        <div class="switch-box">
          <span v-for="item in dateOptions" :key="item.id" class="radio-btn" :class="switch4 == item.id ? 'selected' : ''" @click="handleChangeSwitch4(item)">
            {{ item.text }}
          </span>
        </div>
        <div id="chart3_3" class="block-body p0"></div>
      </div>
    </div>

    <el-dialog :visible.sync="dialogVisible" custom-class="custom-dialog" :show-close="false" :append-to-body="false">
      <div slot="title">
        <div class="title">流量访问统计</div>
        <img class="close-btn" src="./assets/image/trading/close.png" alt="" @click.stop="dialogVisible = false" />
      </div>

      <el-form inline>
        <el-form-item label="访问日期">
          <el-date-picker
            v-model="detailQueryParams.date"
            type="daterange"
            range-separator="至"
            start-placeholder=""
            end-placeholder=""
            value-format="yyyy-MM-dd"
            :append-to-body="false"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="统计级别">
          <el-select v-model="detailQueryParams.level" style="width: 60px" :popper-append-to-body="false">
            <el-option label="年" value="1"></el-option>
            <el-option label="月" value="2"></el-option>
            <el-option label="日" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="search-btn" @click="handleSearchDetail">搜索</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="detailLoading"
        :data="detailList"
        :height="tableHeight"
        stripe
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.3)"
      >
        <el-table-column label="年份" prop="year" align="center" header-align="center">
          <template slot-scope="scope">
            {{ scope.row.year + '年' }}
          </template>
        </el-table-column>
        <el-table-column label="月份" prop="month" align="center" header-align="center">
          <template slot-scope="scope">
            {{ scope.row.month ? scope.row.year + '年' + (scope.row.month + '').padStart(2, '0') + '月' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="日期" prop="date" align="center" header-align="center" min-width="120px">
          <template slot-scope="scope">
            {{ scope.row.date ? scope.row.year + '年' + (scope.row.month + '').padStart(2, '0') + '月' + (scope.row.date + '').padStart(2, '0') + '日' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="总访问人数" prop="total" align="center" header-align="center">
          <template slot-scope="scope">
            {{ scope.row.total == null ? '' : Number(scope.row.total).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="网站访问人数" prop="web" align="center" header-align="center" min-width="100px">
          <template slot-scope="scope">
            {{ scope.row.web == null ? '' : Number(scope.row.web).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="小程序访问人数" prop="app" align="center" header-align="center" min-width="100px">
          <template slot-scope="scope">
            {{ scope.row.app == null ? '' : Number(scope.row.app).toLocaleString() }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import vueSeamlessScroll from 'vue-seamless-scroll'
import mapNav from './views/mapNav.vue'
import NumberFlashCopy from '@/views/NumberFlashCopy'
import { changeMoney, getLastFormatDate } from '@/utils/common'
import { getProjectInfos, getVistisStatistics, getTrendBidAccountRecords, getAreaDealList, getDealTrendList, getAssetClassDealList, getDeadlineTime } from '@/api/trading'
export default {
  components: {
    mapNav,
    vueSeamlessScroll,
    NumberFlashCopy
  },
  data() {
    return {
      records: require('./views/mapData/foshan_area_data.json'),
      areaCode: 'D4406',
      areaLevel: '1',
      currentArea: '佛山市',
      currentDate: '',
      currentAccount: 'fs',

      queryParams: {
        year: '',
        quarter: '',
        month: ''
      },

      tradingModeOptions: [
        {
          id: '5',
          text: '网上竞投（批量）'
        },
        {
          id: '1',
          text: '网上竞投'
        },
        {
          id: '2',
          text: '公开协商'
        },
        {
          id: '3',
          text: '简易竞投'
        },
        {
          id: '6',
          text: '定价招租'
        },
        {
          id: '7',
          text: '书面竞投'
        }
      ],
      quarterMap: {
        1: '一',
        2: '二',
        3: '三',
        4: '四'
      },

      dateOptions: [
        { id: '1', text: '年度' },
        { id: '2', text: '季度' },
        { id: '3', text: '月度' }
      ],
      typeOptions: [
        { id: '2', text: '成交项目数' },
        { id: '1', text: '成交总金额' }
      ],
      switch1: '1',
      switch2: '1',
      switch3: '2',
      switch4: '1',
      yearOptions: [],
      quarterOptions: [
        { id: '1', text: '第一季度' },
        { id: '2', text: '第二季度' },
        { id: '3', text: '第三季度' },
        { id: '4', text: '第四季度' }
      ],
      monthOptions: [
        { id: '1', text: '1月' },
        { id: '2', text: '2月' },
        { id: '3', text: '3月' },
        { id: '4', text: '4月' },
        { id: '5', text: '5月' },
        { id: '6', text: '6月' },
        { id: '7', text: '7月' },
        { id: '8', text: '8月' },
        { id: '9', text: '9月' },
        { id: '10', text: '10月' },
        { id: '11', text: '11月' },
        { id: '12', text: '12月' }
      ],

      data1_1: [],
      data1_2: [],
      chart1_2: null,
      data1_3: [],
      chart1_3: null,
      data2_1: {},
      data3_1: [],
      rankBoxHeight: 0,
      data3_2: {},
      chart3_2: null,
      dialogVisible: false,
      detailLoading: false,
      detailQueryParams: {
        date: [],
        level: '1'
      },
      tableHeight: 0,
      detailList: [],
      data3_3: [],
      chart3_3: null,

      playInterval: 5000,
      task1_3: null,
      task3_1: null,
      task3_3: null,
      setData: {
        data1_3_1: [],
        data1_3_2: [],
        data1_3_3: [],
        data3_1_1: [],
        data3_1_2: [],
        data3_3_1: [],
        data3_3_2: [],
        data3_3_3: []
      },
      currentMouseOverTarget: null // add by ymk 当前鼠标在哪个物体上，就要停止哪个物体上的滚动
    }
  },
  computed: {
    scrollConfig() {
      return {
        step: 0.5,
        hover: true,
        hoverStop: true,
        limitMoveNum: 7
      }
    }
  },
  watch: {
    currentAccount: {
      handler: function (val) {
        localStorage.setItem('currentAccount', val)
      },
      immediate: true
    }
  },
  created() {},
  mounted() {
    this.getDomHeight()
    this.initBaseData()
    window.onresize = () => {
      this.chart1_2.resize()
      this.chart1_3.resize()
      this.chart3_2.resize()
    }
  },
  beforeDestroy() {
    this.task1_3 && clearInterval(this.task1_3)
    this.task3_1 && clearInterval(this.task3_1)
    this.task3_3 && clearInterval(this.task3_3)
  },
  methods: {
    handleMouseEnter(type) {
      this.currentMouseOverTarget = type
    },
    handleMouseLeave(type) {
      this.currentMouseOverTarget = null
    },
    formatMoney(val = 0) {
      return changeMoney(val)
    },
    formateMoney(val = 0, pureText = true) {
      const { num, unit } = changeMoney(val)
      const numStr = (num && num.toLocaleString) ? num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : num // add by ymk 添加千分位格式化
      if (pureText) return numStr + unit
      return { num: numStr, unit }
    },
    getNextOption(options, current, target) {
      if (target == this.currentMouseOverTarget) return null // 如果当前鼠标在上面，就停止滚动
      const currentIndex = options.findIndex((i) => i.id == current)
      let nextIndex = 0
      if (currentIndex >= options.length - 1) {
        nextIndex = 0
      } else {
        nextIndex = currentIndex + 1
      }
      return options[nextIndex]
    },
    initBaseData() {
      const startYear = 2023
      const currentYear = new Date().getFullYear()
      this.$set(this.queryParams, 'year', currentYear)
      for (let i = currentYear; i >= startYear; i--) {
        this.yearOptions.push({
          id: i,
          text: i + '年'
        })
      }
      getDeadlineTime().then((res) => {
        this.currentDate = getLastFormatDate(res.data.split(' ')[0])
      })
      this.getPageData()
    },
    getPageData() {
      this.getData1_1()
      this.getData1_2()
      this.getData1_3()
      this.getData2_1()
      this.getData3_1()
      this.getData3_2()
      this.getData3_3()
    },
    // 重点资产类型成交分析
    getData1_1() {
      const params = {
        ...this.queryParams,
        level: this.switch1,
        areaCode: this.areaCode
      }
      const imgMap = {
        耕地: require('@/assets/image/trading/icon1.png'),
        鱼塘: require('@/assets/image/trading/icon2.png'),
        厂房: require('@/assets/image/trading/icon3.png'),
        商铺: require('@/assets/image/trading/icon4.png'),
        仓库: require('@/assets/image/trading/icon5.png')
      }
      getAssetClassDealList(params).then((res) => {
        this.data1_1 = res.data.map((item) => {
          return {
            type: item.assetClassName,
            imgUrl: imgMap[item.assetClassName],
            cjl: (item.dealRate || 0) * 100,
            yjl: item.dealPriceRate || 0,
            dealPrice: item.avgDealRate || 0,
            unit: item.assetClassName == '耕地' || item.assetClassName == '鱼塘' ? '元/亩/年' : '元/平方米/月'
          }
        })
      })
    },
    // 交易模式成交分析
    getData1_2() {
      getProjectInfos({
        ...this.queryParams,
        areaCode: this.areaCode,
        areaLevel: this.areaLevel,
        tradingMode: '1'
      }).then((res) => {
        this.data1_2 = this.tradingModeOptions.map((item) => {
          const data = res.data.find((i) => i.tradingMode == item.id)
          const value = data ? data.sumDealNum : 0
          return {
            name: item.text,
            value
          }
        })

        // 处理百分比
        const total = this.data1_2.reduce((total, item) => total + Number(item.value), 0)
        console.log(total, 'total')
        this.data1_2.map((item, index) => {
          if (total == 0) {
            item.rate = '0.00'
          } else {
            item.rate = ((Number(item.value) * 100) / total).toFixed(2) + ''
          }
        })
        if (total) {
          const temp = this.data1_2.find((i) => i.rate !== '0.00')
          temp.rate = (
            100 -
            this.data1_2.reduce((total, item) => {
              if (item.name === temp.name) {
                return total
              } else {
                return total + Number(item.rate)
              }
            }, 0)
          ).toFixed(2)
        }
        console.log(this.data1_2)
        this.initChart1_2()
      })
    },
    initChart1_2() {
      const chartDom = document.getElementById('chart1_2')
      this.chart1_2 = echarts.init(chartDom)
      const option = {
        color: ['#368aff', '#1fb5fc', '#27e9cb', '#fed130', '#fd895b', '#fc5659'],
        tooltip: {
          trigger: 'item',
          formatter: (e) => {
            return e.marker + e.name + '<br />成交' + Number(e.value).toLocaleString() + '宗 占比' + e.data.rate + '%'
          }
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          bottom: 'center',
          formatter: (e) => {
            const current = this.data1_2.find((i) => i.name === e)
            const value = current.value
            const rate = current.rate.toString().includes('-') ? 0 : current.rate

            return `{name| ${e}} {value| ${value}宗} {rate| ${rate}%}`
          },
          icon: 'roundRect',
          itemHeight: 18,
          itemWidth: 18,
          itemGap: 12,
          textStyle: {
            align: 'right',
            rich: {
              name: {
                color: '#D1EFFF',
                fontSize: 15,
                width: 120,
                align: 'left'
              },
              value: {
                color: '#D1EFFF',
                fontSize: 15,
                width: 80
              },
              rate: {
                color: '#D1EFFF',
                fontSize: 15
              }
            }
          }
        },
        labelLine: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: '70%',
            center: ['20%', '50%'],
            data: this.data1_2,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            }
          }
        ]
      }
      this.chart1_2.setOption(option)
    },
    handleData1_3(data, type) {
      let newData = data.reverse()
      newData = data.map((i) => {
        const subYear = i.year.toString().slice(2)
        return {
          name: type == '1' ? i.year + '年' : type == '2' ? i.year + '年\n第' + this.quarterMap[i.quarter] + '季度' : subYear + '年\n' + i.month + '月',
          value: i.sumDealNum + '',
          year: i.year,
          quarter: i.quarter,
          month: i.month
        }
      })
      return newData
    },
    getData1_3() {
      this.task1_3 && clearInterval(this.task1_3)
      this.switch2 = '1'
      const params = {
        areaCode: this.areaCode,
        areaLevel: this.areaLevel
      }
      getDealTrendList({
        ...params,
        level: '1'
      }).then((res) => {
        const data = this.handleData1_3(res.data, '1')
        this.setData.data1_3_1 = data
        this.data1_3 = data
        this.$nextTick(() => {
          this.initChart1_3()
        })
        this.task1_3 = setInterval(() => {
          const next = this.getNextOption(this.dateOptions, this.switch2, '1_3')
          if (next) this.handleChangeSwitch2(next)
        }, this.playInterval)
      })
      getDealTrendList({
        ...params,
        level: '2'
      }).then((res) => {
        const data = this.handleData1_3(res.data, '2')
        this.setData.data1_3_2 = data
      })
      getDealTrendList({
        ...params,
        level: '3'
      }).then((res) => {
        const data = this.handleData1_3(res.data, '3')
        this.setData.data1_3_3 = data
      })
    },
    initChart1_3() {
      const chartDom = document.getElementById('chart1_3')
      this.chart1_3 = echarts.init(chartDom)
      const option = {
        color: ['#1ce2dc'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (e) => {
            return e[0].marker + e[0].name + ' 成交：' + Number(e[0].value).toLocaleString() + '宗'
          }
        },
        xAxis: {
          type: 'category',
          data: this.data1_3.map((i) => i.name),
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            rotate: 0,
            textStyle: {
              color: '#3ea7e1',
              fontSize: 14
            }
          }
        },
        yAxis: {
          name: '单位：宗',
          nameTextStyle: {
            color: '#3ea7e1',
            fontSize: 12
          },
          type: 'value',
          splitLine: {
            lineStyle: {
              color: '#214672'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#3ea7e1',
              fontSize: 14
            }
          }
        },
        grid: {
          left: '60px',
          right: '30px',
          top: '40px',
          bottom: '45px'
        },
        series: [
          {
            data: this.data1_3.map((i) => i.value),
            type: 'line',
            symbol: 'circle',
            symbolSize: 12,
            step: false,
            lineStyle: {
              color: '#1ce2dc'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1,
                    color: 'transparent' // 0% 处的颜色
                  },
                  {
                    offset: 0,
                    color: '#0B5F8D' // 100% 处的颜色
                  }
                ]
              }
            }
          }
        ]
      }
      this.chart1_3.setOption(option)
    },
    getData2_1() {
      getProjectInfos({
        ...this.queryParams,
        areaCode: this.areaCode,
        areaLevel: this.areaLevel
      }).then((res) => {
        this.data2_1 = {
          totalAmount: res.data?.[0]?.sumDealAmount * 100000000 || 0,
          onlineProjectNum: res.data?.[0]?.sumProjectNum || 0,
          dealProjectNum: res.data?.[0]?.sumDealNum || 0,
          cjl: res.data?.[0]?.sumDealRate ? res.data?.[0]?.sumDealRate * 100 : 0,
          yjl: res.data?.[0]?.sumPremiumRate ? res.data?.[0]?.sumPremiumRate * 100 : 0
        }
      })
    },
    handleData3_1(data) {
      return data.map((i) => {
        return {
          areaName: i.areaName,
          value: this.switch3 == '2' ? i.sumDealNum : i.sumDealAmount,
          sumDealNum: i.sumDealNum,
          sumDealAmount: i.sumDealAmount
        }
      })
    },
    getData3_1() {
      this.task3_1 && clearInterval(this.task3_1)
      this.data3_1 = []
      this.switch3 = '2'
      getAreaDealList({
        ...this.queryParams,
        areaLevel: 3,
        sort: '2'
      }).then((res) => {
        this.setData.data3_1_1 = this.handleData3_1(res.data)
        this.data3_1 = this.setData.data3_1_1
        this.task3_1 = setInterval(() => {
          const next = this.getNextOption(this.typeOptions, this.switch3, '3_1')
          if (next) this.handleChangeSwitch3(next)
        }, this.playInterval)
      })
      getAreaDealList({
        ...this.queryParams,
        areaLevel: 3,
        sort: '1'
      }).then((res) => {
        this.setData.data3_1_2 = this.handleData3_1(res.data)
      })
    },
    getData3_2() {
      getVistisStatistics({}).then((res) => {
        this.data3_2 = {
          totalRegistry: res.data.data.totalRegistrations,
          averageAccessDay: res.data.data.avgDailyVisitors,
          appAccess: res.data.data.trafficStatisticsList[0].appVisitors,
          webAccess: res.data.data.trafficStatisticsList[0].pcVisitors,
          totalVisitors: res.data.data.totalVisitors
        }
        // this.data3_2 = {
        //   totalRegistry: 0,
        //   averageAccessDay: 0,
        //   appAccess: 0,
        //   webAccess: 0,
        //   totalVisitors: 0
        // }
        this.initChart3_2()
      })
    },
    initChart3_2() {
      const chartDom = document.getElementById('chart3_2')
      this.chart3_2 = echarts.init(chartDom)
      const data = [
        { name: '小程序', value: this.data3_2.appAccess },
        { name: '交易网站', value: this.data3_2.webAccess }
        // { name: '总访问人数', value: this.data3_2.totalVisitors }
      ]
      const _this = this
      const showText = (this.data3_2.totalVisitors / 10000).toFixed(0)
      const option = {
        color: this.data3_2.totalVisitors ? ['#00FFF1', '#1FB5FC'] : ['#5d6579', '#5d6579'],
        tooltip: {
          show: !!this.data3_2.totalVisitors,
          trigger: 'item',
          formatter: (e) => {
            return `${e.marker}${e.name}<br />访问人数：${Number(e.value).toLocaleString()}人次<br />占比：${e.percent.toFixed(2)}%`
          }
        },
        legend: {
          show: false
        },
        title: {
          show: true,
          text: (showText * 1).toLocaleString(),
          subtext: '访问总人数\n(万人次)',
          left: 'center',
          top: '38%',
          itemGap: 5,
          textStyle: {
            color: '#1ce2dc',
            fontSize: 17,
            fontWeight: 'bold'
          },
          subtextStyle: {
            fontSize: 12,
            color: '#ffffff'
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['52%', '70%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            padAngle: 0,
            label: {
              show: !!this.data3_2.totalVisitors,
              color: 'inherit',
              fontSize: 14,
              formatter: (e) => {
                const val = changeMoney(e.value, ['', '万', '亿', '万'])// add by ymk 加上万的单位转换
                return `${e.name}\n${Number(val.num).toLocaleString('en-US', { maximumFractionDigits: 2 }) + val.unit}人次\n${e.percent.toFixed(2)}%`
              }
            },
            silent: !this.data3_2.totalVisitors,
            data: data
          },
          {
            type: 'custom',
            coordinateSystem: 'none',
            silent: true,
            data: [0],
            renderItem(params, api) {
              // 环形图半径
              const r = Math.min(api.getWidth(), api.getHeight()) / 2
              // 圆心
              const center = {
                x: api.getWidth() / 2,
                y: api.getHeight() / 2
              }
              // 大圆半径
              const rBig = r * (_this.data3_2.totalVisitors ? 0.8 : 0.95)
              // 大圆上的扇形
              const bigSector = []
              const sectorSize = 60 // 扇形长度（弧度）
              const sectorInterval = 30 // 扇形与扇形之间的间隔
              const BigStartAngle = 310 // 大扇形起始角度
              for (let i = 0; i < 4; i++) {
                const startAngle = ((i * (sectorInterval + sectorSize) + BigStartAngle) * Math.PI) / 180
                const endAngle = startAngle + (sectorSize * Math.PI) / 180
                bigSector.push({
                  type: 'sector',
                  shape: {
                    cx: center.x,
                    cy: center.y,
                    r: rBig,
                    r0: rBig * 0.98,
                    startAngle,
                    endAngle
                  },
                  style: {
                    fill: '#C8932F',
                    lineWidth: 2
                  }
                })
              }
              return {
                type: 'group',
                children: [
                  {
                    type: 'group',
                    children: [
                      ...bigSector,
                      {
                        // 外圆环
                        type: 'arc',
                        shape: {
                          cx: center.x,
                          cy: center.y,
                          r: rBig
                        },
                        style: {
                          fill: 'transparent',
                          stroke: '#C8932F',
                          lineWidth: 1
                        }
                      }
                    ]
                  }
                ]
              }
            }
          }
        ]
      }
      this.chart3_2.setOption(option)
    },
    handleViewDetail() {
      const date = new Date()
      const current = date.getFullYear() + '-' + (date.getMonth() + 1 + '').padStart(2, '0') + '-' + (date.getDate() + '').padStart(2, '0')
      const last = `${date.getFullYear()}-01-01`
      this.$set(this.detailQueryParams, 'date', [last, current])

      this.dialogVisible = true
      this.handleSearchDetail()

      this.$nextTick(() => {
        const dialog = document.querySelector('.custom-dialog').querySelector('.el-dialog__body')
        const form = dialog.querySelector('.el-form')
        this.tableHeight = dialog.clientHeight - 60 - form.clientHeight
      })
    },
    handleSearchDetail() {
      this.detailLoading = true
      getVistisStatistics({
        level: this.detailQueryParams.level,
        startTime: this.detailQueryParams.date ? this.detailQueryParams.date[0] : '',
        endTime: this.detailQueryParams.date ? this.detailQueryParams.date[1] : ''
      })
        .then((res) => {
          this.detailList = res.data.data.trafficStatisticsList.map((i) => {
            return {
              year: i.year,
              month: i.month || '',
              date: i.date || '',
              total: i.totalVisits,
              web: i.pcVisitors,
              app: i.appVisitors
            }
          })
        })
        .finally(() => {
          this.detailLoading = false
        })
    },
    lastMonthDate(Nowdate) {
      Nowdate = Nowdate ? new Date(Nowdate) : new Date()
      let vYear = Nowdate.getFullYear()
      let vMon = Nowdate.getMonth() + 1
      let vDay = Nowdate.getDate()
      // 每个月的最后一天日期（为了使用月份便于查找，数组第一位设为0）
      const daysInMonth = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
      if (vMon == 1) {
        vYear = Nowdate.getFullYear() - 1
        vMon = 12
      } else {
        vMon = vMon - 1
      }
      // 若是闰年，二月最后一天是29号
      if ((vYear % 4 == 0 && vYear % 100 != 0) || vYear % 400 == 0) {
        daysInMonth[2] = 29
      }
      if (daysInMonth[vMon] < vDay) {
        vDay = daysInMonth[vMon]
      }
      if (vDay < 10) {
        vDay = '0' + vDay
      }
      if (vMon < 10) {
        vMon = '0' + vMon
      }
      const date = vYear + '-' + vMon + '-' + vDay
      const oneDayAfter = new Date(new Date(date).getTime() + 24 * 60 * 60 * 1000)
      return oneDayAfter.getFullYear() + '-' + (oneDayAfter.getMonth() + 1 + '').padStart(2, '0') + '-' + (oneDayAfter.getDate() + '').padStart(2, '0')
    },
    handleData3_3(data, type) {
      return data.occurLists.map((i) => {
        const subYear = i.year.toString().slice(2)
        return {
          name: type == '1' ? i.year + '年' : type == '2' ? i.year + '年\n第' + this.quarterMap[i.quarter] + '季度' : subYear + '年\n' + i.month + '月',
          value: i.totalOccur,
          totalOccur: i.totalOccur,
          year: i.year,
          quarter: i.quarter,
          month: i.month
        }
      })
    },
    getData3_3() {
      this.task3_3 && clearInterval(this.task3_3)
      this.switch4 = '1'
      getTrendBidAccountRecords({
        level: '1',
        areaCode: this.areaCode
      }).then((res) => {
        if (res.data) {
          this.setData.data3_3_1 = this.handleData3_3(res.data, '1')
          this.data3_3 = this.handleData3_3(res.data, '1')
          this.$nextTick(() => {
            this.initChart3_3()
          })
          this.task3_3 = setInterval(() => {
            const next = this.getNextOption(this.dateOptions, this.switch4, '3_3')
            if (next) this.handleChangeSwitch4(next)
          }, this.playInterval)
        }
      })
      getTrendBidAccountRecords({
        level: '2',
        areaCode: this.areaCode
      }).then((res) => {
          if (res.data) {
            this.setData.data3_3_2 = this.handleData3_3(res.data, '2')
          }
      })
      getTrendBidAccountRecords({
        level: '3',
        areaCode: this.areaCode
      }).then((res) => {
          if (res.data) {
            this.setData.data3_3_3 = this.handleData3_3(res.data, '3')
          }
      })
    },
    initChart3_3() {
      const chartDom = document.getElementById('chart3_3')
      this.chart3_3 = echarts.init(chartDom)
      const option = {
        color: {
          type: 'linear',
          x: 0,
          y: 1,
          colorStops: [
            {
              offset: 1,
              color: '#50ADFB'
            },
            {
              offset: 0,
              color: '#0B2D77'
            }
          ]
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (e) => {
            return `${e[0].name}：${Number(e[0].value).toLocaleString()}万元`
          }
        },
        xAxis: {
          type: 'category',
          data: this.data3_3.map((i) => i.name),
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            rotate: 0,
            textStyle: {
              color: '#3ea7e1',
              fontSize: 14
            }
          }
        },
        yAxis: {
          name: '单位：万元',
          nameTextStyle: {
            color: '#3ea7e1',
            fontSize: 14
          },
          type: 'value',
          splitLine: {
            lineStyle: {
              color: '#214672'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#3ea7e1',
              fontSize: 14
            }
          }
        },
        grid: {
          left: '60px',
          right: '30px',
          top: '40px',
          bottom: '45px'
        },
        series: [
          {
            data: this.data3_3.map((i) => i.value),
            type: 'bar',
            barWidth: 300 / this.data3_3.length > 40 ? 40 : 300 / this.data3_3.length
          }
        ]
      }
      this.chart3_3.setOption(option)
    },
    getDomHeight() {
      const dom = document.getElementById('rank-list-box')
      this.rankBoxHeight = dom.clientHeight - 86
    },
    handleDateChange() {
      this.reloadData()
    },
    handleChangeSwitch1(item) {
      if (item.id == this.switch1) return

      this.switch1 = item.id
      if (item.id == '1' && !this.queryParams.year) {
        this.$set(this.queryParams, 'year', new Date().getFullYear() + '')
      } else if (item.id == '2' && !this.queryParams.quarter) {
        // this.$set(this.queryParams, 'month', '')
        this.$set(this.queryParams, 'quarter', Math.ceil((new Date().getMonth() + 1) / 3) + '')
      } else if (item.id == '3' && !this.queryParams.month) {
        this.$set(this.queryParams, 'quarter', '')
        this.$set(this.queryParams, 'month', new Date().getMonth() + 1 + '')
      }
      if (item.id !== '3') {
        this.$set(this.queryParams, 'month', '')
      }
      if (item.id !== '2') {
        this.$set(this.queryParams, 'quarter', '')
      }
      this.reloadData()
    },
    reloadData() {
      this.task1_3 && clearInterval(this.task1_3)
      this.task3_1 && clearInterval(this.task3_1)
      this.task3_3 && clearInterval(this.task3_3)
      this.getData1_1()
      this.getData1_2()
      this.getData2_1()
      this.getData3_1()
    },
    handleChangeSwitch2(item) {
      if (item.id == this.switch2) return
      this.switch2 = item.id
      if (item.id == '1') {
        this.data1_3 = this.setData.data1_3_1
      }
      if (item.id == '2') {
        this.data1_3 = this.setData.data1_3_2
      }
      if (item.id == '3') {
        this.data1_3 = this.setData.data1_3_3
      }
      this.$nextTick(() => {
        this.initChart1_3()
      })
    },
    handleChangeSwitch3(item) {
      if (item.id == this.switch3) return

      this.switch3 = item.id
      if (item.id == '1') {
        this.data3_1 = this.setData.data3_1_1
      }
      if (item.id == '2') {
        this.data3_1 = this.setData.data3_1_2
      }
      this.$nextTick(() => {
        this.$refs._rankListRef.scrollTo(0, 0)
      })
    },
    handleChangeSwitch4(item) {
      if (item.id == this.switch4) return
      this.switch4 = item.id
      if (item.id == '1') {
        this.data3_3 = this.setData.data3_3_1
      }
      if (item.id == '2') {
        this.data3_3 = this.setData.data3_3_2
      }
      if (item.id == '3') {
        this.data3_3 = this.setData.data3_3_3
      }
      this.$nextTick(() => {
        this.initChart3_3()
      })
      // this.getData3_3()
    },
    gotopath(type) {
      console.log(type)
      const current = this.records.records.find((item) => {
        return item.areaCode == `D${type}`
      })
      console.log(current)
      this.currentArea = current.areaName
      this.areaCode = current.areaCode
      if (type != undefined) {
        this.areaCode = type
        this.areaCode = 'D' + this.areaCode
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'nh'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'ss'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'cc'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'sd'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'gm'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'fs'
      }

      if (type.length == 6) {
        this.areaLevel = '2'
      } else if (type.length == 9) {
        this.areaLevel = '3'
      } else {
        this.areaLevel = '1'
      }

      this.getPageData()
    },
    formatNumber(num, cent, isThousand = true) {
      if (typeof num !== 'number') return
      num = num.toString().replace(/\$|\,/g, '')

      // 检查传入数值为数值类型
      if (isNaN(num)) {
        num = '0'
      }

      // 获取符号(正/负数)
      const sign = num == (num = Math.abs(num))

      num = Math.floor(num * Math.pow(10, cent) + 0.***********) // 把指定的小数位先转换成整数.多余的小数位四舍五入
      let cents = num % Math.pow(10, cent) // 求出小数位数值
      num = Math.floor(num / Math.pow(10, cent)).toString() // 求出整数位数值
      cents = cents.toString() // 把小数位转换成字符串,以便求小数位长度

      // 补足小数位到指定的位数
      while (cents.length < cent) {
        cents = '0' + cents
      }

      if (isThousand) {
        // 对整数部分进行千分位格式化.
        for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++) {
          num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3))
        }
      }

      if (cent > 0) {
        return (sign ? '' : '-') + num + '.' + cents
      } else {
        return (sign ? '' : '-') + num
      }
    }
  }
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.container {
  height: calc(100% - 110px);
  display: flex;
  color: #3ea7e1;
  padding: 0 20px 20px;
  .left-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .top {
      flex: 15;
      .block-body {
        display: flex;
        flex-direction: column;
        .rank-header {
          margin: 15px 0 0 0;
          padding: 10px 0;
          display: flex;
          background: linear-gradient(to right, #15408e 0%, #0e3077 75%, #072957 100%);
          .header-item {
            text-align: center;
            color: #fff;
          }
        }
        .rank-item {
          flex: 1;
          display: flex;
          margin: 10px 0;
          font-weight: bold;
          font-size: 20px;
          background: linear-gradient(to right, transparent 0%, #022755 20%, #022755 80%, transparent 100%);
          & > div {
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          &.stripe {
            color: #1ce2dc;
            background: linear-gradient(to right, transparent 0%, #033d59 20%, #033d59 80%, transparent 100%);
          }
        }
      }
    }

    .center {
      flex: 11;
      margin: 20px 0;
    }

    .bottom {
      flex: 9;
      position: relative;
      .switch-box {
        position: absolute;
        right: 20px;
        top: 20px;
      }
    }
  }
  .center-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0 20px;
    .top-container {
      flex: 6;
      display: flex;
      flex-direction: column;
      .tooltip {
        .info {
          display: flex;
          justify-content: space-between;
        }
      }
      .map {
        flex: 1;
        height: 100%;
        width: 100%;
      }
    }
    .bottom-container {
      flex: 2;
      display: flex;
      flex-direction: column;
      .top {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 20px;
        font-weight: bold;
        margin-top: 10px;
      }
      .bottom {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: flex-end;
        .block-box {
          width: calc(50% - 10px);
          height: calc(50% - 10px);
          .block-body {
            padding-bottom: 0;
            display: flex;
            align-items: center;
            padding-right: 0;
            .img {
              height: calc(100% - 20px);
            }
            .detail-info {
              flex: 1;
              display: flex;
              flex-direction: column;
              margin-left: 30px;
              .value {
                font-size: 26px;
                color: #fff;
              }
              .title {
                color: #1ce2dc;
              }
            }
          }
        }
      }
    }
  }
  .right-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .top {
      flex: 15;
      position: relative;
      .switch-box {
        position: absolute;
        right: 20px;
        top: 20px;
      }
      .block-body {
        display: flex;
        flex-direction: column;
        .rank-header {
          margin: 15px 0 0 0;
          padding: 10px 0;
          display: flex;
          background: linear-gradient(to right, #15408e 0%, #0e3077 75%, #072957 100%);
          .header-item {
            text-align: center;
            color: #fff;
          }
        }

        .rank-list {
          flex: 1;
          overflow: hidden;
          margin-left: 15px;
          .rank-item {
            height: 45px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            &.level1 {
              background: url('@/assets/image/no1.png') no-repeat 100% 100%;
            }
            &.level2 {
              background: url('@/assets/image/no2-3.png') no-repeat;
            }
            &.level3 {
              background: url('@/assets/image/no4.png') no-repeat;
            }
            background-size: 100% 100% !important;

            .rank-order {
              display: block;
              width: 73px;
              text-align: center;
              font-size: 24px;
              color: #fff;
              font-weight: bold;
              margin-left: 3px;
            }
            .area-name {
              flex: 1;
              display: block;
              width: 73px;
              text-align: center;
              font-size: 18px;
              color: #bff7ff;
              line-height: 36px;
            }
            .value {
              flex: 1;
              text-align: center;
              // text-align: right;
              color: #bff7ff;
            }
          }
        }
      }
    }

    .center {
      flex: 11;
      margin: 20px 0;
      position: relative;
      .more-btn {
        position: absolute;
        right: 20px;
        top: 15px;
        color: #fff;
        font-size: 14px;
      }
      .block-body {
        display: flex;
        padding-right: 0;

        .left {
          height: 100%;
          flex: 3;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          // padding: 20px 0;
          box-sizing: border-box;
          .info {
            box-sizing: border-box;
            flex: 1;
            width: 100%;
            background: url('@/assets/image/trading/box.png') 0 0 no-repeat;
            background-size: 100% 100%;
            margin-top: 15px;
            display: flex;
            align-items: center;
            &:last-child {
              margin: 10px 0;
            }
            .img {
              margin: 0 15px;
            }
            .detail-info {
              .title {
                color: #fff;
              }
              .value {
                background: linear-gradient(to top, #3bbafb, #fff);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
              }
            }
          }
        }
        .right {
          flex: 5;
          padding-top: 10px;
        }
      }
    }

    .bottom {
      flex: 9;
      position: relative;
      .switch-box {
        position: absolute;
        right: 20px;
        top: 20px;
      }
    }
  }
}
.p0 {
  padding: 0 !important;
}
.area-time {
  font-size: 14px;
  color: #8fd2fd;
  display: flex;
  flex-direction: row;
  align-items: center;
  .area-img {
    height: 18px;
    width: 18px;
  }
  .area-title {
    margin: 0 10px;
    letter-spacing: 1px;
  }
  .area {
    font-weight: bold;
  }
}

.area-org {
  display: flex;
  flex-direction: row;
  align-items: center;
  // font-style: italic;
  line-height: 24px;
  .area-img {
    height: 18px;
    width: 18px;
  }
  .area-title {
    color: #fff;
    margin: 0 10px;
    font-size: 14px;
    letter-spacing: 1px;
  }
  .area {
    font-weight: bold;
    background: linear-gradient(to top, #d3a27a, #fff);
    -webkit-background-clip: text;
    color: transparent;
  }
}

::v-deep .el-select {
  .el-input__inner {
    color: rgb(66, 217, 236) !important;
    background: linear-gradient(to bottom, #005ba8, #002d59) !important;
    border-color: rgb(0, 156, 255) !important;
    font-size: 16px;
  }
  .el-select__caret {
    color: rgb(66, 217, 236) !important;
  }
  .popper__arrow {
    display: none;
  }

  .el-select-dropdown {
    background-color: #005ba8 !important;
    border: none;
    .el-select-dropdown__item {
      color: #fff !important;
      &.selected,
      &.hover {
        background-color: #3ea7e1;
      }
    }
  }
}

.block-box {
  background-color: rgba(3 20 61 / 53%);
  box-shadow: rgba(0 123 228 / 50%) 0px 0px 40px 9px inset;
  display: flex;
  flex-direction: column;
  .block-title {
    height: 40px;
    line-height: 45px;
    background: url('@/assets/image/title.png') 0 0 no-repeat;
    padding-left: 36px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
  }
  .block-body {
    flex: 1;
    padding: 0 30px 10px 30px;
  }
}

::v-deep .custom-dialog {
  // background: url('@/assets/image/bj.jpg') 0 0 no-repeat;
  background: #132647;
  box-shadow: inset 0 0 60px #3d61a4;
  .el-dialog__header {
    position: relative;
    .title {
      color: #fff;
      font-size: 22px;
      padding-left: 50px;
      background: url('@/assets/image/trading/title.png') 0 0 no-repeat;
    }
    .close-btn {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .el-dialog__body {
    height: 500px;
    .el-form {
      .el-form-item__label {
        color: #3ea7e1;
        font-size: 16px;
      }
      .el-date-editor {
        background: linear-gradient(to bottom, #005ba8, #002d59) !important;
        border-color: #3ea7e1 !important;
        .el-icon-date {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range-input {
          color: #3ea7e1 !important;
          background: transparent;
          border-color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range-separator {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range__close-icon {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-select__caret {
          color: #3ea7e1 !important;
        }
        .popper__arrow {
          display: none;
        }
        .el-picker-panel {
          background-color: #3ea7e1 !important;
          color: #fff !important;
          .el-picker-panel__icon-btn,
          .el-date-table th,
          .el-date-table td {
            color: #fff !important;
          }
          .el-date-table td.in-range {
            color: #3ea7e1 !important;
          }
        }
      }

      .search-btn {
        background-color: #71deff;
        border-radius: 10px;
        color: #13264a;
        font-size: 16px;
        padding: 4px 15px;
      }
    }
    .el-table {
      background-color: transparent;
      border: none;
      &::before {
        display: none;
      }
      .el-table__body-wrapper {
        // &::-webkit-scrollbar {
        //   width: 0;
        //   height: 0;
        //   border: none;
        // }
        &::-webkit-scrollbar {
          width: 10px !important;
          height: 100% !important;
        }
        &::-webkit-scrollbar-track {
          background-color: rgba(62, 167, 225, 0.1) !important;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 5px !important;
          background-color: rgba(62, 167, 225, 0.2) !important;
        }
      }
      th.gutter {
        display: none !important;
        width: 0 !important;
      }

      colgroup col[name='gutter'] {
        display: none;
        width: 0;
      }

      .el-table__body {
        width: 100% !important;
      }

      th,
      td {
        border: none;
      }
      th {
        color: #3ea7e1;
        background-color: #02398b;
      }
      tr,
      td {
        color: #3ea7e1;
        background-color: transparent;
      }
      tr.el-table__row--striped td {
        background-color: rgba(26, 58, 96, 0.5);
      }
    }
  }
}
</style>
