export function formatNumber (num, cent, isThousand = true) {
  num = num.toString().replace(/\$|\,/g, '')

  // 检查传入数值为数值类型
  if (isNaN(num)) {
    num = '0'
  }

  // 获取符号(正/负数)
  const sign = num == (num = Math.abs(num))

  num = Math.floor(num * Math.pow(10, cent) + 0.50000000001) // 把指定的小数位先转换成整数.多余的小数位四舍五入
  let cents = num % Math.pow(10, cent) // 求出小数位数值
  num = Math.floor(num / Math.pow(10, cent)).toString() // 求出整数位数值
  cents = cents.toString() // 把小数位转换成字符串,以便求小数位长度

  // 补足小数位到指定的位数
  while (cents.length < cent) {
    cents = '0' + cents
  }

  if (isThousand) {
    // 对整数部分进行千分位格式化.
    for (var i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++) {
      num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3))
    }
  }

  if (cent > 0) {
    return (sign ? '' : '-') + num + '.' + cents
  } else {
    return (sign ? '' : '-') + num
  }
}


/**
 * 统一数据单位工具函数
 * @param {Array} dataArray - 包含数值的数据数组
 * @param {String} valueKey - 数值字段的键名
 * @param {Boolean} formatThousand - 是否需要千分位格式化
 * @param {Number} precision - 小数点保留位数
 * @return {Object} 包含转换后的数据和统一单位
 */
export function unifyDataUnit(dataArray, valueKey = 'value', formatThousand = true, precision = 2) {
  if (!dataArray || !dataArray.length) {
    return { data: [], unit: '' };
  }
  
  // 1. 找出最大值
  const maxValue = Math.max(...dataArray.map(item => Math.abs(Number(item[valueKey]) || 0)));
  
  // 2. 定义单位映射关系
  const unitMap = [
    { value: 1, text: '元' },
    { value: 10000, text: '万元' },
    { value: 100000000, text: '亿元' },
    { value: 1000000000000, text: '万亿' }
  ];
  
  // 3. 确定最大值应该使用的单位
  let unitIndex = 0;
  for (let i = unitMap.length - 1; i >= 0; i--) {
    if (maxValue >= unitMap[i].value) {
      unitIndex = i;
      break;
    }
  }
  
  const targetUnit = unitMap[unitIndex];
  
  // 4. 统一转换所有数据
  const convertedData = dataArray.map(item => {
    const originalValue = Number(item[valueKey]) || 0;
    const convertedValue = originalValue / targetUnit.value;
    
    // 格式化转换后的值（保留小数位数和千分位）
    let formattedValue = Number(convertedValue.toFixed(precision));
    if (formatThousand) {
      formattedValue = formattedValue.toLocaleString('en-US', { 
        minimumFractionDigits: precision,
        maximumFractionDigits: precision
      });
    }
    
    return {
      ...item,
      [valueKey]: formattedValue,
      originalValue: originalValue, // 保存原始值便于后续处理
      convertedValue: convertedValue // 保存转换后但未格式化的值
    };
  });
  
  return {
    data: convertedData,
    unit: targetUnit.text
  };
}

export function changeMoney (num, units = ['元', '万元', '亿元', '万亿']) {
  let curentUnit = units.length ? units[0] : ''

  if (isNaN(num)) {
    return {
      num,
      unit: curentUnit
    }
  }

  const dividend = 10000
  let curentNum = num
  // 转换数字
  // 转换单位
  for (var i = 0; i < 4; i++) {
    if (units.length) {
      curentUnit = units[i]
    }
    if (strNumSize(curentNum) < 5) {
      break
    }
    curentNum = curentNum / dividend
  }
  const m = {
    num: 0,
    unit: ''
  }
  m.num = Number(curentNum)
  m.unit = curentUnit
  return m
}

function strNumSize (tempNum) {
  const stringNum = tempNum.toString()
  const index = stringNum.indexOf('.')
  let newNum = stringNum
  if (index != -1) {
    newNum = stringNum.substring(0, index)
  }
  return newNum.length
}

export function getLastFormatDate(date) {
  let newDate = new Date(date);
  // 日期减一天
  newDate.setDate(newDate.getDate() - 1);
  const year = newDate.getFullYear();
  const month = String(newDate.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需加1
  const day = String(newDate.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}