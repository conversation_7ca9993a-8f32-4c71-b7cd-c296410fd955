<!-- pager -->
<template>
  <div class="pager">
    <div class="left-pager" :class="currentPage <= 1 ? 'disabled' : ''" @click.self="prevPage" />
    <span>
      <span>{{ currentPage }}</span>
      / {{ totalPage }}
    </span>
    <div class="right-pager" :class="currentPage >= totalPage ? 'disabled' : ''" @click.self="nextPage" />
  </div>
</template>

<script>
export default {
  name: 'Pager',
  props: {
    totalPage: {
      // 总页数
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      currentPage: 1
    }
  },
  watch: {
    totalPage: {
      handler: function (val) {
        this.currentPage = this.totalPage
      },
      immediate: true
    }
  },
  created() {
    this.currentPage = this.totalPage
  },
  methods: {
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage = this.currentPage - 1
        this.$emit('prevPage')
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPage) {
        this.currentPage = this.currentPage + 1
        this.$emit('nextPage')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pager {
  color: #fff;
  position: absolute;
  right: 20px;
  top: 30px;
  display: flex;
  z-index: 9;
  .left-pager {
    cursor: pointer;
    background: url('../assets/image/arrow-left.png') no-repeat;
    width: 30px;
    height: 30px;
    margin-right: 5px;
    &.disabled {
      cursor: not-allowed;
      background: url('../assets/image/arrow-leftnull.png') no-repeat;
    }
  }
  .right-pager {
    cursor: pointer;
    background: url('../assets/image/arrow-right.png') no-repeat;
    width: 30px;
    height: 30px;
    margin-left: 5px;
    &.disabled {
      cursor: not-allowed;
      background: url('../assets/image/arrow-rightnull.png') no-repeat;
    }
  }
}
</style>
