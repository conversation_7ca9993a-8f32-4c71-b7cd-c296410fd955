import backgroundImage from '../../assets/image/圆环背景.png'

/**
 * 绘制3d图
 * @param pieData 总数据
 * @param valueUnit:数据单位
 * @param showLegend 是否显示legend
 * @param wrapLegend 是否换行显示legend
 * @param internalDiameterRatio 透明空心占的比率0-1 0
 * @param opacity 饼或者环的透明度
 */

export function getOptions(
  pieData = [],
  valueUnit,
  showLegend,
  internalDiameterRatio = 0.6,
  opacity = 0.8,
  wrapLegend = false,
  showValue = true
) {
  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  const total = pieData.reduce((total, { value = 0 }) => total + value, 0)
  const legend = pieData.map(({ name = '' }) => name)
  const option = {
    // animation: false,
    // color: ['#d8de1b', '#1489ea', '#0196a2', '#b70012', '#fec024', '#5dc216', '#b739d0', '#e75a15'],
    legend: {
      show: showLegend,
      data: legend,
      icon: 'roundRect',
      orient: 'vertical',
      top: 'center',
      right: '20',
      left: '60%',
      itemHeight: 16,
      itemWidth: 16,
      textStyle: {
        fontSize: 14,
        color: 'white',
        rich: {
          title: {
            width: 90,
            fontSize: 14,
            // color: '#0cd2ea',
            padding: [wrapLegend ? 8 : 0, 0]
          }
        }
      },
      // itemGap: legend.length > 5 ? 4 : 8,
      itemGap: 20,
      formatter(seriesName) {
        const item = pieData.find(({ name }) => name === seriesName) || {}
        const { name, value } = item
        const getMaxLength = (prop) =>
          Math.max(...pieData.map((row) => row[prop].length))
        const nameMaxLength = getMaxLength('name')
        const percentage = `${(total ? (value / total) * 100 : 0).toFixed(2)}%`
        const title = name.padEnd(nameMaxLength + 2)
        return `{title|${title}}${
          wrapLegend ? '\n' : ''
        }${value.toLocaleString()}`
      }
    },
    tooltip: {
      formatter: (params) => {
        if (params.seriesName !== 'mouseoutSeries') {
          return `${params.seriesName}<br/>
          <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
  params.color
};"></span>
          ${(
    option.series[params.seriesIndex]?.pieData?.value || 0
  ).toLocaleString()} ${valueUnit}`
        }
      }
    },
    labelLine: {
      show: true,
      lineStyle: {
        color: '#7BC0CB'
      }
    },
    label: {
      show: true,
      position: 'outside',
      rich: {
        b: {
          color: '#7BC0CB',
          fontSize: 12,
          lineHeight: 20
        },
        c: {
          fontSize: 16
        }
      },
      formatter: '{b|{b} \n}{c|{c}}{b|  亩}'
    },
    graphic: [
      {
        type: 'image', // 图形元素类型
        id: 'background', // 更新或删除图形元素时指定更新哪个图形元素，如果不需要用可以忽略。
        left: showLegend ? '0%' : 'center', // 根据父元素进行定位
        top: '33%', // 根据父元素进行定位   （0%）, 如果bottom的值是 0，也可以删除该bottom属性值。
        z: 0, // 层叠
        // bounding: 'all', // 决定此图形元素在定位时，对自身的包围盒计算方式
        style: {
          image: backgroundImage, // 这里一定要注意、注意，必须是https开头的图片路径地址
          width: 234,
          height: 100
          // width: 150,
          // height: 60
        }
      }
    ],
    grid3D: {
      show: false,
      left: showLegend ? '-25%' : 'center',
      top: '-16%',
      boxHeight: 20,
      viewControl: {
        // 3d效果可以放大、旋转等，请自己去查看官方配置
        alpha: 40, // 倾斜角度
        // beta: 40,
        rotateSensitivity: 0, // 旋转比例
        zoomSensitivity: 0, // 放大比例
        panSensitivity: 0, // 平移比例
        autoRotate: false // 自动旋转
      }
      // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
      // postEffect: {//配置这项会出现锯齿，请自己去查看官方配置有办法解决
      //   enable: true,
      //   bloom: {
      //     enable: true,
      //     bloomIntensity: 0.1
      //   },
      //   SSAO: {
      //     enable: false,
      //     quality: 'ultra',
      //     radius: 2
      //   }
      // }
    },
    xAxis3D: {
      min: -1,
      max: 1
    }, // x轴配置
    yAxis3D: {
      min: -1,
      max: 1
    }, // y轴配置
    zAxis3D: {
      // min: -1,
      // max: 'dataMax'
    }, // z轴配置 不设置则自适应
    series: getSeries(pieData, internalDiameterRatio, opacity)
  }
  return option
}

function getSeries(pieData, internalDiameterRatio, opacity) {
  const series = []
  let startValue = 0
  let endValue = 0
  // const color = ['#d8de1b', '#1489ea', '#0196a2', '#b70012', '#fec024', '#5dc216', '#b739d0', '#e75a15']; //颜色列表
  const color = [
    '#65a2f5',
    '#ea4748',
    '#f2ba41',
    '#d8de1b',
    '#e8734d',
    '#5dc216',
    '#9191D0',
    '#e75a15'
  ] // 颜色列表
  const k = (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
  // 为每一个饼图数据，生成一个 series-surface 配置
  const total = pieData.reduce((total, { value = 0 }) => total + value, 0)
  for (let i = 0; i < pieData.length; i++) {
    const { value = 0, name = '' } = pieData[i]
    endValue = startValue + pieData[i].value
    const startRatio = startValue / total
    const endRatio = endValue / total
    const seriesItem = {
      name,
      type: 'surface',
      itemStyle: {
        opacity,
        color: color[i % 8]
      },
      parametric: true,
      wireframe: {
        show: false
      },
      parametricEquation: getParametricEquation(
        startRatio,
        endRatio,
        false,
        false,
        k,
        value
      ),
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k
      }
    }
    startValue = endValue
    series.push(seriesItem)
  }

  // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
  series.push({
    name: 'mouseoutSeries',
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false
    },
    itemStyle: {
      opacity: 0
    },
    parametricEquation: {
      u: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20
      },
      v: {
        min: 0,
        max: Math.PI,
        step: Math.PI / 20
      },
      x: function(u, v) {
        return Math.sin(v) * Math.sin(u) + Math.sin(u)
      },
      y: function(u, v) {
        return Math.sin(v) * Math.cos(u) + Math.cos(u)
      },
      z: function(u, v) {
        return Math.cos(v) > 0 ? 0.1 : -0.1
      }
    }
  })

  return series
}

// 【 getParametricEquation 函数说明 】 :
// *************************
//   根据传入的
//   startRatio（浮点数）: 当前扇形起始比例，取值区间[0, endRatio)
//   endRatio（浮点数）: 当前扇形结束比例，取值区间(startRatio, 1]
//   isSelected（布尔值）: 是否选中，效果参照二维饼图选中效果（单选）
//   isHovered（布尔值）: 是否放大，效果接近二维饼图高亮（放大）效果（未能实现阴影）
//   k（0~1之间的浮点数）：用于参数方程的一个参数，取值 0~1 之间，通过「内径 / 外径」的值换算而来。
//   生成 3D 扇形环曲面
//   *************************
export function getParametricEquation(
  startRatio,
  endRatio,
  isSelected,
  isHovered,
  k,
  h
) {
  // 计算
  const midRatio = (startRatio + endRatio) / 2
  const startRadian = startRatio * Math.PI * 2
  const endRadian = endRatio * Math.PI * 2
  const midRadian = midRatio * Math.PI * 2

  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== 'undefined' ? k : 1 / 3

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  const hoverRate = isHovered ? 1.05 : 1

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20
    },

    x: function(u, v) {
      if (u < startRadian) {
        return (
          offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        )
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    y: function(u, v) {
      if (u < startRadian) {
        return (
          offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        )
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
    },

    z: function(u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u)
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
    }
  }
}

// 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
let selectedIndex = ''
let hoveredIndex = ''

export function bindMouseEvent(myChart, option) {
  // 监听点击事件，实现选中效果（单选）
  myChart.on('click', function(params) {
    // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
    const isSelected = !option.series[params.seriesIndex].pieStatus.selected
    const isHovered = option.series[params.seriesIndex].pieStatus.hovered
    const k = option.series[params.seriesIndex].pieStatus.k
    const startRatio = option.series[params.seriesIndex].pieData.startRatio
    const endRatio = option.series[params.seriesIndex].pieData.endRatio

    // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
    if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {
      option.series[selectedIndex].parametricEquation = getParametricEquation(
        option.series[selectedIndex].pieData.startRatio,
        option.series[selectedIndex].pieData.endRatio,
        false,
        false,
        k,
        option.series[selectedIndex].pieData.value
      )
      option.series[selectedIndex].pieStatus.selected = false
    }

    // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
    option.series[params.seriesIndex].parametricEquation =
      getParametricEquation(
        startRatio,
        endRatio,
        isSelected,
        isHovered,
        k,
        option.series[selectedIndex].pieData.value
      )
    option.series[params.seriesIndex].pieStatus.selected = isSelected

    // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
    isSelected ? (selectedIndex = params.seriesIndex) : null

    // 使用更新后的 option，渲染图表
    myChart.setOption(option)
  })

  // 监听 mouseover，近似实现高亮（放大）效果
  myChart.on('mouseover', function(params) {
    // 准备重新渲染扇形所需的参数
    let isSelected
    let isHovered
    let startRatio
    let endRatio
    let k

    // 如果触发 mouseover 的扇形当前已高亮，则不做操作
    if (hoveredIndex === params.seriesIndex) {
      return

      // 否则进行高亮及必要的取消高亮操作
    } else {
      // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
      if (hoveredIndex !== '') {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
        isSelected = option.series[hoveredIndex].pieStatus.selected
        isHovered = false
        startRatio = option.series[hoveredIndex].pieData.startRatio
        endRatio = option.series[hoveredIndex].pieData.endRatio
        k = option.series[hoveredIndex].pieStatus.k

        // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
        option.series[hoveredIndex].parametricEquation = getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          option.series[hoveredIndex].pieData.value
        )
        option.series[hoveredIndex].pieStatus.hovered = isHovered

        // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
        hoveredIndex = ''
      }

      // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
      if (params.seriesName !== 'mouseoutSeries') {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
        isSelected = option.series[params.seriesIndex].pieStatus.selected
        isHovered = true
        startRatio = option.series[params.seriesIndex].pieData.startRatio
        endRatio = option.series[params.seriesIndex].pieData.endRatio
        k = option.series[params.seriesIndex].pieStatus.k

        // 对当前点击的扇形，执行高亮操作（对 option 更新）
        option.series[params.seriesIndex].parametricEquation =
          getParametricEquation(
            startRatio,
            endRatio,
            isSelected,
            isHovered,
            k,
            option.series[params.seriesIndex].pieData.value + 5
          )
        option.series[params.seriesIndex].pieStatus.hovered = isHovered

        // 记录上次高亮的扇形对应的系列号 seriesIndex
        hoveredIndex = params.seriesIndex
      }

      // 使用更新后的 option，渲染图表
      myChart.setOption(option)
    }
  })

  // 修正取消高亮失败的 bug
  myChart.on('globalout', function() {
    // 准备重新渲染扇形所需的参数
    let isSelected
    let isHovered
    let startRatio
    let endRatio
    let k
    if (hoveredIndex !== '') {
      // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
      isSelected = option.series[hoveredIndex].pieStatus.selected
      isHovered = false
      k = option.series[hoveredIndex].pieStatus.k
      startRatio = option.series[hoveredIndex].pieData.startRatio
      endRatio = option.series[hoveredIndex].pieData.endRatio

      // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
      option.series[hoveredIndex].parametricEquation = getParametricEquation(
        startRatio,
        endRatio,
        isSelected,
        isHovered,
        k,
        option.series[hoveredIndex].pieData.value
      )
      option.series[hoveredIndex].pieStatus.hovered = isHovered

      // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
      hoveredIndex = ''
    }

    // 使用更新后的 option，渲染图表
    myChart.setOption(option)
  })
}
