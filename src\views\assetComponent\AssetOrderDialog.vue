<template>
  <el-dialog :visible.sync="dialogVisible" custom-class="custom-dialog" :show-close="false" :close-on-click-modal="false">
    <div slot="title">
      <div class="title">区域资产数量排名</div>
      <img class="close-btn" src="@/assets/image/trading/close.png" alt="" @click.stop="handleClose" />
    </div>

    <el-form inline>
      <el-form-item label="当前地区">
        <el-select v-model="queryParams.areaName" popper-class="area_tree" style="width: 240px" :title="queryParams.areaName" :popper-append-to-body="false">
          <el-option :value="treeNodeAreaName" style="height: auto; padding: 0">
            <area-tree :is-lazy-load="true" :tree-data="treeData" :expanded-nodes="expandedNodes" @loadChildNode="loadChildNode" @selectedNodeChange="handleNodeChange" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="统计级别" style="padding-left: 10px">
        <el-select v-model="queryParams.areaLevel" style="width: 90px" :popper-append-to-body="false">
          <el-option v-for="item in areaLevelOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="资产类别" style="padding-left: 10px">
        <el-select v-model="queryParams.assetCategory" style="width: 130px" :popper-append-to-body="false">
          <el-option label="资源性资产" value="Z"></el-option>
          <el-option label="经营性资产" value="J"></el-option>
          <el-option label="非经营性资产" value="F"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" class="search-btn" @click="getData">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="showTableData[currentPage - 1]"
      :height="tableHeight"
      stripe
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.3)"
    >
      <el-table-column label="排名" width="80" prop="total" align="center" header-align="center">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="地区" prop="name" align="center" header-align="center"></el-table-column>
      <el-table-column label="数量" prop="num" align="center" header-align="center">
        <template slot-scope="scope">{{ scope.row.num == null ? '' : Number(scope.row.num).toLocaleString() }}宗</template>
      </el-table-column>
      <el-table-column label="占比" prop="percent" align="center" header-align="center">
        <template slot-scope="scope">{{ scope.row.percent }}%</template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination :current-page="currentPage" :page-size="20" layout="prev, pager, next, jumper" :total="tableData.length" @current-change="handleCurrentChange"></el-pagination>
    </div>
  </el-dialog>
</template>
<script>
import AreaTree from './AreaTree.vue'
import { userTreeData, getTotalAssetRankList } from '@/api/asset'
export default {
  components: { AreaTree },
  props: {
    year: {
      type: String,
      default: ''
    }
  },
  data() {
    this.areaLevelEnum = [
      { text: '市级', id: '1' },
      { text: '区级', id: '2' },
      { text: '镇级', id: '3' },
      { text: '村级', id: '4' }
    ]
    return {
      dialogVisible: false,
      loading: false,
      tableData: [],
      treeData: [],
      tableHeight: 0,
      treeNodeAreaName: '',
      areaLevelOptions: [
        { text: '市级', id: '1' },
        { text: '区级', id: '2' },
        { text: '镇级', id: '3' },
        { text: '村级', id: '4' }
      ],
      queryParams: {
        year: this.year,
        areaCode: 'D4406',
        areaName: '佛山市',
        areaLevel: '1',
        assetCategory: 'Z'
      },
      currentPage: 1,
      showTableData: [[]]
    }
  },
  computed: {
    expandedNodes() {
      const ids = []
      if (this.treeData.length > 0) {
        this.treeData.forEach((levelOneNode) => {
          ids.push(levelOneNode.id)
        })
      }
      return ids
    }
  },
  methods: {
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.currentPage = val
    },
    handleClose() {
      this.dialogVisible = false
      this.initData()
    },
    initData() {
      this.tableData = []
      this.treeData = []
      this.tableHeight = 0
      this.currentPage = 1
      this.treeNodeAreaName = ''
      this.areaLevelOptions = [
        { text: '市级', id: '1' },
        { text: '区级', id: '2' },
        { text: '镇级', id: '3' },
        { text: '村级', id: '4' }
      ]
      this.queryParams = {
        areaCode: 'D4406',
        areaName: '佛山市',
        areaLevel: '1',
        assetCategory: 'Z'
      }
    },
    show() {
      this.dialogVisible = true
      this.getUserTypeAll()
      this.getData()
      this.$nextTick(() => {
        const dialog = document.querySelector('.custom-dialog').querySelector('.el-dialog__body')
        const form = dialog.querySelector('.el-form')
        this.tableHeight = dialog.clientHeight - 100 - form.clientHeight
      })
    },
    getData() {
      this.loading = true
      this.queryParams.year = this.year
      getTotalAssetRankList(this.queryParams)
        .then((res) => {
          this.tableData = res.data
          const total = this.tableData.length
          const totalPage = total % 20 ? Math.floor(total / 20) + 1 : total / 20
          this.showTableData = [[]]
          const showTableData = []
          for (let index = 0; index < totalPage; index++) {
            const startIndex = index ? index * 20 : 0
            const endIndex = index ? index * 20 + 20 : 20
            showTableData.push(this.tableData.slice(startIndex, endIndex))
          }
          this.showTableData = showTableData
        })
        .finally(() => {
          this.loading = false
        })
    },
    async getUserTypeAll() {
      const { data: treeData } = await userTreeData({ areaCode: this.areaCode })
      this.treeData = treeData
    },
    handleNodeChange(data) {
      const level = data.attributes.level - 1
      this.areaLevelOptions = this.areaLevelEnum.filter((cur) => cur.id >= level)
      this.$set(this.queryParams, 'areaName', data.text)
      this.$set(this.queryParams, 'areaCode', data.code)
      this.$set(this.queryParams, 'areaLevel', level.toString())
    },
    async loadTree() {
      const params = {}
      if (arguments[0]) {
        params.id = arguments[0]
      }
      return await userTreeData(params.id)
    },
    loadChildNode({ id }, resolve) {
      this.loadTree({ id }).then((res) => {
        resolve(res.data)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-table {
  td,
  th {
    font-size: 14px;
    color: #4fd9fc !important;
  }
  .el-table__body-wrapper {
    &::-webkit-scrollbar {
      width: 10px !important;
      height: 100% !important;
    }
    &::-webkit-scrollbar-track {
      background-color: rgba(62, 167, 225, 0.1) !important;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 5px !important;
      background-color: rgba(62, 167, 225, 0.2) !important;
    }
  }
  .el-table__header {
    .has-gutter {
      tr {
        height: 46px;
      }
    }
  }
}
::v-deep .pagination {
  margin-top: 20px;
  text-align: right;
  .el-pagination,
  .el-pager li.btn-quickprev,
  .el-pager li.btn-quicknext {
    color: rgb(255, 255, 255, 0.5);
    .btn-prev,
    .btn-next,
    .el-pagination__jump {
      margin-top: 2px;
      font-size: 14px;
      color: rgb(255, 255, 255, 0.5);
      background-color: transparent;
    }
    .el-pagination__editor {
      margin: 0 5px;
    }
    .el-input__inner {
      background-color: transparent;
      font-size: 14px;
      color: rgb(255, 255, 255, 0.5);
      border-radius: 2px;
      border-color: rgb(255, 255, 255, 0.5);
    }
  }
  .el-pagination .btn-prev .el-icon,
  .el-pagination .btn-next .el-icon {
    font-size: 14px;
  }
  .el-pagination button:disabled {
    background-color: transparent;
  }
  .el-dialog,
  .el-pager li {
    height: 32px;
    line-height: 32px;
    background-color: transparent;
    font-size: 16px;
    width: 26px;
    border: solid rgb(192, 196, 204, 0.5) 1px;
    margin: 0 5px;
    border-radius: 3px;
    font-weight: lighter;
    &.active,
    &:hover {
      color: rgb(66, 217, 236);
      border: solid rgb(66, 217, 236) 1px;
    }
  }
}

::v-deep .el-select-dropdown {
  background-color: #0d3a897d !important;
}
::v-deep .custom-dialog {
  background: #132647;
  box-shadow: inset 0 0 60px #3d61a4;
  .el-dialog__header {
    position: relative;
    .title {
      color: #fff;
      font-size: 22px;
      padding-left: 50px;
      background: url('@/assets/image/trading/title.png') 0 0 no-repeat;
    }
    .close-btn {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .el-dialog__body {
    height: 500px;
    .el-form {
      .el-form-item__label {
        color: #3ea7e1;
        font-size: 16px;
      }
      .el-date-editor {
        background: linear-gradient(to bottom, #005ba8, #002d59) !important;
        border-color: #3ea7e1 !important;
        .el-icon-date {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range-input {
          color: #3ea7e1 !important;
          background: transparent;
          border-color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range-separator {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range__close-icon {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-select__caret {
          color: #3ea7e1 !important;
        }
        .popper__arrow {
          display: none;
        }
        .el-picker-panel {
          background-color: #3ea7e1 !important;
          color: #fff !important;
          .el-picker-panel__icon-btn,
          .el-date-table th,
          .el-date-table td {
            color: #fff !important;
          }
          .el-date-table td.in-range {
            color: #3ea7e1 !important;
          }
        }
      }

      .search-btn {
        background-color: #71deff;
        border-radius: 5px;
        color: #13264a;
        font-size: 16px;
        padding: 4px 15px;
      }
    }
    .el-table {
      background-color: transparent;
      border: none;
      &::before {
        display: none;
      }
      .el-table__body-wrapper {
        &::-webkit-scrollbar {
          width: 0;
          height: 0;
          border: none;
        }
      }
      th.gutter {
        display: none !important;
        width: 0 !important;
      }

      colgroup col[name='gutter'] {
        display: none;
        width: 0;
      }

      .el-table__body {
        width: 100% !important;
      }

      th,
      td {
        border: none;
      }
      th {
        color: #3ea7e1;
        background-color: #02398b;
      }
      tr,
      td {
        color: #3ea7e1;
        background-color: transparent;
      }
      tr.el-table__row--striped td {
        background-color: rgba(26, 58, 96, 0.5);
      }
    }
  }
}
</style>

<style lang="scss">
::v-deep .popper__arrow {
  display: none !important;
}
</style>
