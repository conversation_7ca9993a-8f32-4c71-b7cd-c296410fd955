<!--
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-08-18 13:44:33
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-01 19:24:34
-->
<template>
  <div id="lineEcharts" ref="lineRefs" class="line-Box"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  components: {},
  props: {
    echartData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      legend: []
    }
  },
  watch: {
    echartData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.$nextTick(() => {
          this.initEcharts()
        })
      }
    },
    type: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal == 'bidProjectTotal') {
          this.legend = ['发布项目', '成交项目']
        } else if (newVal == 'projectAmount') {
          this.legend = ['成交总金额', '溢价总金额']
        } else {
          this.legend = []
        }
      }
    }
  },
  created() {},
  methods: {
    initEcharts() {
      const lineEcharts = document.getElementById('lineEcharts')
      this.myChart = echarts.init(lineEcharts)
      /* 要展示的数据 */
      const seriesView = {
        bidProjectTotal: [
          {
            name: '发布项目',
            data: this.echartData.seriesData,
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#0deec7'
            },
            symbolSize: 0
          },
          {
            name: '成交项目',
            data: this.echartData.seriesData2,
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#f1c92f'
            },
            symbolSize: 0
          }
        ],
        projectAmount: [
          {
            name: '成交总金额',
            data: this.echartData.seriesData,
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#0deec7'
            },
            symbolSize: 0
          },
          {
            name: '溢价总金额',
            data: this.echartData.seriesData3,
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#f1c92f'
            },
            symbolSize: 0
          }
        ],
        servicePersonNumber: [
          {
            data: this.echartData.seriesData,
            type: 'line',
            smooth: true,
            lineStyle: {
              color: '#0deec7'
            },
            symbolSize: 0
          }
        ]
      }
      const option = {
        tooltip: {
          trigger: 'axis',
          position: (point, params, dom, rect, size) => {
            const options = {
              point,
              params,
              dom,
              rect,
              size
            }
            return this.position(options)
          },
          formatter: (params) => {
            let str = ''
            for (const key in params) {
              if (['bidProjectTotal'].includes(this.type)) {
                str += `${params[key].axisValueLabel}<br />${params[key].marker}${params[key].seriesName}&nbsp;&nbsp;&nbsp;&nbsp;${params[key].value + '宗'}<br />`
              } else if (['projectAmount'].includes(this.type)) {
                str += `${params[key].axisValueLabel}<br />${params[key].marker}${params[key].seriesName}&nbsp;&nbsp;&nbsp;&nbsp;${params[key].value.toFixed(2) + '元'}<br />`
              } else {
                str = `${params[key].axisValueLabel}<br />${params[key].marker}&nbsp;&nbsp;&nbsp;&nbsp;${params[key].value}`
              }
            }
            return str
          }
        },
        grid: {
          top: '60'
        },
        color: ['#0deec7', '#f1c92f'],
        legend: {
          top: '15',
          data: this.legend,
          textStyle: {
            color: '#fff',
            align: 'right'
          },
          icon: 'rect',
          itemHeight: 5,
          itemWidth: 15
        },
        xAxis: {
          nameGap: 20,
          type: 'category',
          data: this.echartData.xAisData,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#65ABE7',
              fontSize: 14
            }
          },
          axisPointer: {
            type: 'none'
          }
        },
        yAxis: {
          nameGap: 20,
          type: 'value',
          axisLine: {
            show: false
          },
          splitLine: {
            lineStyle: {
              width: 2,
              color: '#123d72'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#65ABE7',
              fontSize: 14
            }
          }
        },
        series: seriesView[this.type]
      }
      this.myChart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.line-Box {
  margin: 0px auto;
  width: 100%;
  height: 100%;
}
</style>
