<template>
  <div class="com-asset-info">
    <ul v-if="showDetails" class="block-details">
      <li v-for="(item, key) in datas" :key="key" class="block-body">
        <img class="img" :src="icons[item.icon]" alt="" />
        <div class="detail-info">
          <div class="title">{{ item.title }}</div>
          <div class="value">
            {{ Number(item.value).toLocaleString() }}
            <font style="font-size: 12px">{{ item.unit }}</font>
          </div>
        </div>
      </li>
    </ul>
    <div class="btns">
      <div v-for="item in btns" :key="item.id" class="radio-btn" :class="dateValue == item.id ? 'selected' : ''" @click="handleBtn(item.id)">
        {{ item.text }}
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'AssetInfo',
  props: {
    type: {
      type: String,
      default: ''
    },
    showDetails: {
      type: Boolean,
      default: true
    },
    propValue: {
      type: String,
      default: ''
    },
    datas: {
      type: Array,
      default: () => []
    },
    btns: {
      type: Array,
      default: () => [
        {
          text: '主要类型情况',
          id: '1'
        },
        {
          text: '使用现状（按数量）',
          id: '2'
        },
        {
          text: '使用现状（按面积）',
          id: '3'
        }
      ]
    }
  },
  data() {
    return {
      icons: {
        total: require('@/assets/image/asset/icon1.png'),
        area: require('@/assets/image/asset/icon2.png')
      },
      dateValue: 1
    }
  },
  watch: {
    propValue: {
      handler(val) {
        this.dateValue = val
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleBtn(val) {
      this.dateValue = val
      this.$emit('updateValue', { val, key: this.type })
    }
  }
}
</script>
<style lang="scss" scoped>
.com-asset-info {
  .block-details {
    display: flex;
    justify-content: space-between;
    padding: 0 0 20px 0;
    .block-body {
      width: calc(50% - 20px);
      height: 83px;
      display: flex;
      align-items: center;
      background: url('@/assets/image/asset/image1.png') 0 0 no-repeat;
      background-size: 100% 100%;
      padding: 10px;
      box-sizing: border-box;
      .img {
        width: 24%;
        margin: 20px;
      }
      .detail-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        .value {
          font-size: 14px;
          color: #fff;
        }
        .title {
          font-size: 14px;
          color: #fff;
        }
        .value {
          font-size: 18px;
          font-weight: bolder;
          background: linear-gradient(to top, #28b7ff, #fff);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .btns {
    .radio-btn {
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      border-radius: 5px;
      color: #9fd7ff;
      border: 1px solid #8697e9;
      padding: 3px 8px;
      margin-left: 10px;
      background: linear-gradient(rgb(1, 78, 106), rgb(54, 91, 90));
      // background: linear-gradient(to bottom, #014e6a, #087997);
      &.selected {
        color: #2fd0ca;
        border: 1px solid #2fd0ca;
        background: radial-gradient(to top, #0f605d, #2fd0ca);
      }
    }
  }
}
</style>
