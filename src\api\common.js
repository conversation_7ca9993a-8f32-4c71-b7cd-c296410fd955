/*
 * @Descripttion:GEVER公共通用方法
 * @version:
 * @Author: Lantresor
 * @Date: 2021-10-13 13:46:21
 * @LastEditors: Peng Xiao
 * @LastEditTime: 2022-05-11 14:39:16
 */
import request from '@/utils/request'

/**
 * @name:获取字典
 * @param {*} data
 * @description:
 * @return {*}
 * @test:
 * @msg:
 */
// 测试接口
export function getData() {
  return request({
    url: `/asset/leaderAssetInventory/getDate`,
    method: 'post'
  })
}
// 工作进度分析
export function getProgress(data) {
  return request({
    url: `/asset/leaderAssetInventory/workProgressAnalysis`,
    data: data,
    method: 'post'
  })
}
// 每周工作动态下半部分
export function dynamic(data) {
  return request({
    url: `/asset/leaderAssetInventory/assetSituation`,
    data: data,
    method: 'post'
  })
}
// 每周工作动态上半部分
export function getProgressTop(data) {
  return request({
    url: `/asset/leaderAssetInventory/workTrends`,
    data: data,
    method: 'post'
  })
}
// 各单位工作进度
export function getJindu(data) {
  return request({
    url: `/asset/leaderAssetInventory/unitsWorkProgress`,
    data: data,
    method: 'post'
  })
}
