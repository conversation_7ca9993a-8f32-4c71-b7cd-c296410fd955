/*
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-08-21 15:11:43
 * @LastEditors: Andy
 * @LastEditTime: 2023-08-21 15:20:15
 */
export default {
  methods: {
    position ({ point, params, dom, rect, size }) {
      // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
      // 提示框位置
      let x = 0 // x坐标位置
      let y = 0 // y坐标位置

      // 当前鼠标位置
      const pointX = point[0]
      const pointY = point[1]

      // 提示框大小
      const boxWidth = size.contentSize[0]
      const boxHeight = size.contentSize[1]

      // boxWidth > pointX 鼠标左侧放不下提示框
      if (boxWidth > pointX) {
        x = 5
      } else {
        x = pointX - boxWidth
      }

      // boxHeight > pointY 鼠标顶部放不下提示框
      if (boxHeight > pointY) {
        y = 5
      } else {
        y = pointY - boxHeight
      }
      return [x, y]
    }
  }
}
