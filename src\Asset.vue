<template>
  <div class="container">
    <div class="left-container">
      <div class="top block-box" @mouseenter.stop="handleMouseEnter('Z')" @mouseleave.stop="handleMouseLeave('Z')">
        <div class="block-title">资源性资产</div>
        <div class="block-body">
          <AssetInfo :datas="assetCategory.z" type="z" :prop-value="params.z" @updateValue="changeCategoryType" />
          <div id="chart1_1" class="block-body p0" ></div>
        </div>
      </div>
      <div class="bottom block-box" @mouseenter.stop="handleMouseEnter('J')" @mouseleave.stop="handleMouseLeave('J')">
        <div class="block-title">经营性资产</div>
        <div class="block-body">
          <AssetInfo :datas="assetCategory.j" type="j" :prop-value="params.j" @updateValue="changeCategoryType" />
          <div id="chart1_2" class="block-body p0"></div>
        </div>
      </div>
    </div>
    <div class="center-container">
      <div class="top-container">
        <div class="tooltip">
          <div class="info">
            <div class="area-org">
              <img class="area-img" src="@/assets/image/icon1.png" alt="" />
              <span class="area-title" style="font-size: 18px">当前地区：</span>
              <span class="area" style="font-size: 24px">| {{ assetCategory.regionName }} |</span>
            </div>
            <div class="area-time">
              <img class="area-img" src="./assets/image/date.png" alt="" />
              <span class="area-title" style="font-size: 18px">统计截止时间：</span>
              <span class="area" style="font-size: 18px">{{ assetCategory.statisticsTime }}</span>
            </div>
          </div>
          <div class="info" style="margin-top: 20px">
            <div></div>
            <div style="display: flex">
              <el-select v-model="queryParams.year" style="width: 100px" placeholder="" :popper-append-to-body="false" @change="getPageData">
                <el-option v-for="item in yearOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
              <el-select
                v-if="dateValue == '2'"
                v-model="queryParams.quarter"
                style="width: 120px; margin-left: 10px"
                placeholder=""
                :popper-append-to-body="false"
                @change="getPageData"
              >
                <el-option v-for="item in quarterOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
              <el-select
                v-if="dateValue == '3'"
                v-model="queryParams.month"
                style="width: 100px; margin-left: 10px"
                placeholder=""
                :popper-append-to-body="false"
                @change="getPageData"
              >
                <el-option v-for="item in monthOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
<!-- del by ymk 屏蔽年季月按钮 -->
<!--              <span v-for="item in dateOptions" :key="item.id" class="radio-btn" :class="dateValue == item.id ? 'selected' : ''" @click="handleChangeDate(item)">-->
<!--                {{ item.text }}-->
<!--              </span>-->
            </div>
          </div>
        </div>
        <mapNav class="map" :area-code="queryParams.areaCode" @gotopath="gotopath" />
      </div>
      <div class="bottom-container">
        <div class="top">
          <div>资产总宗数</div>
          <number-flash-copy :number="Number(assetCategory.assetNum)" :precision="0" />
          <div>宗</div>
        </div>
        <div class="bottom center-bottom">
          <div class="block-box">
            <div class="block-body">
              <img class="img" src="@/assets/image/asset/icon3.png" alt="" />
              <div class="detail-info">
                <div class="value">
                  {{ assetCategory.villageBuildingNum?Number(assetCategory.villageBuildingNum).toLocaleString():assetCategory.villageBuildingNum }}
                  <span class="unit">个</span>
                </div>
                <div class="title">涉农村居数</div>
              </div>
            </div>
          </div>
          <div class="block-box">
            <div class="block-body">
              <img class="img" src="@/assets/image/asset/icon4.png" alt="" />
              <div class="detail-info">
                <div class="value">
                  {{ assetCategory.groupOrgNum?Number(assetCategory.groupOrgNum).toLocaleString():assetCategory.groupOrgNum }}
                  <span class="unit">个</span>
                </div>
                <div class="title font14">农村集体经济组织(机构)数</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-container">
      <div class="top block-box" @mouseenter.stop="handleMouseEnter('F')" @mouseleave.stop="handleMouseLeave('F')">
        <div class="block-title">非经营性资产</div>
        <div class="block-body">
          <AssetInfo :datas="assetCategory.f" type="f" :prop-value="params.f" @updateValue="changeCategoryType" />
          <div id="chart3_1" class="block-body p0"></div>
        </div>
      </div>
      <div class="bottom block-box">
        <div class="block-title">区域资产数量排名</div>
        <div class="date-box">
          <span
            v-for="item in areaTypeOptions"
            :key="item.id"
            class="radio-btn"
            :class="params.areaType == item.id ? 'selected' : ''"
            @click="changeCategoryType({ val: item.id, key: 'areaType' })"
          >
            {{ item.text }}
          </span>
        </div>
        <div id="chart1_3" class="block-body">
          <AssetInfo :show-details="false" :btns="btns" :prop-value="params.categoryType" type="categoryType" @updateValue="changeCategoryType" />
          <div id="rank-list-box" @mouseenter.stop="handleMouseEnter('C')" @mouseleave.stop="handleMouseLeave('C')">
            <vue-seamless-scroll :data="rankList" class="rank-list" :class-option="scrollConfig" :style="{ height: rankBoxHeight + 'px', maxHeight: rankBoxHeight + 'px' }">
              <ul>
                <li v-for="(item, index) in rankList" :key="index" class="rank-item" :class="index === 0 ? 'level1' : index <= 2 ? 'level2' : 'level3'">
                  <span class="rank-order">{{ index + 1 }}</span>
                  <span class="area-name">{{ item.name }}</span>
                  <div class="rank-progress">
                    <span :style="{ width: item.percent + '%', maxWidth: 'calc(100% - 4px)' }">
                      <span :style="{ marginLeft: Number.parseFloat(item.percent >= 78 ? 78 : item.percent) + '%' }">{{ item.percent.toFixed(2) }}%</span>
                    </span>
                  </div>
                  <span class="value">{{ (item.formatNum && item.unit) ? Number(item.formatNum).toLocaleString() + ' ' + item.unit:Number(item.formatNum).toLocaleString() }}</span>
                </li>
              </ul>
            </vue-seamless-scroll>
          </div>
        </div>
        <p class="view-more" @click="handleOrderMore"><span>查看更多排名></span></p>
      </div>
    </div>
    <!-- 区域资产数量排名弹窗 -->
    <AssetOrderDialog ref="_assetOrderDialogRef" :year="queryParams.year"/>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import mapNav from './views/mapNav.vue'
import NumberFlashCopy from '@/views/NumberFlashCopy'
import AssetInfo from '@/views/assetComponent/AssetInfo.vue'
import AssetOrderDialog from '@/views/assetComponent/AssetOrderDialog.vue'
import vueSeamlessScroll from 'vue-seamless-scroll'
import { baseData, chartData, getAssetRankList, yearList } from '@/api/asset'
import { changeMoney, getLastFormatDate } from '@/utils/common'
export default {
  components: {
    mapNav,
    AssetInfo,
    NumberFlashCopy,
    vueSeamlessScroll,
    AssetOrderDialog
  },
  data() {
    this.assetType = ['Z', 'J', 'F', 'C']
    this.assetTypeMap = [
      {
        text: '主要类型情况',
        id: '1'
      },
      {
        text: '使用现状（按数量）',
        id: '2'
      },
      {
        text: '使用现状（按面积）',
        id: '3'
      }
    ]
    this.btns = [
      {
        text: '资产总数',
        id: 'totalNum'
      },
      {
        text: '资源性资产',
        id: 'resourceNum'
      },
      {
        text: '经营性资产',
        id: 'businessNum'
      },
      {
        text: '非经营性资产',
        id: 'nonBusinessNum'
      }
    ]
    this.categoryTypeEnum = {
      1: 'mainKind',
      2: 'situationNum',
      3: 'situationArea'
    }
    this.colors = ['#fed130', '#13c2c2', '#368aff', '#fc5659', '#1fb5fc']
    return {
      rankBoxHeight: 0,
      records: require('./views/mapData/foshan_area_data.json'),
      queryParams: {
        areaCode: 'D4406', // 地区编码
        year: '', // 年份
        month: '', // 月份
        quarter: '' // 季度
      },
      yearOptions: [],
      quarterOptions: [
        { id: '1', text: '第一季度' },
        { id: '2', text: '第二季度' },
        { id: '3', text: '第三季度' },
        { id: '4', text: '第四季度' }
      ],
      dateOptions: [
        { id: '1', text: '年' },
        { id: '2', text: '季度' },
        { id: '3', text: '月' }
      ],
      areaTypeOptions: [
        { id: 'qu', text: '区级' },
        { id: 'zhen', text: '镇级' },
        { id: 'cun', text: '村级' }
      ],
      monthOptions: [
        { id: '1', text: '1月' },
        { id: '2', text: '2月' },
        { id: '3', text: '3月' },
        { id: '4', text: '4月' },
        { id: '5', text: '5月' },
        { id: '6', text: '6月' },
        { id: '7', text: '7月' },
        { id: '8', text: '8月' },
        { id: '9', text: '9月' },
        { id: '10', text: '10月' },
        { id: '11', text: '11月' },
        { id: '12', text: '12月' }
      ],
      dateValue: '1',
      data2_1: {
        totalAmount: 301217,
        onlineProjectNum: 200000,
        dealProjectNum: 200000
      },
      assetCategory: {
        areaCode: 'D4406',
        regionName: '佛山市',
        assetNum: 0,
        groupOrgNum: 0,
        statisticsTime: '',
        villageBuildingNum: 0,
        z: [
          {
            title: '资产总宗数',
            key: 'resourceAssetNum',
            value: 0,
            icon: 'total',
            unit: '宗'
          },
          {
            title: '资产总面积',
            key: 'resourceAssetArea',
            value: 0,
            icon: 'area',
            unit: '亩'
          }
        ],
        j: [
          {
            title: '资产总宗数',
            key: 'businessAssetNum',
            value: 0,
            icon: 'total',
            unit: '宗'
          },
          {
            title: '资产总面积',
            key: 'businessAssetArea',
            value: 0,
            icon: 'area',
            unit: '平方米'
          }
        ],
        f: [
          {
            title: '资产总宗数',
            key: 'nonBusinessAssetNum',
            value: 0,
            icon: 'total',
            unit: '宗'
          },
          {
            title: '资产总面积',
            key: 'nonBusinessAssetArea',
            value: 0,
            icon: 'area',
            unit: '平方米'
          }
        ]
      },
      params: {
        areaType: 'qu',
        categoryType: 'totalNum',
        z: '1',
        j: '1',
        f: '1'
      },
      dataAll: {},
      chart1_1: null,
      chart1_2: null,
      chart1_3: null,
      chart3_1: null,
      chart3_2: null,
      data1_1: [],
      data1_2: {
        num: [],
        area: []
      },
      data1_3: [],
      data3_1: [],
      data3_2: {},
      chartType: '',
      timeSetIntervalZ: null,
      timeSetIntervalJ: null,
      timeSetIntervalF: null,
      timeSetIntervalC: null,
      playInterval: 5000,
      timeIndex: 0
    }
  },
  computed: {
    scrollConfig() {
      return {
        step: 0.5,
        limitMoveNum: 7
      }
    },
    rankList() {
      const { areaType, categoryType } = this.params
      const key = `${areaType}_${categoryType}List`
      if (this.data3_2.hasOwnProperty(key)) {
        return this.data3_2[key].map((cur) => {
          const { num, unit } = changeMoney(cur.num, ['宗', '万宗', '亿宗'])
          cur.formatNum = num.toString().length <= 4 ? num : num.toFixed(2)
          cur.unit = unit
          return cur
        })
      }
      return []
    }
  },
  mounted() {
    this.initBaseData()
    yearList().then((res) => {
      this.yearOptions = res.data.map((cur) => ({ id: cur, text: cur + '年' }))
      this.$set(this.queryParams, 'year', res.data[res.data.length - 1])
    })
    this.getDomHeight()
    window.onresize = () => {
      this.chart1_1.resize()
      this.chart1_2.resize()
      this.chart3_1.resize()
    }
  },
  beforeDestroy() {
    for (let index = 0; index < this.assetType.length; index++) {
      const cur = this.assetType[index]
      clearInterval(this[`timeSetInterval${cur}`]) // 清除定时器
      this[`timeSetInterval${cur}`] = null
    }
  },
  methods: {
    startTimerZ() {
      this.timeSetIntervalZ = setInterval(() => {
        this.playSlidesCategoryType('z', 3, this.assetTypeMap)
      }, this.playInterval)
    },
    startTimerJ() {
      this.timeSetIntervalJ = setInterval(() => {
        this.playSlidesCategoryType('j', 3, this.assetTypeMap)
      }, this.playInterval)
    },
    startTimerF() {
      this.timeSetIntervalF = setInterval(() => {
        this.playSlidesCategoryType('f', 3, this.assetTypeMap)
      }, this.playInterval)
    },
    startTimerC() {
      this.timeSetIntervalC = setInterval(() => {
        this.playSlidesCategoryType('categoryType', 4, this.btns)
      }, this.playInterval)
    },
    playSlidesCategoryType(type, maxLen, enumData) {
      let index = enumData.findIndex((cur) => cur.id === this.params[type])
      index++
      if (index >= maxLen) {
        index = 0
      }
      this.changeCategoryType({ val: enumData[index].id, key: type })
    },
    handleMouseEnter(type) {
      this.pauseTimer(type)
    },
    handleMouseLeave(type) {
      this[`startTimer${type}`]()
    },
    pauseTimer(type) {
      if (this[`timeSetInterval${type}`]) {
        clearInterval(this[`timeSetInterval${type}`])
        this[`timeSetInterval${type}`] = null
      }
    },
    startTimerAll() {
      for (let index = 0; index < this.assetType.length; index++) {
        const cur = this.assetType[index]
        if (this.playInterval <= 0 || this[`timeSetInterval${cur}`]) return
        this[`startTimer${cur}`]()
      }
    },
    getDomHeight() {
      const dom = document.getElementById('rank-list-box')
      this.rankBoxHeight = dom.clientHeight - 86
    },
    handleOrderMore() {
      this.$refs._assetOrderDialogRef.show()
    },
    // 获取资产排名数据
    getData3_2() {
      getAssetRankList({year: this.queryParams.year}).then((res) => {
        this.data3_2 = res.data
      })
    },
    initBaseData() {
      const startYear = 2023
      const currentYear = new Date().getFullYear()
      this.$set(this.queryParams, 'year', currentYear)
      for (let i = currentYear; i >= startYear; i--) {
        this.yearOptions.push({
          id: i,
          text: i + '年'
        })
      }
      this.getPageData()
    },
    handleChangeDate(item) {
      if (item.id == this.dateValue) return

      this.dateValue = item.id
      if (item.id == '1' && !this.queryParams.year) {
        this.$set(this.queryParams, 'year', new Date().getFullYear() + '')
        this.$set(this.queryParams, 'quarter', '')
        this.$set(this.queryParams, 'month', '')
      } else if (item.id == '2' && !this.queryParams.quarter) {
        this.$set(this.queryParams, 'quarter', Math.ceil((new Date().getMonth() + 1) / 3) + '')
        this.$set(this.queryParams, 'month', '')
      } else if (item.id == '3' && !this.queryParams.month) {
        this.$set(this.queryParams, 'month', new Date().getMonth() + 1 + '')
        this.$set(this.queryParams, 'quarter', '')
      }
      this.getPageData()
    },
    async getBaseData() {
      await baseData({ areaCode: this.queryParams.areaCode, year: this.queryParams.year  }).then((res) => {
        // 资产总宗数 assetNum
        // 统计截止时间 statisticsTime
        // 涉农村居数 villageBuildingNum
        // 农村集体经济组织数 groupOrgNum
        // 资源性资产
        // 宗数 resourceAssetNum
        // 面积 resourceAssetArea
        // 经营性资产
        // 宗数 businessAssetNum
        // 面积 businessAssetArea
        // 非经营性资产
        // 宗数 nonBusinessAssetNum
        // 面积 nonBusinessAssetArea
        for (const key in res.data) {
          if (['assetNum', 'groupOrgNum', 'villageBuildingNum'].includes(key)) {
            this.assetCategory[key] = res.data[key]
          }
          if (['statisticsTime'].includes(key)) {
            this.assetCategory[key] = getLastFormatDate(res.data[key]);
          }
          for (const ategoryKey in this.assetCategory) {
            if (typeof this.assetCategory[ategoryKey] === 'object') {
              this.assetCategory[ategoryKey].forEach((item) => {
                if (item.key === key) {
                  const isArea = ['businessAssetArea', 'nonBusinessAssetArea', 'resourceAssetArea'].includes(item.key)
                  let units = ['宗', '万宗', '亿宗']
                  if (isArea) {
                    if (ategoryKey === 'z') {
                      units = ['亩', '万亩', '亿亩']
                    } else {
                      units = ['平方米', '万平方米', '亿平方米']
                    }
                  }
                  const { num, unit } = changeMoney(res.data[key], units)
                  item.value = num.toFixed(2)
                  item.unit = unit
                }
              })
            }
          }
        }
      })
      await this.getData()
    },
    getPageData() {
      this.getData3_2()
      this.getBaseData()
    },
    gotopath(type) {
      const current = this.records.records.find((item) => {
        return item.areaCode == `D${type}`
      })
      console.log(current)
      this.assetCategory.regionName = current.areaName
      this.queryParams.areaCode = current.areaCode
      if (type != undefined) {
        this.queryParams.areaCode = 'D' + type
      }

      this.getPageData()
    },
    getData() {
      const { areaCode, year, quarter, month } = this.queryParams
      const params = {
        areaCode: areaCode,
        year,
        quarter: this.dateValue == '2' ? quarter : '',
        month: this.dateValue == '3' ? month : ''
      }
      this.chartType = ''
      chartData(params).then((res) => {
        this.dataAll = res.data
        this.getData1_1()
        this.getData1_2()
        this.getData3_1()
        this.$nextTick(() => {
          this.startTimerAll()
        })
      })
    },
    formatNumber(val) { // add by ymk 添加千分位格式化
      return (val && val.toLocaleString) ? Number(val).toLocaleString() : val
    },
    getData1_1() {
      const key = `Z_${this.categoryTypeEnum[this.params.z]}`
      const isFormatUnit = this.assetCategory.z[1].unit === '万亩' && this.params.z !== '2'
      this.data1_1 =
        this.dataAll?.[key]?.map((cur) => {
          if (isFormatUnit) {
            cur.value = cur.area
          } else if (this.params.z === '2') {
            cur.value = cur.num
          } else {
            cur.value = cur.area
          }
          return cur
        }) ?? []
      const uni = isFormatUnit ? '万亩' : this.params.z === '2' ? '宗' : '亩'
      this.initChart('1_1', this.params.z === '1' ? '75%' : ['45%', '75%'], uni)
    },
    getData3_1() {
      const key = `F_${this.categoryTypeEnum[this.params.f]}`
      this.data3_1 =
        this.dataAll?.[key]?.map((cur) => {
          cur.value = this.params.f === '3' ? cur.area : cur.num
          return cur
        }) ?? []
      const uni = this.params.f === '3' ? '平方米' : '宗'
      this.initChart('3_1', ['45%', '75%'], uni)
    },
    getData1_2() {
      this.data1_2 = null
      const key = `J_${this.categoryTypeEnum[this.params.j]}`
      const data = this.dataAll?.[key] ?? []

      if (this.params.j === '1') {
        const num = []
        const area = []
        data.forEach((item) => {
          num.push(item.num)
          area.push(item.area)
        })
        this.data1_2 = {
          num,
          area
        }
        this.initChart1_2()
      } else {
        this.data1_3 = data?.map((cur) => {
          cur.value = this.params.j === '3' ? cur.area : cur.num
          return cur
        })
        const uni = this.params.j === '3' ? '平方米' : '宗'
        this.initChart1_2('1_3', ['45%', '75%'], uni)
      }
    },
    initChart(dataName, size, unit) {
      const chartDom = document.getElementById('chart' + dataName)
      this[`chart${dataName}`] = echarts.init(chartDom)
      const option = this.chartOption(false, dataName, size, unit)
      this[`chart${dataName}`].setOption(option)
    },
    initChart1_2(dataName, size, unit) {
      const chartDom = document.getElementById('chart1_2')
      this.chart1_2 = echarts.init(chartDom)

      const option = this.chartOption(this.params.j === '1', dataName, size, unit)

      this.chart1_2.setOption(option, true)
    },
    changeCategoryType({ val, key }) {
      this.$set(this.params, key, val)
      this.chartType = key
      if (key === 'z') {
        this.getData1_1()
      }
      if (key === 'f') {
        this.getData3_1()
      }
      if (key === 'j') {
        this.getData1_2()
      }
    },
    padNumber(num, length) {
      var str = '' + num
      while (str.length < length) {
        str = str + '0'
      }
      return str * 1
    },
    chartOption(isBar, dataName, size, unit) {
      if (isBar) {
        const unit = this.assetCategory.j[1].unit
        const area = unit === '平方米' ? this.data1_2.area : this.data1_2.area.map((cur) => cur || 0)
        const numMax = Math.ceil(Math.max(...this.data1_2.num))
        const areaMax = Math.ceil(Math.max(...area))
        const numLen = numMax.toString().length
        const firstNum = numMax.toString()[0] * 1 + 1
        const areaLen = areaMax.toString().length
        const firstArea = areaMax.toString()[0] * 1 + 1
        return {
          grid: {
            left: '10',
            right: '10',
            bottom: '10',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'none'
            },
            backgroundColor: 'rgba(2,68,144,.7)',
            borderColor: 'transparent',
            textStyle: {
              color: '#fff',
              fontSize: 14
            }
          },
          legend: {
            top: 20,
            itemGap: 50,
            itemWidth: 12,
            itemHeight: 12,
            selectedMode: false,
            data: ['总宗数', '总面积'],
            textStyle: {
              color: '#fff',
              fontSize: 14
            }
          },
          xAxis: [
            {
              type: 'category',
              data: ['商铺', '厂房', '仓库', '其他'],
              axisLabel: {
                inside: false,
                color: '#087997'
              },
              axisTick: {
                show: false
              },
              axisLine: {
                lineStyle: {
                  color: '#014e6a' // 这里设置 Y 轴刻度线的颜色为红色
                }
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '单位: 宗',
              min: 0,
              max: this.padNumber(firstNum, numLen),
              interval: this.padNumber(firstNum, numLen) / 5,
              nameTextStyle: {
                color: '#2889fe',
                fontSize: 14,
                align: 'center'
              },
              axisTick: {
                show: true
              },
              axisLabel: {
                formatter: '{value}',
                color: '#2889fe'
              },
              axisLine: {
                lineStyle: {
                  color: '#014e6a'
                }
              },
              splitLine: {
                lineStyle: {
                  color: '#2889fe'
                }
              }
            },
            {
              type: 'value',
              name: `单位: ${unit}`,
              min: 0,
              max: this.padNumber(firstArea, areaLen),
              interval: this.padNumber(firstArea, areaLen) / 5,
              nameTextStyle: {
                color: '#2889fe',
                fontSize: 14,
                align: this.queryParams.areaCode.length >= 7 ? 'right' : 'center'
              },
              axisLabel: {
                formatter: '{value}',
                color: '#2889fe'
              },
              axisLine: {
                lineStyle: {
                  color: '#014e6a'
                }
              },
              splitLine: {
                lineStyle: {
                  color: '#014e6a'
                }
              },
              axisTick: {
                show: true // 隐藏刻度线
              }
            }
          ],
          series: [
            {
              name: '总宗数',
              type: 'bar',
              barWidth: 20,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0.5, color: '#50a8f7' },
                  { offset: 1, color: '#0a3475' }
                ])
              },
              data: this.data1_2.num,
              yAxisIndex: '0'
            },
            {
              name: '总面积',
              type: 'bar',
              barWidth: 20,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0.5, color: '#00e5b2' },
                  { offset: 1, color: '#0d6c91' }
                ])
              },
              data: area.map((cur) => cur.toFixed(2) * 1),
              yAxisIndex: '1'
            }
          ]
        }
      }
      return {
        grid: {
          left: '0',
          right: '2%',
          containLabel: true
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff', // 自定义文字颜色
            fontSize: 14 // 自定义文字大小
          },
          formatter: (e) => this.formatterTooltip(e, dataName, unit)
        },
        legend: {
          orient: 'vertical',
          left: dataName === '1_1' && this.params.z === '1' ? '45%' : '50%',
          right: 0,
          top: 'center',
          bottom: 'center',
          selectedMode: false,
          // eslint-disable-next-line complexity
          formatter: (e) => {
            const current = this[`data${dataName}`].find((i) => i.name === e)
            let name = e
            if (name === '建设用地(不包含宅基地)') {
              name = '建设用地\n（不含宅基地）'
            }
            if ((dataName === '1_1' && this.params.z === '2') || (dataName === '1_3' && this.params.j === '2') || (dataName === '3_1' && ['1', '2'].includes(this.params.f))) {
              name = name + '宗数'
            }
            if ((dataName === '1_1' && this.params.z === '3') || (dataName === '1_3' && this.params.j === '3') || (dataName === '3_1' && this.params.f === '3')) {
              name = name + '面积'
            }
            const pureUnit = unit.replace('万', '')
            const formatUnits = [pureUnit, '万' + pureUnit, '亿' + pureUnit]
            const { num, unit: u } = changeMoney(current.value, formatUnits)
            let value = num.toString().length <= 4 ? num : num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
            const rate = current.percent.toFixed(2)
            if (typeof size === 'string') {
              return `{name| ${name}} {value| ${value} ${u}} {rate| ${rate}%}`
            }
            value = '\n ' + value
            return `{name| ${name}} {value| ${value} ${u}} {rate| ${rate}%}`
          },
          icon: 'roundRect',
          itemHeight: 15,
          itemWidth: 15,
          itemGap: 12,
          textStyle: {
            align: 'right',
            top: -10,
            color: '#D1EFFF',
            fontSize: 14,
            rich: {
              name: {
                color: '#D1EFFF',
                fontSize: 14,
                width: 90,
                align: 'left'
              },
              value: {
                color: '#D1EFFF',
                fontSize: 14,
                width: typeof size === 'string' ? 100 : 160,
                lineHeight: 20
              },
              rate: {
                color: '#D1EFFF',
                fontSize: 14
              }
            }
          }
        },
        labelLine: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: size,
            center: ['25%', '55%'],
            data: this[`data${dataName}`],
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            itemStyle: {
              normal: {
                color: (colors) => this.colors[colors.dataIndex]
              }
            }
          }
        ]
      }
    },
    // eslint-disable-next-line complexity
    formatterTooltip(e, dataName) {
      const color = e.color
      let name = e.name
      if ((dataName === '1_1' && this.params.z === '2') || (dataName === '1_3' && this.params.j === '2') || (dataName === '3_1' && this.params.f === '2')) {
        name = name + '资产数'
      }
      if ((dataName === '1_3' && this.params.j === '3') || (dataName === '3_1' && this.params.f === '3')) {
        name = name + '面积'
      }
      let startName = '面积'
      if ((dataName === '3_1' && ['1', '2'].includes(this.params.f)) || (dataName === '1_1' && this.params.z === '2') || (dataName === '1_3' && this.params.j === '2')) {
        startName = '数量'
      }
      const marker = '<span style="display:inline-block;margin-right:4px;width:8px;height:8px;border-radius:2px;background-color:' + color + ';"></span>'
      const defineUnit = this.assetCategory.z[1].unit.replace('万', '')
      const { num: zNum, unit: zUnit } = changeMoney(e.data.num || 0, ['宗', '万宗', '亿宗'])
      if (dataName === '1_1' && this.params.z === '1') {
        const { num, unit } = changeMoney(e.data.value || 0, [defineUnit, '万' + defineUnit, '亿' + defineUnit])
        return (
          marker +
          name +
          '<br /><span style="color: #1ce2dc;"> ' +
          startName +
          '：' +
          (num.toString().length <= 4 ? num : num.toFixed(2)) +
          ' ' +
          unit +
          '，占比 ' +
          e.percent.toFixed(2) +
          '%' +
          '</span><br />数量：' +
          (zNum.toString().length <= 4 ? zNum : zNum.toFixed(2)) +
          zUnit
        )
      }
      if (dataName === '3_1' && this.params.f === '1') {
        const { num, unit } = changeMoney(e.data.area, ['平方米', '万平方米', '亿平方米'])
        return (
          marker +
          name +
          '<br /><span style="color: #1ce2dc;">' +
          startName +
          '：' +
          (zNum.toString().length <= 4 ? zNum : zNum.toFixed(2)) +
          zUnit +
          '，占比 ' +
          e.percent.toFixed(2) +
          '%' +
          '</span><br />面积：' +
          (num.toString().length <= 4 ? num : num.toFixed(2)) +
          unit
        )
      }
      let unit = '平方米'
      if ((dataName === '1_1' && this.params.z === '2') || (dataName === '1_2' && this.params.j === '2') || (dataName === '3_1' && this.params.f === '2')) {
        unit = '宗'
      }
      if (this.params.z === '3' && dataName === '1_1') {
        unit = '亩'
      }
      if (dataName === '1_1' && this.params.z === '3') {
        unit = defineUnit
      }
      const value = unit === '宗' ? e.data.num : e.data.value

      const { num, unit: formatUnit } = changeMoney(value || 0, [unit, `万${unit}`, `亿${unit}`])
      const formatVal = num.toString().length <= 4 ? num : num.toFixed(2)
      return (
        marker +
        name +
        '<br /><span style="color: #1ce2dc;">' +
        startName +
        '：' +
        formatVal +
        formatUnit +
        '，占比 ' +
        e.percent.toFixed(2) +
        '%' +
        '</span><br />其中发起交易数占比：' +
        (this.queryParams.year != new Date().getFullYear() ? "/" :  e.data.tradingPercent.toFixed(2) + '%')
      )
    }
  }
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.container {
  height: calc(100% - 110px);
  display: flex;
  color: #3ea7e1;
  padding: 0 20px 20px;
  #chart1_1,
  #chart1_2,
  #chart3_1 {
    width: 95%;
    height: calc(100% - 150px);
    margin-top: 20px;
  }
  .left-container,
  .right-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    // flex-basis: 600px;
    color: #fff;
    .bottom,
    .top {
      height: calc(50% - 10px);
      position: relative;
      .date-box {
        position: absolute;
        right: 20px;
        top: 10px;
      }
    }
    .rank-list {
      margin-top: 20px;
      margin-left: 15px;
      .rank-item {
        height: 45px;
        margin: 10px 0 20px;
        display: flex;
        align-items: center;
        &.level1 {
          background: url('@/assets/image/no1.png') no-repeat;
        }
        &.level2 {
          background: url('@/assets/image/no2-3.png') no-repeat;
        }
        &.level3 {
          background: url('@/assets/image/no4.png') no-repeat;
        }
        background-size: 500px 100% !important;

        .rank-order {
          display: block;
          width: 80px;
          text-align: center;
          font-size: 24px;
          color: #fff;
          font-weight: bold;
          margin-left: -3px;
        }
        .area-name {
          flex: 1.3;
          display: block;
          width: 73px;
          text-align: center;
          font-size: 18px;
          color: #bff7ff;
          line-height: 25px;
        }
        .rank-progress {
          display: inline-block;
          flex: 3;
          height: 25px;
          border: 1px solid rgb(134, 151, 233, 0.6);
          box-sizing: border-box;
          span {
            display: inline-block;
            margin: 2px;
            height: 19px;
            line-height: 15px;
            background: linear-gradient(to right, #1ce2dc, #2873eb);
            span {
              background: transparent;
              font-size: 14px;
              font-weight: bold;
            }
          }
        }
        .value {
          flex: 1.5;
          text-align: center;
          color: #bff7ff;
        }
      }
    }
    .view-more {
      margin-bottom: 20px;
      letter-spacing: 1px;
      color: #b8bbd5;
      display: flex;
      justify-content: center;
      span {
        padding-bottom: 2px;
        border-bottom: 1px solid #b8bbd5;
        cursor: pointer;
      }
    }
  }
  .center-container {
    flex: 1;
    // height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0 20px;
    .top-container {
      flex: 6;
      // padding: 20px;
      display: flex;
      flex-direction: column;
      .tooltip {
        .info {
          display: flex;
          justify-content: space-between;
        }
      }
      .map {
        flex: 1;
        height: 100%;
        width: 100%;
      }
    }
    .bottom-container {
      flex: 2;
      display: flex;
      flex-direction: column;
      .top {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 20px;
        font-weight: bold;
      }
      .bottom {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 20px;
        .block-box {
          width: calc(50% - 10px);
          .block-body {
            padding: 0 0 0 30px;
            display: flex;
            align-items: center;
            padding-right: 0;
            .img {
              width: 74px;
              height: 63px;
            }
            .detail-info {
              flex: 1;
              display: flex;
              flex-direction: column;
              margin-left: 10px;
              .value {
                font-size: 26px;
                color: #fff;
                .unit {
                  font-size: 16px;
                  font-weight: bold;
                }
              }

              .title {
                color: #1ce2dc;
              }
              .font14 {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
  .block-box {
    background-color: rgba(3 20 61 / 53%);
    box-shadow: rgba(0 123 228 / 50%) 0px 0px 40px 9px inset;
    // box-shadow: inset 0 0 60px rgb(2 68 144);
    display: flex;
    flex-direction: column;
    .block-title {
      height: 40px;
      line-height: 45px;
      background: url('@/assets/image/title.png') 0 0 no-repeat;
      padding-left: 36px;
      font-size: 18px;
      font-weight: bold;
      color: #fff;
    }
    .block-body {
      flex: 1;
      padding: 20px;
    }
  }
  .p0 {
    padding: 0 !important;
  }
}

::v-deep .el-select {
  .el-input__inner {
    color: rgb(66, 217, 236) !important;
    background: linear-gradient(to bottom, #005ba8, #002d59) !important;
    border-color: rgb(0, 156, 255) !important;
    font-size: 16px;
  }
  .el-select__caret {
    color: rgb(66, 217, 236) !important;
  }
  .popper__arrow {
    display: none;
  }

  .el-select-dropdown {
    background-color: #005ba8 !important;
    border: none;
    .el-select-dropdown__item {
      color: #fff !important;
      &.selected,
      &.hover {
        background-color: #3ea7e1;
      }
    }
  }
}
.area-time {
  font-size: 14px;
  color: #8fd2fd;
  display: flex;
  flex-direction: row;
  align-items: center;
  .area-img {
    height: 18px;
    width: 18px;
  }
  .area-title {
    margin: 0 10px;
    letter-spacing: 1px;
  }
  .area {
    font-weight: bold;
  }
}

.area-org {
  display: flex;
  flex-direction: row;
  align-items: center;
  // font-style: italic;
  line-height: 24px;
  .area-img {
    height: 18px;
    width: 18px;
  }
  .area-title {
    color: #fff;
    margin: 0 10px;
    font-size: 14px;
    letter-spacing: 1px;
  }
  .area {
    font-weight: bold;
    background: linear-gradient(to top, #d3a27a, #fff);
    background-clip: text;
    color: transparent;
  }
}
</style>
