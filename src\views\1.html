<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.1/echarts.min.js" integrity="sha512-OTbGFYPLe3jhy4bUwbB8nls0TFgz10kn0TLkmyA+l3FyivDs31zsXCjOis7YGDtE2Jsy0+fzW+3/OVoPVujPmQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <div id="jczy_bar" :canvas="3D" style="height: 240px;width:600px;margin: 0 auto;"></div>

    <script>
        // 调用
        const data = [13,13, 73, 70];
 const data2 = [50,80, 90, 36];
 const sideData = data.map(item => item + 90);
 const sideData2 = data.map(item => item + 90);
var serveTBar = echarts.init(document.getElementById('jczy_bar'));
serveTBar.setOption(getEcharts3DBar());

function getEcharts3DBar () {
		var colorArr = ["#0C628C", "#3887D5", "#2570BB"];
		var color = {
			type: "linear",
			x: 0,
			x2: 2,
			y: 0,
			y2: 0,
			colorStops:  [
				{
					offset: 0,
					color: colorArr[0],
				},
				{
					offset: 0.5,
					color: colorArr[0],
				},
				{
					offset: 0.5,
					color: colorArr[1],
				},
				{
					offset: 1,
					color: colorArr[1],
				},
			],
		};
		var barWidth = 30;
		var option = {
			tooltip: {
				trigger: 'axis',
				formatter: function (params) {
					var str = params[0].name + ":";
					params.filter(function (item) {
						if (item.componentSubType == "bar") {
							str += "<br/>" + item.seriesName + "：" + item.value;
						}
					});
					return str;
				},
			},
			grid:{
				x:'7%',
				x2:'0%',
				y:'15%',
				y2:'15%',
			},
			legend: {
				show:false,
				data:['本期','同期'],
				textStyle:{
					color:'#fff',
					fontSize:'20'
				}
			},
			xAxis: {
				data: ['毕节市','七星关区','大方县','黔西县'],
				//坐标轴
				axisLine :{
					show:true,
					lineStyle: {
						width: 1,
						color: '#214776'
					},
					textStyle:{
						color:'#fff',
						fontSize:'10'
					}
				},
				type : 'category',
				axisLabel :{
					textStyle:{
						color: '#C5DFFB',
						fontWeight: 500,
						fontSize:'14'
					}
				},
				axisTick  :{
					textStyle:{
						color:'#fff',
						fontSize:'16'
					},
					show: false,
				},
				splitLine: {show:false}
			},
			yAxis: {
				type : 'value',
				//坐标轴
				axisLine :{
					show:true,
					lineStyle: {
						width: 1,
						color: '#214776'
					},
					textStyle:{
						color:'#fff',
						fontSize:'10'
					}
				},
				axisTick  :{
					show:false
				},
				//坐标值标注
				axisLabel: {
					show: true,
					textStyle: {
						color: '#C5DFFB',
					}
				},
				//分格线
				splitLine: {
					lineStyle: {
						color: '#13365f'
					}
				}
			},
			series: [
				{
					z: 1,
					name: '本期',
					type: "bar",
					barWidth: barWidth,
					barGap: "0%",
					data: data,
					itemStyle: {
						normal: {
							color: color
						},
					},
				},
				{
					z: 2,
					name: '本期',
					type: "pictorialBar",
					data: sideData,
					symbol: "diamond",
					symbolOffset: ["-75%", "50%"],
					symbolSize: [barWidth, 10],
					itemStyle: {
						normal: {
							color: color
						},
					},
					tooltip: {
						show: false,
					},
				},
				{
					z: 3,
					name: '本期',
					type: "pictorialBar",
					symbolPosition: "end",
					data: data,
					symbol: "diamond",
					symbolOffset: ["-75%", "-50%"],
					symbolSize: [barWidth - 4, (10 * (barWidth - 4)) / barWidth],
					itemStyle: {
						normal: {
							borderWidth: 2,
							color: colorArr[2]
						},
					},
					tooltip: {
						show: false,
					},
				},
				{
					z: 1,
					name: '同期',
					type: "bar",
					barWidth: barWidth,
					barGap: "50%",
					data: data2,
					itemStyle: {
						normal: {
							color: color
						},
					},
				},
				{
					z: 2,
					name: '同期',
					type: "pictorialBar",
					data: sideData2,
					symbol: "diamond",
					symbolOffset: ["75%", "50%"],
					symbolSize: [barWidth, 10],
					itemStyle: {
						normal: {
							color: color
						},
					},
					tooltip: {
						show: false,
					},
				},
				{
					z: 3,
					name: '同期',
					type: "pictorialBar",
					symbolPosition: "end",
					data: data2,
					symbol: "diamond",
					symbolOffset: ["75%", "-50%"],
					symbolSize: [barWidth - 4, (10 * (barWidth - 4)) / barWidth],
					itemStyle: {
						normal: {
							borderWidth: 2,
							color: colorArr[2]
						},
					},
					tooltip: {
						show: false,
					},
				},
			],
		};
		return option;
	}
    </script>
</body>
</html>
