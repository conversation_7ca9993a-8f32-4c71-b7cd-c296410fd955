<template>
  <div ref="peiChart" class="chart" />
</template>

<script>
import { init } from 'echarts'
// import { getOptions } from './chart.js'

export default {
  name: 'PieChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    wrapLegend: {
      type: Boolean,
      default: false
    }, // 是否折叠显示legend
    showValue: {
      type: Boolean,
      default: true
    }, // 是否显示legend 中的value
    internalDiameterRatio: {
      type: Number,
      default: 0.6
    }, // 透明空心所占比率0-1
    opacity: {
      type: Number,
      default: 0.8
    }, // 不透明度 0-1
    unit: {
      type: String,
      default: ''
    } // 数据的单位
  },
  data() {
    this.colors = ['#fed130', '#13c2c2', '#368aff', '#fc5659', '#1fb5fc']
    return {
      myChart: null,
      option: {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 0,
              normal: {
                color: (colors) => this.colors[colors.dataIndex]
              }
            },
            left: '-40%',
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      }
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(newVal) {
        if (this.myChart) {
          this.myChart.clear()
        }
        if (newVal.length) {
          this.setChart()
        }
      }
    }
  },
  mounted() {
    // this.setChart()
    // 根据窗口变化自动调节图表大小
    window.onresize = () => {
      this.changeSize()
    }
  },
  methods: {
    // 图表初始化
    setChart() {
      const data = this.data
      const wrapLegend = this.wrapLegend
      const chart = init(this.$refs.peiChart)
      this.option.legend = {
        right: 30,
        bottom: 'center',
        orient: 'vertical',
        icon: 'roundRect',
        itemHeight: 15,
        itemWidth: 15,
        textStyle: {
          color: '#D1EFFF',
          fontSize: 14,
          rich: {
            value: {
              fontSize: 14,
              lineHeight: 24
            }
          }
        },
        formatter(seriesName) {
          const item = data.find(({ name }) => name === seriesName) || {}
          const { name, value } = item
          const getMaxLength = (prop) => Math.max(...data.map((row) => row[prop].length))
          const nameMaxLength = getMaxLength('name')
          const title = name.padEnd(nameMaxLength + 2)
          return `{title|${title}}${wrapLegend ? '\n' : ''}{title|${value.toLocaleString()}}宗`
        }
      }
      this.option.series[0].data = this.data
      chart.setOption(this.option)
      this.myChart = chart
    },
    // 自适应宽高
    changeSize() {
      this.myChart.resize()
    }
  }
}
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
