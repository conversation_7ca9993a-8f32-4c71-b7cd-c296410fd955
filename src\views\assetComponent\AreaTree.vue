<template>
  <div class="area-tree-container" style="width: 100%; font-size: 10px">
    <el-tree
      ref="_treeRef"
      class="filter-tree"
      :data="treeData"
      :props="defaultProps"
      v-bind="$attrs"
      :expand-on-click-node="false"
      :lazy="isLazyLoad"
      :load="loadNode"
      :show-checkbox="showCheckbox"
      :default-expanded-keys="expandedNodes"
      :default-checked-keys="checkedKeys"
      node-key="id"
      @node-click="handleNodeClick"
      v-on="$listeners"
    >
      <!-- <span slot-scope="{ node, data }" class="custom-tree-node"> -->
      <!-- <i v-if="data.type === 'Organization' || (data.code && data.code.startsWith('O'))" class="tree-node-org" /> -->
      <!-- <i v-else-if="isInLoginDialog" class="tree-node-division" />
        <i v-else-if="data.isLeaf" class="tree-node-leaf" /> -->
      <!-- <i v-else-if="node.expanded" class="tree-node-expanded" /> -->
      <!-- <i v-else class="tree-node-unexpanded" /> -->
      <!-- <span :title="node.label">{{ node.label }}</span> -->
      <!-- </span> -->
    </el-tree>
  </div>
</template>

<script>
export default {
  name: 'AreaTree',
  props: {
    isInLoginDialog: { type: Boolean, default: () => false },
    expandedNodes: { type: Array, default: () => [] },
    isLazyLoad: { type: Boolean, default: false },
    treeData: { type: Array, default: () => [] },
    showCheckbox: { type: Boolean, default: false },
    checkedKeys: { type: Array, default: () => [] },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'text',
          isLeaf: 'isLeaf'
        }
      }
    }
  },
  data() {
    return {}
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.$refs._treeRef.setCurrentKey(1512)
    // })
  },
  methods: {
    filter(value) {
      this.$refs['_treeRef'].filter(value)
    },
    setCurrentKey(value) {
      this.$refs['_treeRef'].setCurrentKey(value)
    },
    handleNodeClick(data, node) {
      this.$emit('selectedNodeChange', data, node)
    },
    loadNode(node, resolve) {
      if (!this.isLazyLoad) {
        return resolve(this.treeData)
      }
      if (node.level === 0 || node.isLeaf) {
        return resolve(this.treeData)
      } else {
        this.$emit('loadChildNode', node.data, resolve)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
i[class^='tree-node-']:before {
  content: '替换';
  visibility: hidden;
}

::v-deep .el-tree {
  background-color: #005ba8;
  font-size: 14px;
  color: #fff;
  .el-tree-node:focus > .el-tree-node__content {
    background-color: #3ea7e1 !important;
  }
  .el-tree-node__content:hover {
    background-color: #3ea7e1 !important;
  }
  .is-current > .el-tree-node__content {
    background-color: transparent;

    .el-tree-node__expand-icon {
      color: fff;
    }
    .custom-tree-node {
      color: fff;
    }
  }
}
</style>
