import Cookies from 'js-cookie'

// const TokenKey = 'Admin-Token'
const TokenKey = 'X-user-token-header'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  Cookies.remove('X-user-token-header')
  Cookies.remove('X-tenant-id-header')
  return Cookies.remove(Token<PERSON>ey)
}
