<template>
  <div id="myChart" class="chart" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'Line<PERSON><PERSON>',
  props: {
    fontSize: {
      type: Number,
      default: 14
    },
    isShowMoneyUnit: {
      type: Boolean,
      default: false
    },
    chartData: {
      type: Array,
      default: () => []
    }, // x轴数据
    isRatio: {
      type: Boolean,
      default: false
    },
    integerYAxis: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      myChart: null,
      timeline1: {
        // loop: false,
        axisType: 'category',
        show: false,
        x: '8%',
        y: '80%',
        x2: '0%',
        y2: '10%',
        autoPlay: true,
        playInterval: 2000,
        width: 900,
        data: ['交易公告发布宗数', '交易成交宗数', '成功交易比例', '成交金额', '平均溢价率'],
        controlPosition: 'none',
        lineStyle: {
          color: '#0f496f'
        },
        label: {
          textStyle: {
            color: '#30eee9'
          }
        },
        checkpointStyle: {
          symbolSize: 10,
          borderWidth: 10
        },
        symbolSize: 10
      },
      timeline: {
        tooltip: {
          show: false
        },
        top: 10,
        playInterval: '2000',
        axisType: 'category',
        autoPlay: true,
        symbol: 'rect',
        symbolSize: 14,
        itemStyle: {
          normal: {
            color: '#000b33',
            borderColor: '#1f79ff',
            borderWidth: 2
          },
          emphasis: {
            color: '#3cd1fc'
          }
        },
        label: {
          normal: {
            textStyle: {
              fontSize: 14,
              color: '#1f79ff'
            }
          },
          emphasis: {
            textStyle: {
              fontSize: 14,
              color: '#3dd4ff'
            }
          }
        },
        lineStyle: {
          color: '#1f79ff',
          width: 0
        },
        checkpointStyle: {
          color: '#3dd4ff',
          borderColor: '#1f79ff',
          borderWidth: 2
        },
        controlStyle: {
          show: false
        },
        data: ['交易公告发布宗数', '交易成交宗数', '成功交易比例', '成交金额', '平均溢价率']
      }
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(newVal) {
        if (this.myChart) {
          this.myChart.clear()
        }
        this.initChart()
      }
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      const divId = document.getElementById('myChart')
      if (this.myChart) {
        this.myChart.dispose()
      }
      this.myChart = echarts.init(divId)
      const xData = []
      const yData = []
      this.chartData.forEach((item) => {
        xData.push(item.label)
        yData.push(item.value)
      })
      const option = {
        xAxis: {
          type: 'category',
          axisTick: { show: false },
          data: xData,
          axisLabel: {
            textStyle: {
              color: '#67d7ff',
              fontSize: this.fontSize
            }
          },
          axisLine: {
            lineStyle: {
              color: '#0f376b'
            }
          }
        },
        yAxis: {
          type: 'value',
          minInterval: this.integerYAxis ? 1 : 0,
          name: this.isShowMoneyUnit ? '(万)' : '',
          axisLabel: {
            textStyle: {
              color: '#2385dc',
              fontSize: this.fontSize > 11 ? 14 : 10
            },
            formatter: (val) => {
              return this.isRatio ? val + '%' : val
            }
          },
          nameTextStyle: {
            color: '#2385dc',
            padding: [0, 0, 0, -30]
          },
          splitLine: {
            lineStyle: {
              color: '#0f376b'
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          appendToBody: true,
          axisPointer: {
            type: 'shadow'
          },
          padding: [5, 10, 5, 10],
          formatter: function (params) {
            let str = ''
            params.forEach((item) => {
              // console.log('item===>', item)
              str += item.marker + item.name + ' : ' + item.value + '</br>'
            })
            return str
          }
        },
        grid: {
          top: 30,
          bottom: 25,
          left: 50,
          right: 20
        },
        series: [
          {
            data: yData,
            type: 'line',
            lineStyle: {
              color: '#08debc'
            }
          }
        ]
      }
      const option1 = {
        baseOption: {
          timeline: this.timeline,
          xAxis: {
            type: 'category',
            axisTick: { show: false },
            // data: xData,
            axisLabel: {
              textStyle: {
                color: '#67d7ff'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#0f376b'
              }
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#2385dc'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#0f376b'
              }
            }
          },
          tooltip: {
            trigger: 'axis',
            confine: true,
            axisPointer: {
              type: 'shadow'
            }
          },
          // grid: {
          //   top: 30,
          //   right: 30
          // },
          series: [
            {
              // data: yData,
              type: 'line',
              lineStyle: {
                color: '#08debc'
              }
            }
          ]
        },
        options: [
          {
            xAxis: [
              {
                data: ['4-01', '4-15', '4-30']
              }
            ],
            // title: {
            //   text: '人均学习时长',
            //   x:'5%',
            //   y:'5%',
            //   textStyle:{
            //     color:'#2ededf',
            //     fontSize:12
            //   }
            // },
            series: [
              {
                data: [225, 175, 178]
              }
            ]
          },
          {
            xAxis: [
              {
                data: ['4-01', '4-15', '4-30']
              }
            ],
            // title: {
            //   text: '人均学习时长',
            //   x:'5%',
            //   y:'5%',
            //   textStyle:{
            //     color:'#2ededf',
            //     fontSize:12
            //   }
            // },
            series: [
              {
                data: [20, 15, 75]
              }
            ]
          },
          {
            xAxis: [
              {
                data: ['4-01', '4-15', '4-30']
              }
            ],
            // title: {
            //   text: '人均学习时长',
            //   x:'5%',
            //   y:'5%',
            //   textStyle:{
            //     color:'#2ededf',
            //     fontSize:12
            //   }
            // },
            series: [
              {
                data: [100, 120, 80]
              }
            ]
          },
          {
            xAxis: [
              {
                data: ['4-01', '4-15', '4-30']
              }
            ],
            // title: {
            //   text: '人均学习时长',
            //   x:'5%',
            //   y:'5%',
            //   textStyle:{
            //     color:'#2ededf',
            //     fontSize:12
            //   }
            // },
            series: [
              {
                data: [200, 175, 175]
              }
            ]
          },
          {
            xAxis: [
              {
                data: ['4-01', '4-15', '4-30']
              }
            ],
            // title: {
            //   text: '人均学习时长',
            //   x:'5%',
            //   y:'5%',
            //   textStyle:{
            //     color:'#2ededf',
            //     fontSize:12
            //   }
            // },
            series: [
              {
                data: [99, 34, 88]
              }
            ]
          }
        ]
      }
      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.chart {
  margin: 0 auto;
  width: 100%;
  height: 100%;
}
</style>
