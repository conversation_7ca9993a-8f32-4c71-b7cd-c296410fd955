{"name": "onlinetrading", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "build:dev": "vue-cli-service build --mode development", "build:prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint"}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"axios": "0.21.0", "core-js": "^3.8.3", "echarts": "5.5.0", "echarts-gl": "^2.0.9", "element-ui": "2.15.5", "html-webpack-plugin": "^5.5.0", "js-beautify": "1.13.0", "js-cookie": "^2.2.1", "jsencrypt": "^3.2.1", "postcss-import": "^12.0.1", "postcss-px2rem": "^0.3.0", "postcss-url": "^8.0.0", "register-service-worker": "^1.7.2", "sass-loader": "^10.1.0", "vue": "^2.6.14", "vue-piczoom": "^1.0.6", "vue-router": "^3.5.1", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-plugin-pwa": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "chalk": "4.1.0", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "html-webpack-plugin": "^5.5.0", "image-webpack-loader": "^7.0.1", "lint-staged": "10.5.3", "postcss": "^8.5.3", "sass": "^1.45.2", "sass-loader": "^12.0.0", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "^2.6.14"}}