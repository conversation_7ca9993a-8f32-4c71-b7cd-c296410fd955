import request from '@/utils/request'

// 基本信息
export function getBaseInfoAmount (params) {
  return request({
    url: '/shares/shares/screen/getBaseInfoAmount',
    method: 'get',
    params,
    withCredentials: true
  })
}

// 集体经济组织（机构）构成
export function getOrgCount (params) {
  return request({
    url: '/shares/shares/screen/getOrgCount',
    method: 'get',
    params,
    withCredentials: true
  })
}

// 成员构成
export function getMemberData (params) {
  return request({
    url: '/shares/shares/screen/getMemberData',
    method: 'get',
    params,
    withCredentials: true
  })
}

// 人均分红趋势
export function getBonusTrend (params) {
  return request({
    url: '/shares/shares/screen/getBonusTrend',
    method: 'get',
    params,
    withCredentials: true
  })
}

// 人均福利分配排名
export function getWelfareRanking (params) {
  return request({
    url: '/shares/shares/screen/getWelfareRanking',
    method: 'get',
    params,
    withCredentials: true
  })
}

// 可分配收益排名
export function getProfitDistributionRanking (params) {
  return request({
    url: '/shares/shares/screen/getProfitDistributionRanking',
    method: 'get',
    params,
    withCredentials: true
  })
}

