<template>
  <div id="echartsBox" class="box"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  props: {
    echartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null,
      data: [
        { value: 10481, name: '农用地', percentage: '10' },
        { value: 735111, name: '建设用地', percentage: '20' },
        { value: 58022, name: '未利用地', percentage: '30' },
        { value: 4843, name: '“四”荒地', percentage: '10.5' },
        { value: 300666, name: '其他土地资产', percentage: '10.6' }
      ]
    }
  },
  watch: {
    echartData: {
      handler: function (val) {
        this.$nextTick(() => {
          this.initializationEcharts()
        })
      },
      immediate: true
    }
  },
  methods: {
    money(val) {
      console.log(val, 888)
      let numberStr = ''
      if (String(parseInt(val)).length > 12) {
        numberStr = Number(val / 100000000).toFixed(2) + '亿亩'
      } else if (String(parseInt(val)).length > 4) {
        numberStr = Number(val / 10000).toFixed(2) + '万亩'
      } else {
        numberStr = Number(val).toFixed(2) + '亩'
      }
      return numberStr
    },
    initializationEcharts() {
      const echartsBox = document.getElementById('echartsBox')
      this.myChart = echarts.init(echartsBox)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          width: '50px',
          padding: [
            20, // 上
            15, // 右
            5, // 下
            10 // 左
          ],
          top: '0%',
          left: '35%',
          formatter: (name) => {
            let tarValue
            let percentage
            for (let i = 0; i < this.echartData.length; i++) {
              if (this.echartData[i].name == name) {
                tarValue = this.echartData[i].value
                percentage = this.echartData[i].percentage
              }
            }
            return [
              '{a|' +
                name +
                '}{b|' +
                this.money(tarValue) +
                '}{c|' +
                percentage +
                '%}'
            ].join('\n')
          },
          textStyle: {
            rich: {
              a: {
                verticalAlign: 'right',
                fontSize: 12,
                align: 'left',
                width: 140
              },
              b: {
                fontSize: 12,
                align: 'left',
                width: 110
              },
              c: {
                fontSize: 12,
                align: 'right',
                width: 50
              }
            },
            color: '#bff7ff'
          },
          tooltip: {
            show: true
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['60%', '95%'],
            center: ['18%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            data: this.echartData
          }
        ]
      }
      this.myChart.setOption(option, true)
      this.myChart.on('click', this.getDetail)
    },
    getDetail(params) {
      this.$emit('getPieData', params)
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  height: 180px;
  margin-bottom: 20px;
  // width: 130%
}
</style>
