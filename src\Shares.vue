<template>
  <div class="container">
    <div class="left-container">
      <div class="box3_1 block-box">
        <div class="block-title">集体经济组织（机构）构成</div>
        <div id="chart3_1" class="chart"></div>
      </div>
      <div class="box3_2 block-box" @mouseenter.stop="handleMouseEnter('3_2')" @mouseleave.stop="handleMouseLeave('3_2')">
        <div class="block-title">成员构成</div>
        <div class="block-body">
          <div class="switch-box">
            <span v-for="item in switch2Options" :key="item.id" class="radio-btn" :class="switch2 == item.id ? 'selected' : ''" @click="handleChangeSwitch2(item)">
              {{ item.text }}
            </span>
          </div>
          <div id="chart3_2" v-loading="loading3_2" element-loading-text="数据加载中" class="chart"></div>
        </div>
      </div>
      <div class="box1_1 block-box">
        <div class="block-title">可分配收益</div>
        <div class="block-body">
          <div class="box">
            <img class="img" src="@/assets/image/shares/icon1.png" alt="" />
            <div class="detail-info">
              <div class="title">可分配收益总额</div>
              <div class="value">
                {{ numberFormate(data1_1.totalAmount).value }}
                <font style="font-size: 16px">{{ numberFormate(data1_1.totalAmount).unit }}</font>
              </div>
            </div>
          </div>
          <div class="box">
            <img class="img" src="@/assets/image/shares/icon2.png" alt="" />
            <div class="detail-info">
              <div class="title">留存总额</div>
              <div class="value">
                {{ numberFormate(data1_1.lcze).value }}
                <font style="font-size: 16px">{{ numberFormate(data1_1.lcze).unit }}</font>
              </div>
            </div>
          </div>
          <div class="box">
            <img class="img" src="@/assets/image/shares/icon3.png" alt="" />
            <div class="detail-info">
              <div class="title">留存比例</div>
              <div class="value">
                {{ data1_1.totalAmount ? ((parseFloat(data1_1.lcze) * 100) / parseFloat(data1_1.totalAmount)).toFixed(2) + '%' : '0.00%' }}
              </div>
            </div>
          </div>
          <div class="box">
            <img class="img" src="@/assets/image/shares/icon4.png" alt="" />
            <div class="detail-info">
              <div class="title">分红总额</div>
              <div class="value">
                {{ numberFormate(data1_1.bonusAmount).value }}
                <font style="font-size: 16px">{{ numberFormate(data1_1.bonusAmount).unit }}</font>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="center-container">
      <div class="top-container">
        <div class="tooltip">
          <div class="info">
            <div class="area-org">
              <img class="area-img" src="@/assets/image/icon1.png" alt="" />
              <span class="area-title" style="font-size: 18px">当前地区：</span>
              <span class="area" style="font-size: 24px">| {{ currentArea }} |</span>
            </div>

            <div class="area-time">
              <img class="area-img" src="./assets/image/date.png" alt="" />
              <span class="area-title" style="font-size: 18px">统计截止时间：</span>
              <span class="area" style="font-size: 18px">{{ currentDate }}</span>
            </div>
          </div>
          <div class="info" style="margin-top: 20px">
            <div></div>
            <div style="display: flex">
              <el-select v-model="queryParams.year" style="width: 100px" placeholder="" :popper-append-to-body="false" @change="getDateRelated">
                <el-option v-for="item in yearOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
              <!-- <el-select
                v-if="switch1 == '2'"
                v-model="queryParams.quarter"
                style="width: 100px; margin-left: 10px"
                placeholder=""
                :popper-append-to-body="false"
                @change="getDateRelated"
              >
                <el-option v-for="item in quarterOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
              <el-select
                v-if="switch1 == '3'"
                v-model="queryParams.month"
                style="width: 100px; margin-left: 10px"
                placeholder=""
                :popper-append-to-body="false"
                @change="getDateRelated"
              >
                <el-option v-for="item in monthOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>

              <span v-for="item in dateOptions" :key="item.id" class="radio-btn" :class="switch1 == item.id ? 'selected' : ''" @click="handleChangeSwitch1(item)">
                {{ item.text }}
              </span> -->
            </div>
          </div>
        </div>
        <mapNav class="map" :area-code="areaCode" @gotopath="gotopath" />
      </div>
      <div class="bottom-container">
        <div class="box2_1">
          <div>集体经济组织（机构）数</div>
          <number-flash-copy :number="data2_1.orgNum ? Number(data2_1.orgNum) : data2_1.orgNum" :precision="0" />
          <div>个</div>
        </div>

        <div class="box2_2">
          <div class="block-box">
            <div class="block-body">
              <img class="img" src="@/assets/image/shares/icon6.png" alt="" />
              <div class="detail-info">
                <div class="value">
                  {{ formatPeople(data2_1.memberNum).num }}
                  <font style="font-size: 16px">{{ formatPeople(data2_1.memberNum).unit }}人</font>
                </div>
                <div class="title">成员数</div>
              </div>
            </div>
          </div>
          <div class="block-box">
            <div class="block-body">
              <img class="img" src="@/assets/image/shares/icon5.png" alt="" />
              <div class="detail-info">
                <div class="value">
                  {{ numberFormate(data2_1.averageAllocation).value }}
                  <font style="font-size: 16px">{{ numberFormate(data2_1.averageAllocation).unit }}</font>
                </div>
                <div class="title">人均分红</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-container">
      <div class="box3_3 block-box" @mouseenter.stop="handleMouseEnter('3_3')" @mouseleave.stop="handleMouseLeave('3_3')">
        <div class="block-title">{{ title + '之星' }}</div>
        <div class="block-body">
          <div class="switch-box">
            <span
              v-for="item in switch3Options"
              :key="item.id"
              :style="{ display: item.show ? 'inline-block' : 'none' }"
              class="radio-btn"
              :class="switch3 == item.id ? 'selected' : ''"
              @click="handleChangeSwitch3(item)"
            >
              {{ item.text }}
            </span>
          </div>
          <div class="switch-box1">
            <span v-for="item in switch4Options" :key="item.id" class="radio-btn1" :class="switch4 == item.id ? 'selected' : ''" @click="handleChangeSwitch4(item)">
              {{ item.text }}
            </span>
          </div>

          <div class="rank-header">
            <div class="header-item" style="width: 80px">名次</div>
            <div class="header-item" style="flex: 3">
              {{ switch3 == 1 ? '地区' : switch3 == 2 ? '镇街' : switch3 == 3 ? '村居' : '组织机构' }}
            </div>
            <div class="header-item" style="flex: 1">{{ switch4Text }}</div>
          </div>

          <ul v-loading="loading4" class="rank-list" element-loading-text="数据加载中" element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.3)">
            <li v-for="(item, index) in data3_3" :key="item.orgName" class="rank-item" :class="index === 0 ? 'level1' : index <= 2 ? 'level2' : 'level3'">
              <span class="rank-order" style="font-size: 20px">{{ index + 1 }}</span>
              <span class="area-name">{{ item.divName }}</span>
              <span class="value">{{ numberFormate(item.value).value + numberFormate(item.value).unit }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div class="box1_2 block-box">
        <div class="block-title">人均分红趋势</div>
        <div class="block-body">
          <div v-if="areaLevel > 2" class="switch-box">
            <span class="radio-btn" @click="searchData1_2_pageData">详情</span>
          </div>
          <div id="chart1_2" class="chart"></div>
        </div>
      </div>
      <div class="box1_3 block-box">
        <div class="block-title">人均福利分配排名</div>
        <div class="block-body">
          <div v-if="areaLevel > 1" class="switch-box">
            <span class="radio-btn" @click="searchData1_3_pageData">详情</span>
          </div>

          <div class="rank-header">
            <div class="header-item" style="width: 80px">名次</div>
            <div class="header-item" style="flex: 3">地区</div>
            <div class="header-item" style="flex: 1">人均福利金额</div>
          </div>

          <ul class="rank-list">
            <li v-for="(item, index) in data1_3" :key="item.orgName" class="rank-item" :class="index === 0 ? 'level1' : index <= 2 ? 'level2' : 'level3'">
              <span class="rank-order" style="font-size: 20px">{{ index + 1 }}</span>
              <span class="area-name">{{ item.orgName }}</span>
              <span class="value">{{ numberFormate(item.value).value + numberFormate(item.value).unit }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 人均分红趋势 -->
    <el-dialog :visible.sync="dialogVisible1" custom-class="custom-dialog" :show-close="false" :append-to-body="false" width="70%" @close="() => (page1_2 = 1)">
      <div slot="title">
        <div class="title">人均分红趋势</div>
        <img class="close-btn" src="./assets/image/trading/close.png" alt="" @click.stop="dialogVisible1 = false" />
      </div>

      <div style="display: flex; justify-content: space-between; margin-bottom: 10px; margin-top: 10px">
        <el-button class="search-btn" @click="handleExportData1_2">导出报表</el-button>

        <!-- <span style="color: #fff">单位：元</span> -->
      </div>

      <el-table
        ref="table1_2"
        v-loading="loading1"
        :data="data1_2_table.rows"
        stripe
        :height="410"
        element-loading-text="数据加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.3)"
      >
        <el-table-column prop="serialNum" label="名次" align="center" header-align="center" width="80px"></el-table-column>
        <el-table-column prop="orgName" :label="areaLevel == 3 ? '村居' : '组织机构'" align="center" header-align="center" width="200px"></el-table-column>
        <el-table-column v-for="col in columns1_2" :key="col" :label="isNaN(col) ? col : col + '年'" align="center" header-align="center" min-width="120px">
          <template slot-scope="scope">
            {{ numberFormate(scope.row.map[col]).value + numberFormate(scope.row.map[col]).unit }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page1_2"
        :page-size="10"
        layout="total, prev, pager, next, jumper"
        :total="data1_2_table.total"
        style="float: right"
        @current-change="handleCurrentChange1_2"
      ></el-pagination>
    </el-dialog>

    <!-- 人均福利分配排名 -->
    <el-dialog :visible.sync="dialogVisible2" custom-class="custom-dialog" :show-close="false" :append-to-body="false" @close="() => (page1_3 = 1)">
      <div slot="title">
        <div class="title">人均福利分配排名</div>
        <img class="close-btn" src="./assets/image/trading/close.png" alt="" @click.stop="dialogVisible2 = false" />
      </div>

      <div style="display: flex; justify-content: space-between; margin-bottom: 10px;margin-top: 10px">
        <el-button class="search-btn" @click="handleExportData1_3">导出报表</el-button>

        <!-- <span style="color: #fff">单位：元</span> -->
      </div>

      <el-table
        v-loading="loading2"
        :data="data1_3_table.rows"
        stripe
        :height="430"
        element-loading-text="数据加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.3)"
      >
        <el-table-column prop="serialNum" label="名次" align="center" header-align="center" width="100"></el-table-column>
        <el-table-column
          prop="orgName"
          :label="areaLevel == 2 ? '镇街' : areaLevel == 3 ? '村居' : '组织机构'"
          align="center"
          header-align="center"
          min-width="120"
        ></el-table-column>
        <el-table-column prop="perAmount" label="人均福利金额" align="center" header-align="center" min-width="120">
          <template slot-scope="scope">
            {{ numberFormate(scope.row.perAmount).value + numberFormate(scope.row.perAmount).unit }}
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        :current-page="page1_3"
        :page-size="10"
        layout="total, prev, pager, next, jumper"
        :total="data1_3_table.total"
        style="float: right"
        @current-change="handleCurrentChange1_3"
      ></el-pagination>
    </el-dialog>

    <!-- 区域之星 -->
    <el-dialog
      :visible.sync="dialogVisible3"
      custom-class="custom-dialog"
      :show-close="false"
      :append-to-body="false"
      width="70%"
      :destroy-on-close="true"
      @close="() => (page3_3 = 1)"
    >
      <div slot="title">
        <div class="title">{{ title + '之星' }}</div>
        <img class="close-btn" src="./assets/image/trading/close.png" alt="" @click.stop="dialogVisible3 = false" />
      </div>
      <div v-if="dialogVisible3" v-loading="loading3">
        <el-form inline style="margin-top: 10px">
          <el-form-item label="当前地区">
            <el-select ref="_areaSelectRef" v-model="params3_3.areaName" popper-class="area_tree" style="width: 240px" :title="params3_3.areaName" :popper-append-to-body="false">
              <el-option :value="treeNodeAreaName" style="height: auto; padding: 0">
                <area-tree :is-lazy-load="true" :tree-data="treeData" :expanded-nodes="expandedNodes" @loadChildNode="loadChildNode" @selectedNodeChange="handleNodeChange" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="统计年份">
            <el-select v-model="params3_3.year" style="width: 100px" placeholder="" :popper-append-to-body="false">
              <el-option v-for="item in yearOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button class="search-btn" @click="searchData3_3_pageData">查询</el-button>
            <el-button class="search-btn" @click="handleExportData3_3">导出报表</el-button>
          </el-form-item>
          <el-form-item style="float: right">
            <div class="switch-box">
              <span
                v-for="item in dialogSwitchOptions"
                :key="item.id"
                class="switch-btn"
                style="display: inline"
                :class="params3_3.switch == item.id ? 'selected' : ''"
                @click="handleChangeDialogSwitch(item)"
              >
                {{ item.text }}
              </span>
            </div>
          </el-form-item>
        </el-form>

        <el-table
          v-if="showTable"
          v-loading="loading2"
          :data="data1_3_table.rows"
          stripe
          :height="430"
          element-loading-text="数据加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0)"
        >
          <el-table-column prop="serialNum" label="名次" align="center" header-align="center" width="100"></el-table-column>
          <el-table-column prop="divName" :label="colTitle" align="center" header-align="center" min-width="200"></el-table-column>
          <el-table-column v-for="col in columns3_3" :key="col.prop" :prop="col.prop" :label="col.label" align="center" header-align="center" min-width="120">
            <template slot-scope="scope">
              {{ numberFormate(scope.row[col.prop]).value + numberFormate(scope.row[col.prop]).unit }}
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-if="showTable"
          :current-page="page3_3"
          :page-size="10"
          layout="total, prev, pager, next, jumper"
          :total="data1_3_table.total"
          style="float: right"
          @current-change="handleCurrentChange3_3"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import mapNav from './views/mapNav.vue'
import AreaTree from '@/views/assetComponent/AreaTree.vue'
import NumberFlashCopy from '@/views/NumberFlashCopy'
import { userTreeData } from '@/api/asset'
import { getBaseInfoAmount, getOrgCount, getMemberData, getBonusTrend, getWelfareRanking, getProfitDistributionRanking } from '@/api/shares.js'
import { changeMoney, getLastFormatDate } from '@/utils/common'

export default {
  name: 'Shares',
  components: {
    mapNav,
    AreaTree,
    NumberFlashCopy
  },
  data() {
    return {
      records: require('./views/mapData/foshan_area_data.json'),
      areaCode: 'D4406',
      areaLevel: '1',
      currentArea: '佛山市',
      currentDate: '',
      currentAccount: 'fs',
      queryParams: {
        year: '',
        quarter: '',
        month: ''
      },

      switch1: '1',

      yearOptions: [],
      quarterOptions: [
        { id: '1', text: '第一季度' },
        { id: '2', text: '第二季度' },
        { id: '3', text: '第三季度' },
        { id: '4', text: '第四季度' }
      ],
      monthOptions: [
        { id: '1', text: '1月' },
        { id: '2', text: '2月' },
        { id: '3', text: '3月' },
        { id: '4', text: '4月' },
        { id: '5', text: '5月' },
        { id: '6', text: '6月' },
        { id: '7', text: '7月' },
        { id: '8', text: '8月' },
        { id: '9', text: '9月' },
        { id: '10', text: '10月' },
        { id: '11', text: '11月' },
        { id: '12', text: '12月' }
      ],
      dateOptions: [
        { id: '1', text: '年度' },
        { id: '2', text: '季度' },
        { id: '3', text: '月度' }
      ],
      switch2: '1',
      switch2Options: [
        { id: '1', text: '按年龄' },
        { id: '2', text: '按性别' }
      ],
      switch3: '1',
      switch4: '1',
      switch4Options: [
        { id: '1', text: '按可分配收益数' },
        { id: '2', text: '按留存数' },
        { id: '3', text: '按人均分红' }
      ],

      data1_1: {},

      data1_2: [],
      chart1_2: null,
      dialogVisible1: false,
      loading1: false,
      data1_2_table: {
        rows: [],
        total: 0
      },
      columns1_2: [],
      page1_2: 1,

      data1_3: [],
      dialogVisible2: false,
      loading2: false,
      data1_3_table: {
        rows: [],
        total: 0
      },
      page1_3: 1,

      data2_1: {},

      data3_1: [],
      chart3_1: null,
      data3_2: [],
      sourceData3_2: null,
      chart3_2: null,
      loading3_2: false,
      data3_3: [],
      colTitle: '',
      loading4: false,
      dialogVisible3: false,
      loading3: false,
      data3_3_table: {
        rows: [],
        total: 0
      },
      page3_3: 1,
      params3_3: {
        year: '',
        switch: '',
        areaName: '',
        areaCode: '',
        areaType: ''
      },
      columns3_3: [],
      showTable: false,
      treeNodeAreaName: '',
      treeData: [],

      playInterval: 5000,
      task3_2: null,
      task3_3: null,
      currentMouseOverTarget: null // add by ymk 当前鼠标在哪个物体上，就要停止哪个物体上的滚动
    }
  },
  computed: {
    switch3Options() {
      return [
        { id: '1', text: '区域', show: this.areaLevel == '1' },
        { id: '2', text: '镇街', show: this.areaLevel <= '2' },
        { id: '3', text: '村居', show: this.areaLevel <= '3' },
        { id: '4', text: '组织', show: this.areaLevel <= '3' },
        { id: '5', text: '更多', show: true }
      ]
    },
    title() {
      const temp = this.switch3Options.find((i) => i.id == this.switch3)
      return temp.text
    },
    switch4Text() {
      const temp = this.switch4Options.find((i) => i.id == this.switch4)
      return temp.text.substring(1)
    },
    dialogSwitchOptions() {
      const options = [
        { id: '1', text: '按可分配收益数排序' },
        { id: '2', text: '按留存数排序' },
        { id: '3', text: '按人均分红排序' }
      ]
      return options.filter((i) => i.id != this.switch4)
    },
    expandedNodes() {
      const ids = []
      if (this.treeData.length > 0) {
        this.treeData.forEach((levelOneNode) => {
          ids.push(levelOneNode.id)
        })
      }
      return ids
    }
  },
  watch: {
    switch3Options: {
      handler(val) {
        // const temp = this.switch3Options.filter((i) => i.show)
        // if (temp[0].id == '5') {
        //   // this.switch3 = ''
        // } else {
        //   this.switch3 = temp[0].id
        // }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initBaseData()
  },
  beforeDestroy () {
    this.task3_2 && clearTimeout(this.task3_2)
    this.task3_3 && clearTimeout(this.task3_3)
  },
  methods: {
    handleMouseEnter(type) {
      this.currentMouseOverTarget = type
    },
    handleMouseLeave(type) {
      this.currentMouseOverTarget = null
    },
    getNextOption (options, current, target) {
      if (target == this.currentMouseOverTarget) return null // 如果当前鼠标在上面，就停止滚动
      const currentIndex = options.findIndex(i => i.id == current)
      let nextIndex = 0
      if (currentIndex >= options.length - 1) {
        nextIndex = 0
      } else {
        nextIndex = currentIndex + 1
      }
      return options[nextIndex]
    },
    initBaseData() {
      const startYear = 2023
      const currentYear = new Date().getFullYear()
      this.$set(this.queryParams, 'year', currentYear)
      for (let i = currentYear; i >= startYear; i--) {
        this.yearOptions.push({
          id: i,
          text: i + '年'
        })
      }
      this.currentDate = getLastFormatDate(new Date().getFullYear() + '-' + (new Date().getMonth() + 1 + '').padStart(2, '0') + '-' + (new Date().getDate() + '').padStart(2, '0'))
      this.getPageData()
    },
    getPageData() {
      // 计算所有接口
      this.task3_2 && clearTimeout(this.task3_2)
      this.task3_3 && clearTimeout(this.task3_3)
      this.getData1_1()
      this.getData1_2()
      this.getData1_3()
      this.getData3_1()
      this.getData3_2(true)
      this.getData3_3()
    },
    getDateRelated() {
      // 计算与年月日相关的接口
      this.task3_2 && clearTimeout(this.task3_2)
      this.task3_3 && clearTimeout(this.task3_3)
      this.getData1_1(true)
      this.getData1_2(true)
      this.getData1_3(true)
      this.getData3_1(true)
      this.getData3_2(true)
      this.getData3_3(true)
    },
    getAreaRelated() {
      // 计算与地区相关的接口
      this.getData1_1(true)
      this.getData1_2(true)
      this.getData1_3(true)
      this.getData3_1(true)
      this.getData3_2(true)
      this.getData3_3(true)
    },
    // eslint-disable-next-line complexity
    gotopath(type) {
      const current = this.records.records.find((item) => {
        return item.areaCode == `D${type}`
      })
      this.currentArea = current.areaName
      this.areaCode = current.areaCode
      if (type != undefined) {
        this.areaCode = type
        this.areaCode = 'D' + this.areaCode
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'nh'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'ss'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'cc'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'sd'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'gm'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'fs'
      }

      if (type.length == 4) {
        this.areaLevel = '1'
      } else if (type.length == 6) {
        this.areaLevel = '2'
      } else if (type.length == 9) {
        this.areaLevel = '3'
      } else {
        this.areaLevel = '4'
      }

      if (this.areaLevel == 2 && this.switch3 == 1) {
        this.switch3 = '2'
      } else if (this.areaLevel == 3 && this.switch3 < 3) {
        this.switch3 = '3'
      } else if (this.areaLevel == 4 && this.switch3 < 4) {
        this.switch3 = '4'
      }

      this.getAreaRelated()
    },
    handleChangeSwitch1(item) {
      if (item.id == this.switch1) return

      this.switch1 = item.id
      if (item.id == '1' && !this.queryParams.year) {
        this.$set(this.queryParams, 'year', new Date().getFullYear() + '')
      } else if (item.id == '2' && !this.queryParams.quarter) {
        // this.$set(this.queryParams, 'month', '')
        this.$set(this.queryParams, 'quarter', Math.ceil((new Date().getMonth() + 1) / 3) + '')
      } else if (item.id == '3' && !this.queryParams.month) {
        this.$set(this.queryParams, 'quarter', '')
        this.$set(this.queryParams, 'month', new Date().getMonth() + 1 + '')
      }
      if (item.id !== '3') {
        this.$set(this.queryParams, 'month', '')
      }
      if (item.id !== '2') {
        this.$set(this.queryParams, 'quarter', '')
      }
      this.getDateRelated()
    },
    handleChangeSwitch2(item) {
      if (this.switch2 == item.id) return
      this.switch2 = item.id
      this.getData3_2()
    },

    handleChangeSwitch4(item) {
      if (this.switch4 == item.id) return
      this.switch4 = item.id
      this.getData3_3()
    },

    getData1_1() {
      getBaseInfoAmount({
        areaCode: this.areaCode,
        year: this.queryParams.year
      }).then((res) => {
        this.data1_1 = {
          totalAmount: res.data.allocatableTotalAmount,
          lcze: res.data.retainTotalAmount,
          bonusAmount: res.data.bonusTotalAmount
        }
        this.data2_1.averageAllocation = res.data.perAmount
      })
    },

    getData1_2() {
      getBonusTrend({
        areaCode: this.areaCode,
        year: this.queryParams.year
      }).then((res) => {
        this.data1_2 = []
        const areaList = []
        const yearList = []
        for (const year in res.data) {
          if (!areaList.length) {
            areaList.push(...Object.keys(res.data[year]))
          }
          yearList.push(year)
        }
        areaList.forEach((area) => {
          const temp = {
            name: area,
            list: getAreaData(area)
          }
          this.data1_2.push(temp)
        })
        console.log(this.data1_2)
        this.initChart1_2()

        function getAreaData(area) {
          const list = []
          for (const year in res.data) {
            list.push({
              year: year,
              value: res.data[year][area] || 0
            })
          }
          return list
        }
      })
    },
    initChart1_2() {
      const chartDom = document.getElementById('chart1_2')
      if (this.chart1_2) {
        this.chart1_2.dispose()
      }
      this.chart1_2 = echarts.init(chartDom)
      const option = {
        color: ['#e23d64', '#f8c340', '#00f3f8', '#00e436', '#00a4fb', '#886eea'],
        legend: {
          data: this.data1_2.map((i) => i.name),
          textStyle: {
            color: '#fff'
          },
          top: '20px'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (e) => {
            return (
              '人均分红趋势：<br />' +
              e
                .map((i) => {
                  return i.marker + i.seriesName + '：' + this.numberFormate(i.value).value + this.numberFormate(i.value).unit
                })
                .join('<br />')
            )
          }
        },
        xAxis: {
          type: 'category',
          data: this.data1_2[0].list.map((i) => i.year + '年'),
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            rotate: 0,
            textStyle: {
              color: '#3ea7e1',
              fontSize: 14
            }
          }
        },
        yAxis: {
          name: '单位：元',
          nameTextStyle: {
            color: '#3ea7e1',
            fontSize: 12
          },
          type: 'value',
          splitLine: {
            lineStyle: {
              color: '#214672'
            }
          },
          axisLabel: {
            textStyle: {
              color: '#3ea7e1',
              fontSize: 14
            }
          }
        },
        grid: {
          left: '80px',
          right: '30px',
          top: '80px',
          bottom: '20px'
        },
        series: []
      }
      this.data1_2.forEach((item) => {
        option.series.push({
          name: item.name,
          data: item.list.map((i) => i.value),
          type: 'line',
          symbol: 'circle',
          symbolSize: 12,
          step: false
        })
      })
      this.chart1_2.setOption(option)
    },
    handleExportData1_2() {
      const url = `/api/shares/shares/screen/exportBonusTrend?areaCode=${this.areaCode}&year=${this.queryParams.year}&page=&rows=`
      window.location.href = url
    },
    searchData1_2_pageData() {
      this.dialogVisible1 = true
      // 接口查询
      this.loading1 = true
      getBonusTrend({
        areaCode: this.areaCode,
        year: this.queryParams.year,
        page: this.page1_2,
        rows: 10
      })
        .then((res) => {
          this.columns1_2 = Object.keys(res.data.rows[0].map || [])
          this.data1_2_table.rows = res.data.rows
          this.data1_2_table.total = res.data.total
          this.$nextTick(() => {
            this.$refs.table1_2.doLayout()
          })
        })
        .finally(() => {
          this.loading1 = false
        })
    },
    handleCurrentChange1_2(val) {
      this.page1_2 = val
      this.searchData1_2_pageData()
    },

    getData1_3() {
      getWelfareRanking({
        areaCode: this.areaCode,
        year: this.queryParams.year
      }).then((res) => {
        this.data1_3 = res.data.rows.map((i) => {
          return {
            orgName: i.orgName,
            value: i.perAmount
          }
        })
      })
    },
    handleExportData1_3() {
      const url = `/api/shares/shares/screen/exportWelfareRanking?areaCode=${this.areaCode}&year=${this.queryParams.year}&page=&rows=`
      window.location.href = url
    },
    searchData1_3_pageData() {
      this.dialogVisible2 = true
      this.loading2 = true
      // 接口查询
      getWelfareRanking({
        areaCode: this.areaCode,
        year: this.queryParams.year,
        page: this.page1_3,
        rows: 10
      })
        .then((res) => {
          this.data1_3_table.rows = res.data.rows
          this.data1_3_table.total = res.data.total
        })
        .finally(() => {
          this.loading2 = false
        })
    },
    handleCurrentChange1_3(val) {
      this.page1_3 = val
      this.searchData1_3_pageData()
    },

    getData3_1() {
      getOrgCount({
        areaCode: this.areaCode,
        year: this.queryParams.year
      }).then((res) => {
        this.data3_1 = [
          { value: res.data.jlsCount, name: '经联社' },
          { value: res.data.jjsCount, name: '经济社' }
          // { value: res.data.gsCount, name: '公司' },
          // { value: res.data.qtCount, name: '其他' }
        ]
        // 处理百分比
        const total = this.data3_1.reduce((total, item) => total + Number(item.value), 0)
        this.data3_1.map((item, index) => {
          if (total == 0) {
            item.rate = '0.00'
          } else {
            item.rate = ((Number(item.value) * 100) / total).toFixed(2) + ''
          }
        })
        if (total) {
          const temp = this.data3_1.find((i) => i.rate !== '0.00')
          temp.rate = (
            100 -
            this.data3_1.reduce((total, item) => {
              if (item.name === temp.name) {
                return total
              } else {
                return total + Number(item.rate)
              }
            }, 0)
          ).toFixed(2)
        }

        this.data2_1.orgNum = res.data.totalCount

        this.initChart3_1()
      })
    },
    initChart3_1() {
      const chartDom = document.getElementById('chart3_1')
      if (this.chart3_1) {
        this.chart3_1.dispose()
      }
      this.chart3_1 = echarts.init(chartDom)
      const option = {
        color: ['#368aff', '#3ad4fa', '#00e436', '#fce84e'],
        grid: {
          left: '0',
          right: '2%',
          containLabel: true
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (e) => {
            return e.marker + e.name + '：' + this.numberFormate(e.value, true, 0).value + '个，占比' + e.data.rate + '%'
          }
        },
        legend: {
          orient: 'vertical',
          left: '40%',
          right: 0,
          top: 'center',
          bottom: 'center',
          selectedMode: false,
          icon: 'roundRect',
          itemHeight: 15,
          itemWidth: 15,
          itemGap: 12,
          textStyle: {
            align: 'right',
            top: -10,
            color: '#89ccf5',
            fontSize: 14,
            rich: {
              name: {
                color: '#89ccf5',
                fontSize: 14,
                width: 60,
                align: 'left'
              },
              value: {
                color: '#89ccf5',
                fontSize: 14,
                width: 120,
                lineHeight: 20
              },
              rate: {
                color: '#89ccf5',
                fontSize: 14
              }
            }
          },
          formatter: (e) => {
            const current = this.data3_1.find((i) => i.name === e)
            const value = current.value
            const rate = current.rate.toString().includes('-') ? 0 : current.rate

            return `{name| ${e}} {value| ${this.numberFormate(value, true, 0).value}个} {rate| ${rate}%}`
          }
        },
        labelLine: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '60%'],
            center: ['20%', '45%'],
            data: this.data3_1,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            itemStyle: {
              normal: {}
            }
          }
        ]
      }
      this.chart3_1.setOption(option)
    },
    getData3_2 (force) {
      this.task3_2 && clearTimeout(this.task3_2)
      if (!force && this.sourceData3_2) {
        this.handleData3_2(this.sourceData3_2)
      } else {
        this.loading3_2 = true
        getMemberData({
          areaCode: this.areaCode,
          year: this.queryParams.year
        }).then((res) => {
          this.sourceData3_2 = res
          this.handleData3_2(res)
          this.loading3_2 = false
        })
      }
    },
    handleData3_2 (res) {
        if (this.switch2 == '1') {
          this.data3_2 = [
            { name: '18岁以下', value: res.data.age1 },
            { name: '18(含)-30岁', value: res.data.age2 },
            { name: '30(含)-40岁', value: res.data.age3 },
            { name: '40(含)-50岁', value: res.data.age4 },
            { name: '50(含)-60岁', value: res.data.age5 },
            { name: '60(含)及以上', value: res.data.age6 }
          ]
        } else {
          this.data3_2 = [
            { name: '男性', value: res.data.sex1 },
            { name: '女性', value: res.data.sex2 }
          ]
        }

        // 处理百分比
        const total = this.data3_2.reduce((total, item) => total + Number(item.value), 0)
        this.data3_2.map((item, index) => {
          if (total == 0) {
            item.rate = '0.00'
          } else {
            item.rate = ((Number(item.value) * 100) / total).toFixed(2) + ''
          }
        })
        if (total) {
          const temp = this.data3_2.find((i) => i.rate !== '0.00')
          temp.rate = (
            100 -
            this.data3_2.reduce((total, item) => {
              if (item.name === temp.name) {
                return total
              } else {
                return total + Number(item.rate)
              }
            }, 0)
          ).toFixed(2)
        }

        this.data2_1.memberNum = res.data.memberNum

        this.initChart3_2()
        this.loading3_2 = false
        this.task3_2 = setTimeout(() => {
          const next = this.getNextOption(this.switch2Options, this.switch2, '3_2')
          if (next) this.handleChangeSwitch2(next)
        }, this.playInterval)
    },
    initChart3_2() {
      const chartDom = document.getElementById('chart3_2')
      if (this.chart3_2) {
        this.chart3_2.dispose()
      }
      this.chart3_2 = echarts.init(chartDom)
      let option = null
      if (this.switch2 == '1') {
        option = {
          color: ['#368aff', '#1fb5fc', '#27e9cb', '#fed130', '#fd895b', '#fc5659'],
          grid: {
            left: '0',
            right: '2%',
            containLabel: true
          },
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(2,68,144,.7)',
            borderColor: 'transparent',
            textStyle: {
              color: '#fff',
              fontSize: 14
            },
            formatter: (e) => {
              const formatV = this.formatPeople(e.value)
              return e.marker + e.name + '：' + formatV.num + formatV.unit + '人，占比' + e.data.rate + '%'
            }
          },
          legend: {
            orient: 'vertical',
            left: '40%',
            right: 0,
            top: 'center',
            bottom: 'center',
            selectedMode: false,
            icon: 'roundRect',
            itemHeight: 15,
            itemWidth: 15,
            itemGap: 12,
            textStyle: {
              align: 'right',
              top: -10,
              color: '#89ccf5',
              fontSize: 14,
              rich: {
                name: {
                  color: '#89ccf5',
                  fontSize: 14,
                  width: 100,
                  align: 'left'
                },
                value: {
                  color: '#89ccf5',
                  fontSize: 14,
                  width: 100,
                  lineHeight: 20
                },
                rate: {
                  color: '#89ccf5',
                  fontSize: 14
                }
              }
            },
            formatter: (e) => {
              const current = this.data3_2.find((i) => i.name === e)
              const value = current.value
              const rate = current.rate.toString().includes('-') ? 0 : current.rate
              const formatV = this.formatPeople(value)
              return `{name| ${e}} {value| ${formatV.num + formatV.unit}人} {rate| ${rate}%}`
            }
          },
          labelLine: {
            show: false
          },
          series: [
            {
              type: 'pie',
              data: this.data3_2,
              radius: [0, '65%'],
              center: ['20%', '50%'],
              label: {
                show: false
              },
              emphasis: {
                label: {
                  show: false
                }
              },
              itemStyle: {
                normal: {}
              }
            }
          ]
        }
      } else {
        option = {
          color: ['#00FFF0', '#FCE84E'],
          tooltip: {
            show: false
          },
          legend: {
            show: false
          },
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: ['35%', '60%'],
              center: ['50%', '50%'],
              // avoidLabelOverlap: false,
              startAngle: 70,
              data: this.data3_2,
              labelLine: {
                length: 20,
                length2: 20
              },
              label: {
                color: 'inherit',
                fontSize: 16,
                formatter: (e) => {
                  const formatV = this.formatPeople(e.value)
                  return `${e.name} \n\n${formatV.num + formatV.unit}人 \n\n占比 ${e.data.rate}%`
                }
              }
            },
            {
              type: 'custom',
              coordinateSystem: 'none',
              silent: true,
              data: [0],
              renderItem(params, api) {
                // 环形图半径
                const r = Math.min(api.getWidth(), api.getHeight()) / 2
                // 圆心
                const center = {
                  x: api.getWidth() / 2,
                  y: api.getHeight() / 2
                }
                // 大圆半径
                const rBig = r * 0.65
                // 大圆上的扇形
                const bigSector = []
                const sectorSize = 30 // 扇形长度（弧度）
                const sectorInterval = 60 // 扇形与扇形之间的间隔
                const BigStartAngle = 300 // 大扇形起始角度
                for (let i = 0; i < 4; i++) {
                  const startAngle = ((i * (sectorInterval + sectorSize) + BigStartAngle) * Math.PI) / 180
                  const endAngle = startAngle + (sectorSize * Math.PI) / 180
                  bigSector.push({
                    type: 'sector',
                    shape: {
                      cx: center.x,
                      cy: center.y,
                      r: rBig,
                      r0: rBig * 0.98,
                      startAngle,
                      endAngle
                    },
                    style: {
                      fill: '#C8932F',
                      lineWidth: 2
                    }
                  })
                }
                return {
                  type: 'group',
                  children: [
                    {
                      type: 'group',
                      children: [
                        ...bigSector,
                        {
                          // 外圆环
                          type: 'arc',
                          shape: {
                            cx: center.x,
                            cy: center.y,
                            r: rBig
                          },
                          style: {
                            fill: 'transparent',
                            stroke: '#C8932F',
                            lineWidth: 1
                          }
                        }
                      ]
                    }
                  ]
                }
              }
            }
          ]
        }
      }

      this.chart3_2.setOption(option)
    },

    getData3_3 () {
      this.task3_3 && clearTimeout(this.task3_3)
      this.loading4 = true
      this.data3_3 = []
      getProfitDistributionRanking({
        areaCode: this.areaCode,
        year: this.queryParams.year,
        areaType: this.switch3,
        orderType: this.switch4
      })
        .then((res) => {
          this.data3_3 = res.data.rows.map((i) => {
            if (this.switch4 == 1) {
              return {
                divName: i.divName,
                value: i.amount
              }
            } else if (this.switch4 == 2) {
              return {
                divName: i.divName,
                value: i.retainAmount
              }
            } else {
              return {
                divName: i.divName,
                value: i.perAmount
              }
            }
          })

          this.task3_3 = setTimeout(() => {
            const next = this.getNextOption(this.switch4Options, this.switch4, '3_3')
            if (next) this.handleChangeSwitch4(next)
          }, this.playInterval)
        })
        .finally(() => {
          this.loading4 = false
        })
    },
    handleChangeSwitch3(item) {
      if (item.id == '5') {
        // 查看更多
        this.dialogVisible3 = true
        this.params3_3.year = this.queryParams.year
        this.params3_3.areaType = this.switch3
        this.params3_3.switch = this.switch4
        this.colTitle = this.switch3 == 1 ? '地区' : this.switch3 == 2 ? '镇街' : this.switch3 == 3 ? '村居' : '组织机构'
        this.getTreeRoot()
        this.getColumns(this.switch4)
      } else {
        if (this.switch3 == item.id) return
        this.switch3 = item.id
        this.getData3_3()
      }
    },
    searchData3_3_pageData() {
      this.showTable = false
      this.loading3 = true
      this.colTitle = this.params3_3.areaType == 1 ? '地区' : this.params3_3.areaType == 2 ? '镇街' : this.params3_3.areaType == 3 ? '村居' : '组织机构'
      // 分页接口
      getProfitDistributionRanking({
        areaCode: this.params3_3.areaCode,
        year: this.params3_3.year,
        areaType: this.params3_3.areaType,
        orderType: this.params3_3.switch,
        page: this.page3_3,
        rows: 10
      })
        .then((res) => {
          this.data1_3_table.rows = res.data.rows
          this.data1_3_table.total = res.data.total
        })
        .finally(() => {
          this.showTable = true
          this.loading3 = false
        })
    },
    handleExportData3_3() {
      const url = `/api/shares/shares/screen/exportProfitDistributionRanking?areaCode=${this.params3_3.areaCode}&year=${this.params3_3.year}&areaType=${this.params3_3.areaType}&orderType=${this.params3_3.switch}&page=&rows=`
      window.location.href = url
    },
    handleChangeDialogSwitch(item) {
      this.params3_3.switch = item.id
      this.getColumns(item.id)
      this.searchData3_3_pageData()
    },
    getColumns(val) {
      if (val == '1') {
        this.columns3_3 = [
          { label: '可分配收益数', prop: 'amount' },
          { label: '留存数', prop: 'retainAmount' },
          { label: '人均分红', prop: 'perAmount' }
        ]
      } else if (val == '2') {
        this.columns3_3 = [
          { label: '留存数', prop: 'retainAmount' },
          { label: '可分配收益数', prop: 'amount' },
          { label: '人均分红', prop: 'perAmount' }
        ]
      } else {
        this.columns3_3 = [
          { label: '人均分红', prop: 'perAmount' },
          { label: '可分配收益数', prop: 'amount' },
          { label: '留存数', prop: 'retainAmount' }
        ]
      }
    },
    handleCurrentChange3_3(val) {
      this.page3_3 = val
      this.searchData3_3_pageData()
    },
    async getTreeRoot() {
      const { data: treeData } = await userTreeData({ areaCode: this.areaCode })
      this.$set(this.params3_3, 'areaName', treeData[0].text)
      this.$set(this.params3_3, 'areaCode', treeData[0].code)
      this.treeData = treeData
      this.searchData3_3_pageData()
    },
    handleNodeChange(data) {
      console.log(data)
      this.$set(this.params3_3, 'areaName', data.text)
      this.$set(this.params3_3, 'areaCode', data.code)
      this.$set(this.params3_3, 'areaType', data.attributes.level - 1)
      this.$refs._areaSelectRef.blur()
    },
    async loadTree() {
      const params = {}
      if (arguments[0]) {
        params.id = arguments[0]
      }
      return await userTreeData(params.id)
    },
    loadChildNode({ id }, resolve) {
      this.loadTree({ id }).then((res) => {
        resolve(res.data)
      })
    },
    formatPeople(value) {
      if (isNaN(value) || parseFloat(value) == 0) {
        return {
          num: 0,
          unit: ''
        }
      }
      const { num, unit } = changeMoney(value, ['', '万', '亿', '万亿'])
      let numStr = (num && num.toLocaleString) ? num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : num
      if (unit == '') numStr = num.toLocaleString()
      return { num: numStr, unit }
      // if (isNaN(value) || parseFloat(value) == 0) {
      //   return {
      //     value: parseFloat(0).toFixed(precision),
      //     unit: '元'
      //   }
      // }
      // const integer = (parseFloat(value) + '').split('.')[0]
      // if (integer.length >= 9) {
      //   return {
      //     value: (parseFloat(value) / 100000000).toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      //     unit: '亿元'
      //   }
      // } else if (integer.length >= 5) {
      //   return {
      //     value: (parseFloat(value) / 10000).toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      //     unit: '万元'
      //   }
      // } else {
      //   return {
      //     value: parseFloat(value)
      //       .toFixed(precision)
      //       .replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      //     unit: '元'
      //   }
      // }
    },
    // 数字超过一万转换为万，超过一亿转为亿
    numberFormate(value, thousands = true, precision = 2) {
      if (isNaN(value) || parseFloat(value) == 0) {
        return {
          value: parseFloat(0).toFixed(precision),
          unit: '元'
        }
      }
      const integer = (parseFloat(value) + '').split('.')[0]
      if (integer.length >= 9) {
        return {
          value: (parseFloat(value) / 100000000).toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          unit: '亿元'
        }
      } else if (integer.length >= 5) {
        return {
          value: (parseFloat(value) / 10000).toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          unit: '万元'
        }
      } else {
        return {
          value: parseFloat(value)
            .toFixed(precision)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          unit: '元'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.rank-header {
  margin: 15px 0 0px 0;
  padding: 10px 0;
  display: flex;
  background: linear-gradient(to right, #15408e 0%, #0e3077 75%, #072957 100%);
  .header-item {
    text-align: center;
    color: #fff;
  }
}

.rank-list {
  flex: 1;
  overflow: hidden;
  // margin-left: 15px;
  .rank-item {
    height: 45px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    &.level1 {
      background: url('@/assets/image/no1.png') no-repeat 100% 100%;
    }
    &.level2 {
      background: url('@/assets/image/no2-3.png') no-repeat;
    }
    &.level3 {
      background: url('@/assets/image/no4.png') no-repeat;
    }
    background-size: 100% 100% !important;

    .rank-order {
      display: block;
      width: 80px;
      text-align: center;
      font-size: 24px;
      color: #fff;
      font-weight: bold;
      margin-left: 3px;
    }
    .area-name {
      flex: 3;
      display: block;
      width: 73px;
      text-align: center;
      font-size: 18px;
      color: #bff7ff;
      line-height: 36px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // text-align: left;
      padding: 0 10px;
    }
    .value {
      flex: 1;
      text-align: center;
      color: #bff7ff;
      // text-align: right;
    }
  }
}

::v-deep .el-select {
  .el-input__inner {
    color: rgb(66, 217, 236) !important;
    background: linear-gradient(to bottom, #005ba8, #002d59) !important;
    border-color: rgb(0, 156, 255) !important;
    font-size: 16px;
  }
  .el-select__caret {
    color: rgb(66, 217, 236) !important;
  }
  .popper__arrow {
    display: none;
  }

  .el-select-dropdown {
    background-color: #005ba8 !important;
    border: none;
    .el-select-dropdown__item {
      color: #fff !important;
      &.selected,
      &.hover {
        background-color: #3ea7e1;
      }
    }
  }
}

.area-time {
  font-size: 14px;
  color: #8fd2fd;
  display: flex;
  flex-direction: row;
  align-items: center;
  .area-img {
    height: 18px;
    width: 18px;
  }
  .area-title {
    margin: 0 10px;
    letter-spacing: 1px;
  }
  .area {
    font-weight: bold;
  }
}

.area-org {
  display: flex;
  flex-direction: row;
  align-items: center;
  // font-style: italic;
  line-height: 24px;
  .area-img {
    height: 18px;
    width: 18px;
  }
  .area-title {
    color: #fff;
    margin: 0 10px;
    font-size: 14px;
    letter-spacing: 1px;
  }
  .area {
    font-weight: bold;
    background: linear-gradient(to top, #d3a27a, #fff);
    -webkit-background-clip: text;
    color: transparent;
  }
}

.block-box {
  background-color: rgba(3 20 61 / 53%);
  box-shadow: rgba(0 123 228 / 50%) 0px 0px 40px 9px inset;
  display: flex;
  flex-direction: column;
  .block-title {
    height: 40px;
    line-height: 45px;
    background: url('@/assets/image/title.png') 0 0 no-repeat;
    padding-left: 36px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
  }
  .block-body {
    flex: 1;
    padding: 0 30px 20px 30px;
  }
}

::v-deep .el-loading-mask {
  background-color: rgba(0, 0, 0, 0) !important; /* 完全透明 */
}
::v-deep .custom-dialog {
  // background: url('@/assets/image/bj.jpg') 0 0 no-repeat;
  background: #132647;
  box-shadow: inset 0 0 60px #3d61a4;
  .el-dialog__header {
    position: relative;
    .title {
      color: #fff;
      font-size: 22px;
      padding-left: 50px;
      background: url('@/assets/image/trading/title.png') 0 0 no-repeat;
    }
    .close-btn {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .el-dialog__body {
    height: 500px;
    padding-top: 0;
    .el-form {
      .el-form-item__label {
        color: #3ea7e1;
        font-size: 16px;
      }
      .el-date-editor {
        background: linear-gradient(to bottom, #005ba8, #002d59) !important;
        border-color: #3ea7e1 !important;
        .el-icon-date {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range-input {
          color: #3ea7e1 !important;
          background: transparent;
          border-color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range-separator {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range__close-icon {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-select__caret {
          color: #3ea7e1 !important;
        }
        .popper__arrow {
          display: none;
        }
        .el-picker-panel {
          background-color: #3ea7e1 !important;
          color: #fff !important;
          .el-picker-panel__icon-btn,
          .el-date-table th,
          .el-date-table td {
            color: #fff !important;
          }
          .el-date-table td.in-range {
            color: #3ea7e1 !important;
          }
        }
      }
    }
    .search-btn {
      // background-color: #005ba8;
      background-color: #72deff;
      // border-color: #3ea7e1;
      border-radius: 6px;
      color: #132649;
      font-size: 14px;
      padding: 6px 15px;
      font-weight: bold;
      border: none;
    }
    .switch-btn {
      cursor: pointer;
      padding: 4px 15px;
      color: #d1d1d1;
      background-color: #132649;
      border: 1px solid #1058a4;
      &:nth-child(1) {
        border-right-color: #0e8bff;
      }
      &:nth-child(2) {
        border-left: none;
      }
      &.selected {
        color: #72deff;
      }
    }
    .el-table {
      background-color: transparent;
      border: none;
      &::before {
        display: none;
      }
      .el-table__body-wrapper {
        &::-webkit-scrollbar {
          width: 10px !important;
          height: 100% !important;
        }
        &::-webkit-scrollbar-track {
          background-color: rgba(62, 167, 225, 0.1) !important;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 5px !important;
          background-color: rgba(62, 167, 225, 0.2) !important;
        }
      }
      th.gutter {
        display: none !important;
        width: 0 !important;
      }

      colgroup col[name='gutter'] {
        display: none;
        width: 0;
      }

      .el-table__body {
        width: 100% !important;
      }

      th,
      td {
        border: none;
      }
      th {
        font-size: 14px;
        color: #4fd9fc;
        background-color: #02398b;
      }
      tr,
      td {
        font-size: 14px;
        color: #4fd9fc;
        background-color: transparent;
      }
      tr.el-table__row--striped td {
        background-color: rgba(26, 58, 96, 0.5);
      }
    }
  }

  .el-loading-mask {
    background-color: rgba(0, 0, 0, 0) !important; /* 完全透明 */
  }
}

::v-deep .el-pagination {
  color: #fff;
  .el-pagination__total {
    color: #fff;
  }
  .el-pagination__jump {
    color: #fff;
    .el-input__inner {
      color: #fff;
      background: transparent;
      border-color: #3ea7e1 !important;
      font-size: 15px;
      font-weight: bold;
    }
  }
  .btn-prev,
  .el-pager .number,
  .btn-next {
    color: #fff;
    background: transparent;
    font-size: 15px;
    font-weight: bold;
    border-color: #3ea7e1 !important;
  }

  .el-pager .number.active {
    color: #3ea7e1 !important;
  }

  .more {
    background: transparent;
    color: #fff;
  }
}
.container {
  height: calc(100% - 110px);
  display: flex;
  color: #3ea7e1;
  padding: 0 20px 20px;

  .chart {
    height: 100%;
  }
  .left-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    .box3_1 {
      flex: 24;
    }
    .box3_2 {
      //flex: 22;
      height: 250px;
      margin: 20px 0;
      position: relative;
      .switch-box {
        position: absolute;
        right: 20px;
        top: 16px;
      }
      .block-body {
        padding: 0;
      }
    }
    .box1_1 {
       flex: 22;

      .block-body {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding-top: 20px;
        .box {
          width: calc(50% - 5px);
          display: flex;
          align-items: center;
          margin: 10px 0;
          // height: 50%;
          .img {
            height: calc(100% - 20px);
            // margin: 25px 0;
          }
          .detail-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-left: 10px;
            justify-content: center;
            .value {
              text-align: left;
              font-size: 26px;
              color: #1ce2dc;
            }
            .title {
              text-align: left;
              color: #fff;
            }
          }
        }
      }
    }
  }
  .center-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0 20px;
    .top-container {
      flex: 6;
      display: flex;
      flex-direction: column;
      .tooltip {
        .info {
          display: flex;
          justify-content: space-between;
        }
      }
      .map {
        flex: 1;
        height: 100%;
        width: 100%;
      }
    }
    .bottom-container {
      flex: 2;
      display: flex;
      flex-direction: column;
      .box2_1 {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 20px;
        font-weight: bold;
        margin: 20px 0;
      }
      .box2_2 {
        flex: 1;
        height: 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        .block-box {
          width: calc(50% - 10px);
          height: calc(100% - 10px);
          .block-body {
            padding: 0;
            padding-left: 10px;
            display: flex;
            align-items: center;
            .img {
              height: calc(100% - 60px);
              margin: 25px 0;
            }
            .detail-info {
              flex: 1;
              display: flex;
              flex-direction: column;
              margin-left: 30px;
              .value {
                font-size: 26px;
                color: #fff;
              }
              .title {
                color: #1ce2dc;
              }
            }
          }
        }
      }
    }
  }
  .right-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .box3_3 {
      flex: 24;
      position: relative;
      .switch-box {
        position: absolute;
        right: 20px;
        top: 16px;
      }
      .switch-box1 {
        position: absolute;
        left: 20px;
        top: 50px;
      }
      .block-body {
        display: flex;
        flex-direction: column;
        // padding: 0;
        padding-top: 35px;
        padding-bottom: 0;
      }
      .rank-header {
        padding: 2px 0;
        margin: 10px 0 0px 0;
      }
      .rank-item {
        height: 35px;
        margin: 7px 0;
      }
    }
    .box1_2 {
      // flex: 22;
      height: 250px;
      margin: 20px 0;
      position: relative;
      .switch-box {
        position: absolute;
        right: 20px;
        top: 16px;
      }
    }
    .box1_3 {
      flex: 22;
      position: relative;
      height: 0;

      .switch-box {
        position: absolute;
        right: 20px;
        top: 16px;
      }

      .block-body {
        // padding: 0;
        padding-bottom: 0;
        height: 0;
        display: flex;
        flex-direction: column;
        .rank-header {
          padding: 5px 0;
          margin: 10px 0 0px 0;
        }
        .rank-item {
          height: 35px;
          margin: 8px 0;
        }
      }
    }
  }
}
</style>
