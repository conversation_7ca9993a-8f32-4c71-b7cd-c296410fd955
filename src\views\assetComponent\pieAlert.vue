<template>
  <div ref="pieAlert" class="chart"></div>
</template>

<script>
import { init } from 'echarts'
export default {
  name: 'PieAlert',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showLabel: {
      type: Boolean,
      default: false
    },
    wrapLegend: {
      type: Boolean,
      default: false
    }, // 是否折叠显示legend
    showValue: {
      type: Boolean,
      default: true
    }, // 是否显示legend 中的value
    internalDiameterRatio: {
      type: Number,
      default: 0.6
    }, // 透明空心所占比率0-1
    opacity: {
      type: Number,
      default: 0.8
    }, // 不透明度 0-1
    ratio: {
      type: Number,
      default: 0
    }, // 占比
    unit: {
      type: String,
      default: ''
    } // 数据的单位
  },
  data() {
    return {
      myChart: null,
      option: {
        title: {
          text: `${this.ratio}%`,
          subtext: '',
          left: 'center',
          top: 'center', // top待调整
          textStyle: {
            color: '#f0d12d',
            fontSize: '16',
            fontFamily: 'DINAlternate-Bold'
          },
          subtextStyle: {
            color: '#fff',
            fontSize: 15,
            fontFamily: 'PingFangSC-Regular',
            top: 'center'
          },
          itemGap: -4 // 主副标题间距
        },
        series: [
          {
            name: 'pie1',
            type: 'pie',
            clockWise: true,
            radius: ['27px', '22px'],
            itemStyle: {
              normal: {
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            data: [
              {
                value: this.ratio,
                name: 'completed',
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    borderRadius: 5,
                    color: {
                      // 完成的圆环的颜色
                      colorStops: [
                        {
                          offset: 0,
                          color: 'rgba(255, 194, 40, 0.8)' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: 'rgba(255, 194, 40, 0.1)' // 100% 处的颜色
                        }
                      ]
                    },
                    label: {
                      show: false
                    },
                    labelLine: {
                      show: false
                    }
                  }
                }
              },
              {
                name: 'gap',
                value: 100 - this.ratio,
                itemStyle: {
                  normal: {
                    label: {
                      show: false
                    },
                    labelLine: {
                      show: false
                    },
                    color: 'rgba(0, 0, 0, 0.2)',
                    borderColor: 'rgba(0, 0, 0, 0.2)',
                    borderWidth: 0
                  }
                }
              }
            ]
          }
        ]
      }
    }
  },
  computed: {
    // total() {
    //   const total = this.data.reduce((total, { value = 0 }) => total + value, 0)
    //   return total
    // }
  },
  watch: {
    data: {
      deep: true,
      handler(newVal) {
        console.log(newVal)
        if (this.myChart) {
          this.myChart.clear()
        }
        this.setChart()
      }
    }
  },
  mounted() {
    this.setChart()
    // 根据窗口变化自动调节图表大小
    const that = this
    window.onresize = function () {
      that.changeSize()
    }
  },
  methods: {
    // 图表初始化
    setChart() {
      const chart = init(this.$refs.pieAlert)
      chart.setOption(this.option)
      this.myChart = chart
    },
    // 自适应宽高
    changeSize() {
      this.myChart.resize()
    }
  }
}
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
