<template>
  <div style="display: inline-block">
    <div class="num-box">
      <div v-for="(item, index) of String(integerPart).length" :key="`num${index}`" :class="isNaN(String(integerPart)[index]) ? 'point-span' : 'num-span'">
        {{ String(integerPart)[index] }}
        <!-- <div class="bg1"></div>
        <div class="split"></div>
        <div class="bg2"></div> -->
      </div>
      <div v-if="precision" class="point-span">.</div>
      <div v-for="(item, index) of String(decimalPart).length" :key="`number${index}`" class="num-span">
        {{ String(decimalPart)[index] }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NumberFlash',
  props: {
    number: {
      type: Number,
      default: 0
    },
    precision: {
      // 小数位精度
      type: Number,
      default: 2
    },
    prefix: {
      // 前缀符号
      type: String,
      default: ''
    },
    unit: {
      // 单位
      type: String,
      default: ''
    }
  },
  data() {
    return {
      numberStr: null,
      numberLength: 0,
      integerPart: [],
      decimalPart: []
    }
  },
  watch: {
    number: {
      handler(val) {
        if (val == null || isNaN(val)) return
        this.init()
      },
      immediate: true
    }
  },
  created() {
    // this.init()
  },
  methods: {
    init() {
      if (String(parseInt(this.number)).length > 12) {
        this.numberStr = (this.number / 100000000).toFixed(this.precision)
      } else if (String(parseInt(this.number)).length > 8) {
        this.numberStr = (this.number / 10000).toFixed(this.precision)
      } else {
        this.numberStr = this.number.toFixed(this.precision)
      }
      //   this.numberStr = this.number.toFixed(this.precision)
      this.numberLength = this.precision === 0 ? this.numberStr.length : this.numberStr.length - 1
      this.createFlash()
    },
    createFlash() {
      // 获取flash数据
      let integerPartTemp = []
      let decimalPartTemp = []
      if (this.precision) {
        integerPartTemp = this.numberStr.split('.')[0]
        decimalPartTemp = this.numberStr.split('.')[1]
      } else {
        integerPartTemp = this.numberStr
        decimalPartTemp = []
      }

      const flashFreq = 80 // 闪动频率
      const flashComplete = 300 // 闪动持续的时间

      let integerInterval = null
      let decimalInterval = null

      const integerPartlength = integerPartTemp.length
      const decimalPartlength = decimalPartTemp ? decimalPartTemp.length : 0

      // 闪烁
      integerInterval = setInterval(() => {
        this.integerPart = this.getRandom(integerPartlength)
        this.integerPart = this.numFormat(this.integerPart)
      }, flashFreq)

      decimalInterval = setInterval(() => {
        this.decimalPart = this.getRandom(decimalPartlength)
        this.decimalPart = this.numFormat(this.decimalPart)
      }, flashFreq)

      // 停止闪烁
      setTimeout(() => {
        clearInterval(integerInterval)
        clearInterval(decimalInterval)
        this.integerPart = this.numFormat(integerPartTemp)
        this.decimalPart = decimalPartTemp
      }, flashComplete)
    },
    getRandom(n) {
      const add = 1
      let max = 12 - add

      if (n > max) {
        return this.generate(max) + this.generate(n - max)
      }

      max = Math.pow(10, n + add)
      const min = max / 10 // Math.pow(10, n) basically
      const number = Math.floor(Math.random() * (max - min + 1)) + min

      return ('' + number).substring(add)
    },
    numFormat(num) {
      const res = num.toString().replace(/\d+/, function (n) {
        // 先提取整数部分
        return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
          return $1 + ','
        })
      })
      return res
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../assets/fonts/digitalFont/stylesheet.css';
.num-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 0 8px;
  .num-span {
    width: 40px;
    height: 42px;
    display: inline-block;
    font-size: 42px;
    font-weight: normal;
    text-align: center;
    color: #fff;
    margin: 0 5px;
    display: flex;
    flex-direction: column;
    font-family: 'ds-digitalnormal';
    background: url(@/assets/image/number-bg.png) 0 0 no-repeat;
  }
  .point-span {
    font-size: 45px;
    font-weight: bold;
    color: #fff;
    font-family: serif;
  }
}
</style>
