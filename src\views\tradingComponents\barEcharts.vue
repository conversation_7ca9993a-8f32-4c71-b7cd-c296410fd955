<!--
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-08-18 13:44:33
 * @LastEditors: Andy
 * @LastEditTime: 2023-08-31 16:59:12
-->
<template>
  <div id="barEcharts" class="bar-box"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  components: {},
  props: {
    echartData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  watch: {
    echartData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.$nextTick(() => {
          this.initEcharts()
        })
      }
    }
  },
  created() {},
  methods: {
    initEcharts() {
      const barEcharts = document.getElementById('barEcharts')
      this.myChart = echarts.init(barEcharts)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          position: (point, params, dom, rect, size) => {
            const options = {
              point,
              params,
              dom,
              rect,
              size
            }
            return this.position(options)
          },
          formatter: (params) => {
            let str = ''
            for (const key in params) {
              str += `${params[key].axisValueLabel}<br />${params[key].marker}&nbsp;&nbsp;&nbsp;&nbsp;${params[key].value + '人'}`
            }
            return str
          }
        },
        color: '#07e3fe',
        grid: {
          left: '15%',
          bottom: '30',
          top: '30'
        },
        xAxis: [
          {
            type: 'category',
            data: ['围观人数', '报名人数', '申报优先权人数', '出价人数'],
            axisLabel: {
              textStyle: {
                color: '#65ABE7',
                fontSize: 12
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisPointer: {
              type: 'none'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              textStyle: {
                color: '#65ABE7',
                fontSize: 14
              }
            },
            splitLine: {
              lineStyle: {
                width: 2,
                color: '#123d72'
              }
            }
          }
        ],
        series: [
          {
            type: 'bar',
            emphasis: {
              focus: 'series'
            },
            data: this.echartData.seriesData,
            barWidth: 10,
            itemStyle: {
              borderRadius: [5, 5, 0, 0],
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#06e9ff'
                  },
                  {
                    offset: 0.5,
                    color: '#0592fb'
                  }
                ]
              }
            }
          }
        ]
      }
      this.myChart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.bar-box {
  margin: 0 auto;
  width: 100%;
  height: 100%;
}
</style>
