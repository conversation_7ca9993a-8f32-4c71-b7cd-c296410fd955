import axios from 'axios'
import { Notification } from 'element-ui'
import { message as Message } from '@/utils/message.js'
import errorCode from '@/utils/errorCode'
import Cookies from 'js-cookie'

axios.defaults.withCredentials = true
axios.defaults.headers['Content-Type'] = 'application/x-www-form-urlencoded'
// axios.defaults.headers['X-user-token-header'] = getToken()

// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  // baseURL: process.env.VUE_APP_BASE_API,
  // 超时
  timeout: 60000
})

function transformRequest (data) {
  let ret = ''
  for (const it in data) {
    ret += encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
  }
  ret = ret.substring(0, ret.lastIndexOf('&'))
  return ret
}

// request拦截器
// eslint-disable-next-line complexity
service.interceptors.request.use(config => {
  // const userToken = getToken()
  const userToken = ''// 8e7050c1-8aa8-4001-af02-619b674d546b
  const tenant_id = Cookies.get('X-tenant-id-header')
  if (userToken && userToken !== 'undefined' && !config.url.includes('getPublicKey')) {
    config.headers['X-user-token-header'] = userToken
  }
  if (tenant_id) {
    config.headers['X-tenant-id-header'] = tenant_id
  }
  if (config.mock) {
    config.url = process.env.VUE_APP_BASE_API + config.url
  } else {
    config.url = process.env.VUE_APP_BASE_API + config.url
  }
  if (config.payload) {
    config.headers['Content-Type'] = 'application/json'
  } else {
    // 转为formdata数据格式
    config.data = transformRequest(config.data)
  }

  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?'
    for (const propName of Object.keys(config.params)) {
      const value = config.params[propName]
      var part = encodeURIComponent(propName) + '='
      if (value !== null && typeof (value) !== 'undefined') {
        if (typeof value === 'object') {
          for (const key of Object.keys(value)) {
            if (value[key] !== null && typeof (value[key]) !== 'undefined') {
              const params = propName + '[' + key + ']'
              const subPart = encodeURIComponent(params) + '='
              url += subPart + encodeURIComponent(value[key]) + '&'
            }
          }
        } else {
          url += part + encodeURIComponent(value) + '&'
        }
      }
    }
    url = url.slice(0, -1)
    config.params = {}
    config.url = url
  }
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})
// 响应拦截器
service.interceptors.response.use(res => {
  const headers = res.headers
  const tenant_id = headers['tenantid'] || 'default'
  if (tenant_id !== Cookies.get('X-tenant-id-header')) {
    Cookies.set('X-tenant-id-header', tenant_id)
  }
  // 未设置状态码则默认成功状态
  const code = res.code && res.code instanceof Number ? res.code : 200
  // 获取错误信息
  const msg = errorCode[code] || res.data.message || errorCode['default']
  if (code === 400 || code === 500) {
    Message({
      message: msg,
      type: 'error',
      dangerouslyUseHTMLString: true
    })
    return Promise.reject(new Error(msg))
  } else if (code !== 200) {
    Notification.error({
      title: msg
    })
    return Promise.reject('error')
  } else {
    const { returnCode, message } = res.data

    if ((returnCode && returnCode !== '0') || returnCode === '') {
      Message({
        message: message,
        type: 'error',
        dangerouslyUseHTMLString: true
      })
      return Promise.reject(res.data)
    }
    return res.data
  }
}, ({ response }) => {
  if (!response) {
    console.error('请求已关闭')
    return {
      data: {
        code: 666,
        cancel: true,
        errorMsg: '请求已关闭'
      }
    }
  }
  const { data: { message, status } } = response
  const msg = errorCode[status] || message || errorCode['default']
  if (response.status === 401 && response.data.returnCode === 'B08001') {
    return Promise.reject(response)
  }
  Message({
    message: msg,
    type: 'error',
    dangerouslyUseHTMLString: true
  })
  Promise.reject(response)
}
)

export default service
