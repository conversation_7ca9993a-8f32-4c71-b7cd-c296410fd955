import request from '@/utils/request'
import axios from 'axios'

// 重点资产类型成交分析
export function getAssetClassDealList(data) {
  return request({
    url: '/warehouse/da/trading/getAssetClassDealList',
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

// 根据时间节点获取每个地区成交情况
export function getProjectInfos(data) {
  return request({
    url: '/warehouse/da/trading/getProjectInfos',
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

// 根据时间节点获取访问流量情况
export function getVistisStatistics(data) {
  return axios({
    url: window.homeData.tradingWebService + '/anon/bidHome/bidVisits/statistics',
    method: 'post',
    data: JSON.stringify(data),
    headers: {
      'content-type': 'application/json'
    }
  })
}

// 按年，月，日获取交易保证金入账趋势
export function getTrendBidAccountRecords(data) {
  return request({
    url: '/trading/bidAccount/getOccurSumDetails',
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

// 各地区成交明细列表，按成交宗数或者成交金额排序
export function getAreaDealList(data) {
  return request({
    url: '/warehouse/da/trading/getAreaDealList',
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

// 获取每年/季/月的交易项目成交趋势
export function getDealTrendList(data) {
  return request({
    url: '/warehouse/da/trading/getDealTrendList',
    method: 'post',
    data,
    payload: true,
    withCredentials: true
  })
}

// 获取统计截止时间
export function getDeadlineTime(data) {
  return request({
    url: '/warehouse/da/trading/getDeadlineTime',
    method: 'post',
    withCredentials: true
  })
}
