import request from '@/utils/request'

// 基本信息栏目
export function getCategoryId(data) {
  return request({
    url: `/cms/category/list.do`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 查询栏目文章
export function getContent(data) {
  return request({
    url: `/cms/content/list.do`,
    method: 'post',
    data,
    withCredentials: true
  })
}

// 查询机构
export function getAreaTree(data) {
  return request({
    url: `/cms/content/selectAreaTree.do`,
    method: 'get',
    params: data,
    withCredentials: true,
    directConnection: 1
  })
}
// 查询镇
export function selectAllTownList(data) {
  return request({
    url: `/cms/content/selectAllTownList.do`,
    method: 'get',
    params: data,
    withCredentials: true,
    directConnection: 1
  })
}
// 财务公开查询机构
export function selectAllAreaTree(data) {
  return request({
    url: `/cms/content/selectAllAreaTree.do`,
    method: 'get',
    params: data,
    withCredentials: true,
    directConnection: 1
  })
}

// 查询详情
export function getView(data) {
  return request({
    url: `/cms/content/get.do`,
    method: 'get',
    params: data,
    withCredentials: true
  })
}

// 查询栏目文章
export function getContentfin(data) {
  return request({
    url: `/cms/villageData/findList`,
    method: 'post',
    data,
    withCredentials: true,
    directConnection: 1
  })
}
export function findDetail(data) {
  return request({
    url: `/cms/villageData/findDetail`,
    method: 'post',
    data,
    withCredentials: true,
    directConnection: 1
  })
}

export function getDo(data) {
  return request({
    url: `/cms/content/get.do`,
    method: 'get',
    params: data,
    withCredentials: true
  })
}

// 首页交易动态list
export function tadingNotice(data) {
  return request({
    url: `/cms/tadingNotice/findList`,
    method: 'post',
    data,
    withCredentials: true,
    directConnection: 1
  })
}

// 临期资产list
export function temporaryAssets(data) {
  return request({
    url: `/cms/temporaryAssets/findList`,
    method: 'post',
    data,
    withCredentials: true,
    directConnection: 1
  })
}
