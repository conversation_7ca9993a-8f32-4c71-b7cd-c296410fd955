import request from '@/utils/request'

// 年份列表
export function getYearList() {
  return request({
    url: '/integrated/report/bigScreen/getYear',
    method: 'get'
  })
}

// 基础信息
export function getBaseData(params) {
  return request({
    url: '/integrated/report/bigScreen/getBaseData',
    method: 'get',
    params
  })
}

// 总收入分析-收入总额
export function getIncomeTotal(params) {
  return request({
    url: '/integrated/report/bigScreen/getIncomeTotal',
    method: 'get',
    params
  })
}

// 总收入分析-收入趋势
export function getIncomeTrend(params) {
  return request({
    url: '/integrated/report/bigScreen/getIncomeTrend',
    method: 'get',
    params
  })
}

// 收支结构分析
export function getIncomeExpenseStructuralAnalysis(params) {
  return request({
    url: '/integrated/report/bigScreen/getIncomeExpenseStructuralAnalysis',
    method: 'get',
    params
  })
}

// 总资产情况-收益总额
export function getProfitTotal(params) {
  return request({
    url: '/integrated/report/bigScreen/getProfitTotal',
    method: 'get',
    params
  })
}

// 总资产情况-资产总额
export function getAssetSituationTotal(params) {
  return request({
    url: '/integrated/report/bigScreen/getAssetSituationTotal',
    method: 'get',
    params
  })
}

// 总资产情况-资产构成
export function getAssetSituationCompose(params) {
  return request({
    url: '/integrated/report/bigScreen/getAssetSituationCompose',
    method: 'get',
    params
  })
}

// 在管资金-资金构成
export function getManagingFundCompose(params) {
  return request({
    url: '/integrated/report/bigScreen/getManagingFundCompose',
    method: 'get',
    params
  })
}

// 在管资金-资金趋势
export function getManagingFundTrend(params) {
  return request({
    url: '/integrated/report/bigScreen/getManagingFundTrend',
    method: 'get',
    params
  })
}

// 经济收入超亿元村（社区）情况
export function getMillionVillage(params) {
  return request({
    url: '/integrated/report/bigScreen/getMillionVillage',
    method: 'get',
    params
  })
}

// 经济收入超亿元村（社区）情况
export function contractInfo(data) {
  return request({
    url: '/contractT/bigScreenSumUp/contractInfo',
    method: 'post',
    data
  })
}
