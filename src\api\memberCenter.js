import request from '@/utils/request'

// 注册
export function validateRegister(params) {
  return request({
    url: '/bid/anon/userApi/newRegister',
    method: 'post',
    payload: true,
    data: params,
    withCredentials: true
  })
}

// 交易信息
export function participateProjList(params) {
  return request({
    url: '/bid/anon/userApi/participateProjList',
    method: 'post',
    payload: true,
    data: params,
    withCredentials: true
  })
}
// 修改手机号
export function updatePhone(params) {
  return request({
    url: '/bid/anon/userApi/updatePhone',
    method: 'post',
    data: params,
    withCredentials: true
  })
}

// 个人信息
export function findUserDetail(params) {
  return request({
    url: '/bid/anon/userApi/findUserDetail',
    method: 'post',
    data: params,
    withCredentials: true
  })
}

// 修改密码
export function updatePasswordOnLogin(params) {
  return request({
    url: '/bid/anon/userApi/updatePasswordOnLogin',
    method: 'post',
    data: params,
    withCredentials: true
  })
}

export function updatePasswordNotLogin(params) {
  return request({
    url: '/bid/anon/userApi/updatePasswordNotLogin',
    method: 'post',
    data: params,
    withCredentials: true
  })
}

// 修改个人信息
export function newUpdateUserInfo(params) {
  return request({
    url: '/bid/anon/userApi/newUpdateUserInfo',
    method: 'post',
    payload: true,
    data: params,
    withCredentials: true
  })
}
