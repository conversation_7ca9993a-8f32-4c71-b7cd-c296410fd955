<!-- 看板-边框组件 :style="{ width: width, height: height }"-->
<template>
  <div class="KBPanel-template" :style="{ width: width, height: height }">
    <div class="KBPanel-wrapper" :style="{ borderRadius: borderRadius }">
      <!-- 主体内容 -->
      <div v-if="title" class="KBPanel-head">
        <h3 :style="{ width: titleWidth }">
          <span>{{ title }}</span>
        </h3>
        <slot name="area" />
      </div>
      <div class="KBPanel-body" :style="{ height: bodyHeight }">
        <div class="KBPanel-body-wrapper">
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Panel',
  props: {
    title: {
      // 标题
      type: String,
      default: ''
    },
    titleWidth: {
      // 标题宽度
      type: String,
      default: '150px'
    },
    width: {
      // 面板宽度
      type: String,
      default: '100%'
    },
    height: {
      // 面板高度
      type: String,
      default: '100%'
    },
    borderRadius: {
      type: String,
      default: ''
    },
    // 主题高度
    bodyHeight: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  created() {},
  methods: {
    init() {}
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/css/mixin.scss';
.KBPanel-template {
  .KBPanel-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: rgba(3 20 61 / 53%);
    box-shadow: rgba(0 123 228 / 50%) 0px 0px 40px 9px inset;
    // box-shadow: inset 0 0 50px rgb(2 68 144);

    .KBPanel-head {
      height: 40px;
      justify-content: space-between;
      @include flex-center;
      & > h3 {
        height: 40px;
        @include flex-center;
        line-height: 40px;
        background: url('../assets/image/title.png') 0 0 no-repeat;
        background-size: cover;
        & > span {
          display: block;
          @include font(18px, #fff);
          font-weight: bold;
          height: 100%;
          padding-left: 50px;
          position: relative;
        }
      }
    }

    .KBPanel-body {
      height: calc(100% - 40px);
      overflow: hidden;
      // padding: 10px;
      .KBPanel-body-wrapper {
        height: 100%;
      }
    }

    // title在左边
    &.titlePosition-left {
      .corner-t-l {
        display: none;
      }

      .line-l {
        height: calc(100% - 42px);
        top: 30px;
      }
      .KBPanel-head {
        height: 30px;
        @include flex-center;
        padding-right: 12px;
        & > h3 {
        }

        .head-intro-r {
          display: block;
          width: 100%;
          height: 100%;
          flex: 1;
          position: relative;
        }
      }
    }
  }
}
</style>
