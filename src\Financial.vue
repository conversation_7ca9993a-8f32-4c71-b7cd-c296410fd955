<template>
  <div class="container">
    <div class="left-container">
      <div
        class="box1_1 block-box"
        style="height: 303px"
        @mouseenter.stop="stopHandleMouseEnter('task3_1')"
        @mouseleave.stop="startHandleMoseLeave('task3_1', 'switchOptions4', 'switch4', 'handleChangeSwitch4')"
      >
        <div class="block-title">在管资金</div>
        <div class="switch-box">
          <span v-for="item in switchOptions4" :key="item.id" class="radio-btn" :class="switch4 == item.id ? 'selected' : ''" @click="handleChangeSwitch4(item.id)">
            {{ item.text }}
          </span>
        </div>
        <div class="switch-box1" style="margin-left: 15px">
          <template v-for="item in switchOptions7">
            <span v-show="switch4 === '2'" :key="item.id" class="radio-btn1" :class="switch1_1 == item.id ? 'selected' : ''" @click="handleChangeSwitch1_1(item.id)">
              {{ item.text }}
            </span>
          </template>
        </div>
        <div id="financial_chart3_1"></div>
      </div>

      <div
        class="box1_3 block-box"
        style="height: 303px"
        @mouseenter.stop="stopHandleMouseEnter('task1_3')"
        @mouseleave.stop="startHandleMoseLeave('task1_3', 'switchOptions3', 'switch3', 'handleChangeSwitch3')"
      >
        <div class="block-title">财务情况分析</div>
        <div class="switch-box">
          <span v-for="item in switchOptions3" :key="item.id" class="radio-btn" :class="switch3 == item.id ? 'selected' : ''" @click="handleChangeSwitch3(item.id)">
            {{ item.text }}
          </span>
        </div>
        <div class="totalCount">
          <img class="totalImg" :src="require(`@/assets/image/financial/total-icon2-${switch3}.png`)" alt="" />
          <span class="totalText">{{ totalLabel3[switch3] }}：</span>
          <number-flash-copy class="totalNum" :number="getTotalMoney(total1_3).num" :precision="2" />
          <span class="totalText">{{ getTotalMoney(total1_3).unit }}</span>
        </div>
        <div id="financial_chart1_3"></div>
      </div>

      <div
        class="box1_2 block-box"
        style="height: 303px"
        @mouseenter.stop="stopHandleMouseEnter('task1_2')"
        @mouseleave.stop="startHandleMoseLeave('task1_2', 'switchOptions2', 'switch2', 'handleChangeSwitch2')"
      >
        <div class="block-title">收益分配情况分析</div>
        <div class="switch-box">
          <span v-for="item in switchOptions2" :key="item.id" class="radio-btn" :class="switch2 == item.id ? 'selected' : ''" @click="handleChangeSwitch2(item.id,true)">
            {{ item.text }}
          </span>
        </div>
        <div v-if="switch2 == '3'" class="totalCount">
          <img class="totalImg" src="@/assets/image/financial/total-icon3.png" alt="" />
          <span class="totalText">收益总额：</span>
          <number-flash-copy class="totalNum" :number="getTotalMoney(total1_2).num" :precision="2" />
          <span class="totalText">{{ getTotalMoney(total1_2).unit }}</span>
        </div>
        <div id="financial_chart1_2" :style="switch2 == '3'?'height:200px;':''"></div>
      </div>
    </div>

    <div class="center-container">
      <div class="box2_1">
        <div class="tooltip">
          <div class="info">
            <div class="area-org">
              <img class="area-img" src="@/assets/image/icon1.png" alt="" />
              <span class="area-title" style="font-size: 18px">当前地区：</span>
              <span class="area" style="font-size: 24px">| {{ currentArea }} |</span>
            </div>

            <div class="area-time">
              <img class="area-img" src="./assets/image/date.png" alt="" />
              <span class="area-title" style="font-size: 18px">统计截止时间：</span>
              <span class="area" style="font-size: 18px">{{ currentDate }}</span>
            </div>
          </div>
          <div class="info" style="margin-top: 20px">
            <div></div>
            <div style="display: flex">
              <el-select v-model="queryParams.year" style="width: 100px" placeholder="" :popper-append-to-body="false" @change="handleDateChange">
                <el-option v-for="item in yearOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <mapNav class="map" :area-code="areaCode" @gotopath="gotopath" />
      </div>
      <div class="box2_2">
        <div class="box">
          <img class="img" src="@/assets/image/financial/icon1.png" alt="" />
          <div class="detail-info">
            <div class="value">
              {{ numberFormate(baseData.booksCount, true, 0, false) }}
              <font style="font-size: 12px">套</font>
            </div>
            <div class="title">账套数量</div>
          </div>
        </div>
        <div class="box">
          <img class="img" src="@/assets/image/financial/icon2.png" alt="" />
          <div class="detail-info">
            <div class="value">
              {{ formateMoney(baseData.assetTotal, false).num | formateNumber }}
              <font style="font-size: 12px">{{ formateMoney(baseData.assetTotal, false).unit }}</font>
            </div>
            <div class="title">资产总值</div>
          </div>
        </div>
        <div class="box">
          <img class="img" src="@/assets/image/financial/icon6.png" alt="" />
          <div class="detail-info">
            <div class="value">
              {{ formateMoney(baseData.liabilitiesTotal, false).num | formateNumber }}
              <font style="font-size: 12px">{{ formateMoney(baseData.liabilitiesTotal, false).unit }}</font>
            </div>
            <div class="title">负债总值</div>
          </div>
        </div>
        <div class="box">
          <img class="img" src="@/assets/image/financial/icon3.png" alt="" />
          <div class="detail-info">
            <div class="value">
              {{ formateMoney(baseData.ownerEquityTotal, false).num | formateNumber }}
              <font style="font-size: 12px">{{ formateMoney(baseData.ownerEquityTotal, false).unit }}</font>
            </div>
            <div class="title">所有者权益总值</div>
          </div>
        </div>
        <div class="box">
          <img class="img" src="@/assets/image/financial/icon4.png" alt="" />
          <div class="detail-info">
            <div class="value">
              {{ formateMoney(baseData.incomeTotal, false).num | formateNumber }}
              <font style="font-size: 12px">{{ formateMoney(baseData.incomeTotal, false).unit }}</font>
            </div>
            <div class="title">总收入</div>
          </div>
        </div>
        <div class="box">
          <img class="img" src="@/assets/image/financial/icon5.png" alt="" />
          <div class="detail-info">
            <div class="value">
              {{ formateMoney(baseData.expendTotal, false).num | formateNumber }}
              <font style="font-size: 12px">{{ formateMoney(baseData.expendTotal, false).unit }}</font>
            </div>
            <div class="title">总支出</div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-container">
      <div class="box3_1 block-box">
        <div class="block-title">总收入分析</div>
        <div class="switch-box">
          <span v-for="item in switchOptions1" :key="item.id" class="radio-btn" :class="switch1 == item.id ? 'selected' : ''" @click="handleChangeSwitch1(item.id)">
            {{ item.text }}
          </span>
        </div>
        <div v-if="switch1 === '2'" class="switch-box1" style="margin-left: 15px">
          <template v-for="item in switchOptions7">
            <span v-if="!(item.id == 3 && switch5 == 2)" :key="item.id" class="radio-btn1" :class="switch3_1 == item.id ? 'selected' : ''" @click="handleChangeSwitch3_1(item.id)">
              {{ item.text }}
            </span>
          </template>
        </div>
        <div
          v-if="switch1 == '1'"
          class="block-body"
          @mouseenter.stop="stopHandleMouseEnter('task1_1')"
          @mouseleave.stop="startHandleMoseLeave('task1_1', 'switchOptions1', 'switch1', 'handleChangeSwitch1')"
        >
          <div class="rank-header">
            <div class="header-item" style="width: 80px">名次</div>
            <div class="header-item" style="flex: 1">地区机构</div>
            <div class="header-item" style="flex: 2">总收入金额</div>
            <div class="header-item" style="flex: 2">总收入占比</div>
          </div>
          <vue-seamless-scroll
            :data="data1_1_1"
            class="rank-list"
            :class-option="{
              step: 0.5,
              limitMoveNum: 7
            }"
            :style="{ height: '270px', maxHeight: '270px' }"
          >
            <ul>
              <li v-for="item in data1_1_1" :key="item.areaOrgName" class="rank-item" :class="item.rank == 1 ? 'level1' : item.rank <= 3 ? 'level2' : 'level3'">
                <span class="rank-order">{{ item.rank }}</span>
                <span class="area-name" :title="item.areaOrgName">{{ item.areaOrgName }}</span>
                <span class="value">{{ formateMoneyStr(item.incomeTotal) | formateNumber }}</span>
                <el-progress class="rank-progress" :text-inside="true" :stroke-width="18" :percentage="item.rate" />
              </li>
            </ul>
          </vue-seamless-scroll>
        </div>

        <div
          v-else
          id="financial_chart1_1_2"
          class="block-body"
          @mouseenter.stop="stopHandleMouseEnter('task1_1')"
          @mouseleave.stop="startHandleMoseLeave('task1_1', 'switchOptions1', 'switch1', 'handleChangeSwitch1')"
        ></div>
      </div>
      <div class="box3_3 block-box">
        <div class="block-title">上一年度经济收入超亿元村(社区)情况</div>
        <div id="financial_chart3_3"></div>
      </div>

      <div class="box3_2 block-box" @mouseenter.stop="stopHandleMouseEnter('task3_2')" @mouseleave.stop="targetMouseLeave('task3_2')">
        <div class="block-title">在管合同</div>
        <div class="switch-box1" style="margin-left: 15px">
          <template v-for="item in switchOptions6">
            <span v-if="!(item.id == 3 && switch5 == 2)" :key="item.id" class="radio-btn1" :class="switch6 == item.id ? 'selected' : ''" @click="handleChangeSwitch6(item.id)">
              {{ item.text }}
            </span>
          </template>
        </div>

        <div
          v-if="switch5 == 1 && switch6 == 1"
          class="box1 aa"
          @mouseenter.stop="stopHandleMouseEnter('task3_2')"
          @mouseleave.stop="startHandleMoseLeave('task3_2', 'switchOptions6', 'switch6', 'handleChangeSwitch6')"
        >
          <template v-if="data3_2">
            <div class="left">
              <div class="left1 item">
                <img class="img" src="@/assets/image/financial/icon7.png" alt="" />
                <div class="detail-info">
                  <div class="title fwb">
                    应收款总额
                    <span class="subunit">({{ formateMoney(data3_2.receivablesMoney, false).unit }})</span>
                  </div>
                  <div class="value">
                    {{ formateMoney(data3_2.receivablesMoney, false).num | formateNumber }}
                    <!--                    {{ (data3_2.receivablesMoney/data3_2.totalMoney)*formateMoney(data3_2.totalMoney, false).num | formateNumber }}-->
                  </div>
                </div>
              </div>
              <div class="left2 item">
                <img class="img" src="@/assets/image/financial/icon8.png" alt="" />
                <div class="detail-info">
                  <div class="title fwb">
                    应收未收款总额
                    <span class="subunit">({{ formateMoney(data3_2.unReceivedMoney, false).unit }})</span>
                  </div>
                  <div class="value">
                    {{ formateMoney(data3_2.unReceivedMoney, false).num | formateNumber }}
                    <!--                    {{ (data3_2.unReceivedMoney/data3_2.totalMoney)*formateMoney(data3_2.totalMoney, false).num | formateNumber }}-->
                  </div>
                </div>
              </div>
            </div>

            <div class="center">
              <div class="value">
                {{ formateMoney(data3_2.totalMoney, false).num | formateNumber }}
              </div>
              <div class="title fwb">
                收款合同总额
                <div class="subunit">({{ formateMoney(data3_2.totalMoney, false).unit }})</div>
              </div>
            </div>

            <div class="right">
              <div class="right1 item">
                <div class="detail-info">
                  <div class="title fwb">
                    已收款总额
                    <span class="subunit">({{ formateMoney(data3_2.receivedMoney, false).unit }})</span>
                  </div>
                  <div class="value">
                    {{ formateMoney(data3_2.receivedMoney, false).num | formateNumber }}
                    <!--                    {{ (data3_2.receivedMoney/data3_2.totalMoney)*formateMoney(data3_2.totalMoney, false).num | formateNumber }}-->
                  </div>
                </div>
                <img class="img" src="@/assets/image/financial/icon9.png" alt="" />
              </div>
              <div class="right2 item">
                <div class="detail-info">
                  <div class="title fwb">履约率</div>
                  <div class="value">{{ numberFormate(data3_2.performanceRate, true, 2, false) }}%</div>
                </div>
                <img class="img" src="@/assets/image/financial/icon10.png" alt="" />
              </div>
            </div>

            <!--            <div class="unit">单位：{{ formateMoney(data3_2.totalMoney, false).unit }}</div>-->
          </template>
        </div>

        <div
          v-else-if="switch5 == 1 && switch6 == 2"
          id="financial_chart3_2"
          @mouseenter.stop="handleMouseEnter('task3_2')"
          @mouseleave.stop="startHandleMoseLeave('task3_2', 'switchOptions6', 'switch6', 'handleChangeSwitch6')"
        ></div>

        <div
          v-else-if="switch5 == 1 && switch6 == 3"
          id="financial_chart3_2"
          @mouseenter.stop="stopHandleMouseEnter('task3_2')"
          @mouseleave.stop="startHandleMoseLeave('task3_2', 'switchOptions6', 'switch6', 'handleChangeSwitch6')"
        ></div>
      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      custom-class="custom-dialog"
      :show-close="false"
      :append-to-body="false"
      width="70%"
      :destroy-on-close="true"
      @close="() => (page = 1)"
    >
      <div slot="title">
        <div class="title">{{ switch5 == '1' ? '收款合同统计' : '付款合同统计' }}</div>
        <img class="close-btn" src="./assets/image/trading/close.png" alt="" @click.stop="dialogVisible = false" />
      </div>
      <div v-if="dialogVisible">
        <el-form inline>
          <el-form-item label="地区机构">
            <el-select
              ref="_areaSelectRef"
              v-model="dialogQueryParams.areaName"
              popper-class="area_tree"
              style="width: 200px"
              :title="dialogQueryParams.areaName"
              :popper-append-to-body="false"
            >
              <el-option :value="treeNodeAreaName" style="height: auto; padding: 0">
                <area-tree :is-lazy-load="true" :tree-data="treeData" :expanded-nodes="expandedNodes" @loadChildNode="loadChildNode" @selectedNodeChange="handleNodeChange" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="统计时间">
            <el-date-picker
              v-model="dialogQueryParams.date"
              type="daterange"
              range-separator="至"
              start-placeholder=""
              end-placeholder=""
              value-format="yyyy-MM-dd"
              :append-to-body="false"
              style="width: 300px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="统计级别">
            <el-select v-model="dialogQueryParams.level" :popper-append-to-body="false" style="width: 100px">
              <el-option v-for="item in dateOptions" :key="item.id" :label="item.text" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button class="search-btn" @click="searchPageList">查询</el-button>
            <el-button class="search-btn" @click="handleExport">导出报表</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="dialogTable.rows"
          stripe
          :height="430"
          element-loading-text="数据加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0)"
        >
          <el-table-column prop="serialNum" label="名次" align="center" header-align="center" width="100"></el-table-column>
        </el-table>

        <el-pagination
          :current-page="page"
          :page-size="10"
          layout="total, prev, pager, next, jumper"
          :total="dialogTable.total"
          style="float: right"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import vueSeamlessScroll from 'vue-seamless-scroll'
import mapNav from './views/mapNav.vue'
import AreaTree from '@/views/assetComponent/AreaTree.vue'
import { changeMoney, getLastFormatDate, unifyDataUnit } from '@/utils/common'
import NumberFlashCopy from '@/views/NumberFlashCopy'
import {
  getYearList,
  getBaseData,
  getIncomeTotal,
  getIncomeTrend,
  getIncomeExpenseStructuralAnalysis,
  getProfitTotal,
  getAssetSituationTotal,
  // getAssetSituationCompose,
  getManagingFundCompose,
  getManagingFundTrend,
  getMillionVillage,
  contractInfo
} from '@/api/financial.js'
import { userTreeData } from '@/api/asset'

export default {
  name: 'Financial',
  components: {
    mapNav,
    vueSeamlessScroll,
    NumberFlashCopy,
    AreaTree
  },
  filters: {
    formateNumber(val) {
      return typeof val === 'number' && !isNaN(val) ? val.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : val || 0
    },
    formateNumberW(val) {
      let res = 0.0
      if (typeof val === 'number') {
        // 保留两位小数
        res = parseFloat(val / 10000).toFixed(2)
        // 转换为千分位格式
        res = res.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
      return res
    }
  },
  data() {
    this.intervalType = ['1_1', '1_2', '1_3', '3_1', '3_2']
    return {
      records: require('./views/mapData/foshan_area_data.json'),
      areaCode: 'D4406',
      areaLevel: '1',
      currentArea: '佛山市',
      currentDate: '',
      currentAccount: 'fs',
      queryParams: {
        year: '',
        quarter: '',
        month: ''
      },

      yearOptions: [],
      quarterOptions: [
        { id: '1', text: '第一季度' },
        { id: '2', text: '第二季度' },
        { id: '3', text: '第三季度' },
        { id: '4', text: '第四季度' }
      ],
      monthOptions: [
        { id: '1', text: '1月' },
        { id: '2', text: '2月' },
        { id: '3', text: '3月' },
        { id: '4', text: '4月' },
        { id: '5', text: '5月' },
        { id: '6', text: '6月' },
        { id: '7', text: '7月' },
        { id: '8', text: '8月' },
        { id: '9', text: '9月' },
        { id: '10', text: '10月' },
        { id: '11', text: '11月' },
        { id: '12', text: '12月' }
      ],
      dateOptions: [
        { id: '1', text: '年度' },
        { id: '2', text: '季度' },
        { id: '3', text: '月度' }
      ],

      dateSwitch: '1',

      switchOptions1: [
        { id: '1', text: '收入总额' },
        { id: '2', text: '收入趋势' }
      ],
      switch1: '1',
      switch1_1: '1',
      switch3_1: '1',
      data1_1_1: [],
      data1_1_2: [],
      financial_chart1_1_2: null,

      switchOptions2: [
        { id: '1', text: '收入' },
        { id: '2', text: '支出' },
        { id: '3', text: '收益总额' }
      ],
      switch2: '1',
      data1_2: [],
      financial_chart1_2: null,
      total1_2: 0,
      switchOptions3: [
        { id: '1', text: '资产类总值' },
        { id: '2', text: '负债类总值' },
        { id: '3', text: '所有者权益类总值' }
      ],
      totalLabel3: ['', '资产总额', '负债总额', '所有者权益总额'],
      switch3: '1',
      data1_3: [],
      chart1_3: null,
      total1_3: 0,

      baseData: {},

      switchOptions4: [
        { id: '1', text: '资金构成' },
        { id: '2', text: '资金趋势' }
      ],
      switch4: '1',
      data3_1: [],
      chart3_1: null,

      switchOptions5: [
        { id: '1', text: '收款合同' },
        { id: '2', text: '付款合同' }
      ],
      switchOptions6: [
        { id: '1', text: '基本情况' },
        { id: '2', text: '交易类别分布' },
        { id: '3', text: '年限分析' }
      ],
      switchOptions7: [
        { id: '1', text: '按年度' },
        { id: '2', text: '按月度' }
      ],
      switch5: '1',
      data3_2: null,
      financial_chart3_2: null,
      switch6: '1',
      dialogVisible: false,
      page: 1,
      dialogTable: {
        rows: [],
        total: 0
      },
      loading: false,
      dialogQueryParams: {
        areaName: ''
      },
      treeNodeAreaName: '',
      treeData: [],
      data3_3: [],
      financial_chart3_3: null,
      params: {
        switch1: '1',
        switch2: '1',
        switchOptions3: '1',
        switch4: '1',
        switch5: '1'
      },
      timeSetInterval1_1: null,
      timeSetInterval1_2: null,
      timeSetInterval1_3: null,
      timeSetInterval3_1: null,
      timeSetInterval3_2: null,
      playInterval: 8000,
      timeIndex: 0,
      task1_1: null,
      task1_2: null,
      task1_3: null,
      task3_1: null,
      task3_2: null,
      currentOverTask: '',
      dataSet: {
        data3_1_1: [],
        data3_1_2_1: [],
        data3_1_2_2: [],
        data1_1_1: [],
        data1_1_2: [],
        data1_1_3: [],
        data3_3_1: [],
        data3_3_2: [],
        data3_2_1: [],
        data3_2_2_1: [],
        data3_2_2_2: []
      },
      taskKeyAll: ['task3_1', 'task1_3', 'task1_2', 'task1_1', 'financial_chart3_2'],
      currentMouseOverTarget: null // add by ymk 当前鼠标在哪个物体上，就要停止哪个物体上的滚动
    }
  },
  computed: {
    expandedNodes() {
      const ids = []
      if (this.treeData.length > 0) {
        this.treeData.forEach((levelOneNode) => {
          ids.push(levelOneNode.id)
        })
      }
      return ids
    }
  },
  watch: {
    currentAccount: {
      handler: function (val) {
        localStorage.setItem('currentAccount', val)
      },
      immediate: true
    }
  },
  mounted() {
    this.initBaseData()
  },
  beforeDestroy() {
    this.clearIntervalAll()
  },
  methods: {
    targetMouseEnter(type) {
      this.currentMouseOverTarget = type
    },
    targetMouseLeave(type) {
      this.currentMouseOverTarget = null
      this.startTask('task3_2', 'switchOptions6', 'switch6', 'handleChangeSwitch6')
    },
    getTotalMoney(data) {
      if (data) {
        return changeMoney(data)
      } else {
        return { num: 0, unit: '' }
      }
    },
    getNextOption(options, current, target) {
      if (target == this.currentMouseOverTarget) return null // 如果当前鼠标在上面，就停止滚动
      const currentIndex = options.findIndex((i) => i.id == current)
      let nextIndex = 0
      if (currentIndex >= options.length - 1) {
        nextIndex = 0
      } else {
        nextIndex = currentIndex + 1
      }
      return options[nextIndex]
    },
    formateMoney(val, pureText = true) {
      const { num, unit } = changeMoney(val)
      const numStr = num
      if (pureText) return num + unit
      return { num: numStr, unit }
    },
    formateMoneyToFixed(val, pureText = true) {
      const { num, unit } = changeMoney(val)
      if (pureText) return num?.toFixed(2) + unit
      return { num, unit }
    },
    formateMoneyStr(val, pureText = true) {
      const { num, unit } = changeMoney(val)
      const numStr = num && num.toLocaleString ? num.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : num // add by ymk 添加千分位格式化
      if (pureText) return numStr + unit
      return { num: numStr, unit }
    },
    formateNumberW(val) {
      let res = 0.0
      if (typeof val === 'number') {
        // 保留两位小数
        res = val.toFixed(2)
        // 转换为千分位格式
        res = res.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      }
      return res
    },
    playSlidesCategoryType(type, enumData, fn) {
      let index = enumData.findIndex((cur) => cur.id == this[type])
      index++
      if (index >= enumData.length) {
        index = 0
      }
      this[fn](enumData[index].id)
    },
    stopHandleMouseEnter(type) {
      this.currentOverTask = type
      this.handleMouseEnter(type)
    },
    startHandleMoseLeave(type, options, switchName, fn) {
      this.currentOverTask = ''
      this.startTask(type, options, switchName, fn)
    },
    handleMouseEnter(type) {
      console.log('clearTimeout', type)
      this[type] && clearTimeout(this[type])
    },
    initBaseData() {
      const startYear = 2024
      const currentYear = new Date().getFullYear()
      this.$set(this.queryParams, 'year', currentYear)
      for (let i = currentYear; i >= startYear; i--) {
        this.yearOptions.push({
          id: i,
          text: i + '年'
        })
      }
      // getYearList().then((res) => {})

      this.currentDate = getLastFormatDate(new Date().getFullYear() + '-' + (new Date().getMonth() + 1 + '').padStart(2, '0') + '-' + (new Date().getDate() + '').padStart(2, '0'))

      this.getPageData()
      // 初始化时也需要延迟启动轮播
      this.$nextTick(() => {
        setTimeout(() => {
          this.startAllTasks()
        }, 1500) // 初始化时给更多时间
      })
    },
    handleDateChange() {
      this.reloadData()
    },
    handleChangeDateSwitch(item) {
      if (item.id == this.dateSwitch) return

      this.dateSwitch = item.id
      if (item.id == '1' && !this.queryParams.year) {
        this.$set(this.queryParams, 'year', new Date().getFullYear() + '')
      } else if (item.id == '2' && !this.queryParams.quarter) {
        this.$set(this.queryParams, 'quarter', Math.ceil((new Date().getMonth() + 1) / 3) + '')
      } else if (item.id == '3' && !this.queryParams.month) {
        this.$set(this.queryParams, 'quarter', '')
        this.$set(this.queryParams, 'month', new Date().getMonth() + 1 + '')
      }
      if (item.id !== '3') {
        this.$set(this.queryParams, 'month', '')
      }
      if (item.id !== '2') {
        this.$set(this.queryParams, 'quarter', '')
      }
      this.reloadData()
    },
    gotopath(type) {
      const current = this.records.records.find((item) => {
        return item.areaCode == `D${type}`
      })
      console.log(current)
      this.currentArea = current.areaName
      this.areaCode = current.areaCode
      if (type != undefined) {
        this.areaCode = type
        this.areaCode = 'D' + this.areaCode
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'nh'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'ss'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'cc'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'sd'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'gm'
      }
      if (this.areaCode == 'D440605') {
        this.currentAccount = 'fs'
      }

      if (type.length == 6) {
        this.areaLevel = '2'
      } else if (type.length == 9) {
        this.areaLevel = '3'
      } else {
        this.areaLevel = '1'
      }

      this.reloadData()
    },
    reloadData() {
      this.clearIntervalAll()
      this.reSetData()
      this.getPageData()
      // 延迟启动轮播，确保所有数据加载完成后再统一启动
      this.$nextTick(() => {
        setTimeout(() => {
          this.startAllTasks()
        }, 1000) // 给异步数据加载留出时间
      })
    },
    clearIntervalAll() {
      this.taskKeyAll.forEach((cur) => {
        this.handleMouseEnter(cur)
      })
    },
    reSetData() {
      this.handleChangeSwitch4('1')
      this.handleChangeSwitch1_1('1')
      this.handleChangeSwitch3('1')
      this.handleChangeSwitch2('1')
      this.handleChangeSwitch1('1')
      this.handleChangeSwitch3_1('1')
      this.handleChangeSwitch6('1')
    },
    getPageData() {
      this.getBaseData()
      this.getData1_2()
      this.getData1_3()
      this.getData3_1()
      this.getData3_2()
      this.getData3_3()
      this.getData1_1_1()
    },
    handleChangeSwitch1(id) {
      this.handleMouseEnter('task1_1')
      this.switch1 = id
      this.switch3_1 = '1'
      if (this.switch1 == '1') {
        this.financial_chart1_1_2 && this.financial_chart1_1_2.dispose()
        this.data1_1_1 = this.dataSet.data3_2_1
      } else {
        this.data1_1_2 = this.dataSet.data3_2_2_1
        this.$nextTick(() => {
          this.initChart1_1_2()
        })
      }
      // 移除这里的 startTask 调用，避免重复启动轮播
    },
    getData1_1_1() {
      this.handleMouseEnter('task1_1')
      getIncomeTotal({
        ...this.queryParams,
        areaCode: this.areaCode,
        orgType: '02,03'
      }).then((res) => {
        this.dataSet.data3_2_1 = res.data
        this.data1_1_1 = res.data
        // 移除这里的 startTask 调用，统一在 startAllTasks 中处理
      })
      this.getData1_1_2()
    },
    getData1_1_2() {
      const { year } = this.queryParams

      getIncomeTrend({
        areaCode: this.areaCode,
        orgType: '02,03',
        year
      }).then((res) => {
        const { data, unit} = unifyDataUnit(res.data, 'amount', true, 2);
        this.dataSet.data3_2_2_1 = data.map((item) => ({
          ...item,
          amount: item.convertedValue,
          unit: unit,
          orgAmount:item.originalValue
        }))
      })

      const month = this.getMonth(year)
      getIncomeTrend({
        areaCode: this.areaCode,
        orgType: '02,03',
        year,
        month
      }).then((res) => {
        const amountSet = res.data.map((cur) => cur.amount)
        const max = Math.max(...amountSet)
        const maxLen = max.toString().length
        const enumUnit = {
          元: 1,
          万元: 10000,
          亿元: 100000000,
          万亿: 1000000000000
        }
        const maxUint = this.formateMoney(max, false).unit
        this.dataSet.data3_2_2_2 = res.data.map((cur) => {
          const { num } = this.formateMoney(cur.amount, false)
          const len = num.toString().length
          if (num > 0 && len < maxLen) {
            cur.amount = cur.amount / enumUnit[maxUint]
          } else {
            cur.amount = num
          }
          cur.unit = maxUint
          cur.orgAmount = cur.amount
          return cur
        })
      })
    },
    getMonth(paramYear) {
      const date = new Date()
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      console.log('paramYear:', paramYear)
      console.log('year:', year)
      if (paramYear < year) {
        return 12
      }
      return month
    },
    padNumber(num, length) {
      var str = '' + num
      while (str.length < length) {
        str = str + '0'
      }
      return str * 1
    },
    initChart1_1_2() {
      const chartDom = document.getElementById('financial_chart1_1_2')
      if (this.financial_chart1_1_2) {
        this.financial_chart1_1_2.dispose()
      }
      this.financial_chart1_1_2 = echarts.init(chartDom)
      const data = this.data1_1_2.map((i) => i.amount)
      const max = Math.max(...data)
      const numMax = Math.ceil(max, false)
      const numLen = numMax.toString().length
      const firstNum = numMax.toString()[0] * 1 + 1
      let unit = '元'
      this.data1_1_2.forEach((item) => {
        if (item.amount === max) {
          unit = item.unit
        }
      })
      const option = {
        color: ['#1ce2dc'],
        legend: {
          itemGap: 50,
          itemWidth: 12,
          itemHeight: 12,
          selectedMode: false,
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (e) => {
            const valMoney = e[0].value?.toFixed(2)
            return e[0].marker + e[0].name + '：' + parseFloat(valMoney).toLocaleString() + unit
          }
        },
        xAxis: {
          type: 'category',
          data: this.data1_1_2.map((i) => i.coordinate),
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            textStyle: {
              color: '#3ea7e1',
              fontSize: 12
            },
            rotate: this.switch3_1 === '2' ? 60 : 0,
            formatter: (value) => {
              if (this.switch3_1 === '2') {
                let newVal = value.replace('年', '-')
                newVal = newVal.replace('月', '')
                return newVal
              }
              return value
            }
          }
        },
        yAxis: [
          {
            name: '单位：' + unit,
            max: this.padNumber(firstNum, numLen),
            interval: this.padNumber(firstNum, numLen) / 5,
            nameTextStyle: {
              color: '#3ea7e1',
              fontSize: 12
            },
            type: 'value',
            splitLine: {
              lineStyle: {
                color: '#214672'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#3ea7e1',
                fontSize: 14
              }
            }
          }
        ],
        grid: {
          left: '20',
          right: '15',
          bottom: '10',
          top: '30',
          containLabel: true
        },
        series: [
          {
            data,
            type: 'line',
            symbol: 'circle',
            symbolSize: 12,
            step: false,
            lineStyle: {
              color: '#1ce2dc'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1,
                    color: 'transparent' // 0% 处的颜色
                  },
                  {
                    offset: 0,
                    color: '#0B5F8D' // 100% 处的颜色
                  }
                ]
              }
            }
          }
        ]
      }
      this.financial_chart1_1_2.setOption(option)
    },

    handleChangeSwitch2(id, isStop) {
      this.switch2 = id
      this.$nextTick(() => {
        if (id == '1') {
          this.data1_2 = this.dataSet.data1_1_1
          this.initChart1_2()
        }
        if (id == '2') {
          this.data1_2 = this.dataSet.data1_1_2
          this.initChart1_2()
        }
        if (id == '3') {
          this.data1_2 = this.dataSet.data1_1_3
          this.initChart1_3_3()
        }
        // 移除这里的 startTask 调用，避免重复启动轮播
        // this.handleMouseEnter('task1_2')
      })
    },
    getData1_2() {
      this.handleMouseEnter('task1_2')
      // 收入
      getIncomeExpenseStructuralAnalysis({
        ...this.queryParams,
        areaCode: this.areaCode,
        parentCode: 0,
        type: '1',
        orgType: '02,03'
      }).then((res) => {
        this.dataSet.data1_1_1 = res.data
        this.data1_2 = res.data
        this.initChart1_2()
        // 移除这里的 startTask 调用，统一在 startAllTasks 中处理
      })
      // 支出
      getIncomeExpenseStructuralAnalysis({
        ...this.queryParams,
        areaCode: this.areaCode,
        parentCode: 0,
        type: '2',
        orgType: '02,03'
      }).then((res) => {
        this.dataSet.data1_1_2 = res.data
      })
      // 收益总额
      getProfitTotal({
        ...this.queryParams,
        areaCode: this.areaCode,
        orgType: '02,03'
      }).then((res) => {
        this.dataSet.data1_1_3 = res.data
      })
    },
    initChart1_3_3() {
      const chartDom = document.getElementById('financial_chart1_2')
      if (this.financial_chart1_2) {
        this.financial_chart1_2.dispose()
      }

      this.financial_chart1_2 = echarts.init(chartDom)
      const tempData = this.data1_2.map((i) => i.profitAmount)
      // this.total1_2 = showData1.reduce((total, num) => total + num) // add by ymk 计算总额
      let total = 0 // add ymk 计算总额
      this.data1_2.forEach((item) => {
        total += item.profitAmount
      })
      this.total1_2 = total
      // const showData2 = this.data1_2.map((i) => i.undistributedProfitAmount)
      const max = Math.max(...tempData)
      const maxLen = max.toString().length
      const enumUnit = {
        元: 1,
        万元: 10000,
        亿元: 100000000,
        万亿: 1000000000000
      }
      const maxUint = this.formateMoney(max, false).unit
      const showData1 = tempData.map((cur) => {
        const { num } = this.formateMoney(cur, false)
        const len = cur.toString().length
        if ((num > 0 || num < 0) && len <= maxLen) {
          cur = cur / enumUnit[maxUint]
        } else {
          cur = num
        }
        return cur
      })
      const option = {
        grid: {
          left: '20',
          right: '10',
          bottom: '30',
          top: '40',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (val) => {
            return (
              val[0].name + '<br />' + val[0].marker + '收益总额：' + this.formateNumberW(parseFloat(val[0].value)) + maxUint
              // '<br />' +
              // val[1].marker +
              // '未分配收益总额：' +
              // this.numberFormate(val[1].value, true, 2, false) +
              // unit
            )
          }
        },
        legend: {
          top: 10,
          itemGap: 50,
          itemWidth: 12,
          itemHeight: 12,
          show: false,
          selectedMode: false,
          data: ['收益总额'],
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        dataZoom: [
          {
            type: 'inside',
            zoomLock: true,
            startValue: 0,
            endValue: 10,
            moveOnMouseWheel: true
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: this.data1_2.map((i) => i.coordinate),
            axisLabel: {
              rotate: this.data1_2.length > 7 ? 30 : 0,
              textStyle: {
                color: '#3ea7e1',
                fontSize: 14
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#014e6a'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位: ' + maxUint,
            // max: Math.ceil(num),
            nameTextStyle: {
              color: '#3ea7e1',
              fontSize: 12,
              align: 'center'
            },
            axisTick: {
              show: true
            },
            axisLabel: {
              color: '#3ea7e1'
            },

            splitLine: {
              lineStyle: {
                color: '#214672'
              }
            }
          }
        ],
        series: [
          {
            name: '收益总额',
            type: 'bar',
            barWidth: this.data1_2.length <= 6 ? 20 : 10,
            itemStyle: {
              color: {
                colorStops: [
                  {
                    offset: 0.5,
                    color: '#50a8f7'
                  },
                  {
                    offset: 1,
                    color: '#0a3475'
                  }
                ],
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false
              }
            },
            yAxisIndex: '0',
            data: showData1.map((cur) => this.formateMoneyToFixed(cur, false).num)
          }
          // {
          //   name: '未分配收益总额1',
          //   type: 'bar',
          //   barWidth: this.data1_2.length <= 6 ? 20 : 10,
          //   itemStyle: {
          //     color: {
          //       colorStops: [
          //         {
          //           offset: 0.5,
          //           color: '#00e5b2'
          //         },
          //         {
          //           offset: 1,
          //           color: '#0d6c91'
          //         }
          //       ],
          //       x: 0,
          //       y: 0,
          //       x2: 0,
          //       y2: 1,
          //       type: 'linear',
          //       global: false
          //     }
          //   },
          //   yAxisIndex: '0',
          //   data: showData2.map((cur) => changeMoney(cur).num)
          // }
        ]
      }

      this.financial_chart1_2.setOption(option)
    },
    initChart1_2() {
      const chartDom = document.getElementById('financial_chart1_2')
      if (this.financial_chart1_2) {
        this.financial_chart1_2.dispose()
      }
      let total = 0 // add ymk 计算总额
      this.data1_2.forEach((item) => {
        total += item.amount
      })
      // this.total1_2 = total
      const totalObj = changeMoney(total)

      this.financial_chart1_2 = echarts.init(chartDom)
      const color = this.switch2 === '1' ? ['#368aff', '#1fb5fc', '#fed130', '#fc5659'] : ['#368aff', '#1fb5fc', '#fed130', '#fc5659', '#27e9cb', '#9e33f8']
      const option = {
        color,
        grid: {
          left: '0',
          right: '2%',
          containLabel: true
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (e) => {
            const valMoney = this.formateMoney(e.value, false).num
            const valUnit = this.formateMoney(e.value, false).unit
            return e.marker + e.name + '：' + this.formateNumberW(parseFloat(valMoney)) + valUnit + '，占比' + this.numberFormate(e.data.rate, false, 2, false) + '%'
          }
        },
        legend: {
          orient: 'vertical',
          left: '40%',
          right: 0,
          top: 'center',
          bottom: 'center',
          selectedMode: false,
          icon: 'roundRect',
          itemHeight: 15,
          itemWidth: 15,
          itemGap: 12,
          textStyle: {
            align: 'right',
            top: -10,
            color: '#89ccf5',
            fontSize: 14,
            rich: {
              name: {
                color: '#89ccf5',
                fontSize: 14,
                width: 100,
                align: 'left'
              },
              value: {
                color: '#89ccf5',
                fontSize: 14,
                width: 100,
                lineHeight: 20
              },
              rate: {
                color: '#89ccf5',
                fontSize: 14
              }
            }
          },
          formatter: (e) => {
            const current = this.data1_2.find((i) => i.pieName === e)
            const valMoney = this.formateMoney(current?.amount, false).num
            const valUnit = this.formateMoney(current?.amount, false).unit
            return `{name| ${e}} {value| ${this.formateNumberW(parseFloat(valMoney))}${valUnit}} {rate| ${this.numberFormate(current.rate, false, 2, false)}%}`
          }
        },
        labelLine: {
          show: false
        },
        title: {
          text: totalObj.num.toLocaleString('en-US', { maximumFractionDigits: 2 }),
          subtext: this.switch2 === '1' ? '收入总额\n(' + totalObj.unit + ')' : '支出总额\n(' + totalObj.unit + ')',
          top: 'center',
          left: '19%',
          textAlign: 'center',
          textStyle: {
            fontSize: 21,
            color: '#2BCAFF'
          },
          subtextStyle: {
            fontSize: 14,
            align: 'center',
            color: '#fff'
          }
        },
        series: [
          {
            type: 'pie',
            data: this.data1_2.map((i) => {
              return {
                name: i.pieName,
                value: i.amount,
                rate: i.rate
              }
            }),
            radius: ['50%', '70%'],
            center: ['20%', '50%'],
            // radius: [0, '75%'],
            // center: ['20%', '50%'],
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            itemStyle: {
              normal: {}
            }
          }
        ]
      }
      this.financial_chart1_2.setOption(option)
    },

    handleChangeSwitch3(id) {
      this.handleMouseEnter('task1_3')
      this.switch3 = id
      let key = 'assetTotal'
      let title = '资产类总值'
      if (this.switch3 == 1) {
        key = 'assetTotal'
        title = '资产类总值'
      } else if (this.switch3 == 2) {
        key = 'liabilitiesTotal'
        title = '负债类总值'
      } else {
        key = 'ownerEquityTotal'
        title = '所有者权益类总值'
      }
      this.initChart1_3_1(key, title)
      // 移除这里的 startTask 调用，避免重复启动轮播
    },
    getData1_3() {
      this.handleMouseEnter('task1_3')
      getAssetSituationTotal({
        ...this.queryParams,
        areaCode: this.areaCode,
        orgType: '02,03'
      }).then((res) => {
        this.data1_3 = res.data
        this.initChart1_3_1('assetTotal', '资产类总值')
        // 移除这里的 startTask 调用，统一在 startAllTasks 中处理
      })
    },
    initChart1_3_1(dataKey, title) {
      const chartDom = document.getElementById('financial_chart1_3')
      if (this.chart1_3) {
        this.chart1_3.dispose()
      }
      this.chart1_3 = echarts.init(chartDom)
      const tempData = this.data1_3.map((i) => i[dataKey])
      if (!tempData || tempData.length === 0) return
      this.total1_3 = tempData.reduce((total, num) => total + num, 0) // add by ymk
      const max = Math.max(...tempData)
      const maxLen = max.toString().length
      const enumUnit = {
        元: 1,
        万元: 10000,
        亿元: 100000000,
        万亿: 1000000000000
      }
      const maxUint = this.formateMoney(max, false).unit
      const showData = tempData.map((cur) => {
        const { num } = this.formateMoney(cur, false)
        const len = cur.toString().length
        if (num > 0 && len <= maxLen) {
          cur = cur / enumUnit[maxUint]
        } else {
          cur = num
        }
        return cur
      })
      // const { unit, num } = changeMoney(max)
      const option = {
        grid: {
          left: '10',
          right: '10',
          bottom: '10',
          top: '30',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (val) => {
            return val[0].name + '<br />' + val[0].marker + val[0].seriesName + '：' + this.formateNumberW(parseFloat(val[0].value)) + maxUint
          }
        },
        legend: {
          itemGap: 50,
          itemWidth: 12,
          itemHeight: 12,
          show: false,
          selectedMode: false,
          data: [title],
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        dataZoom: [
          {
            type: 'inside',
            zoomLock: true,
            startValue: 0,
            endValue: 10,
            moveOnMouseWheel: true
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: this.data1_3.map((i) => i.areaOrgName),
            axisLabel: {
              rotate: this.data1_3.length > 7 ? 30 : 0,
              textStyle: {
                color: '#3ea7e1',
                fontSize: 14
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#014e6a'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位: ' + maxUint,
            // max: Math.ceil(num),
            nameTextStyle: {
              color: '#3ea7e1',
              fontSize: 12,
              align: 'center'
            },
            axisTick: {
              show: true
            },
            axisLabel: {
              color: '#3ea7e1'
            },
            splitLine: {
              lineStyle: {
                color: '#214672'
              }
            }
          }
        ],
        series: [
          {
            name: title,
            type: 'bar',
            yAxisIndex: '0',
            barWidth: this.data1_3.length <= 6 ? 20 : 10,
            itemStyle: {
              color: {
                colorStops: [
                  {
                    offset: 0.5,
                    color: '#00e5b2'
                  },
                  {
                    offset: 1,
                    color: '#0d6c91'
                  }
                ],
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false
              }
            },
            data: showData
          }
        ]
      }

      this.chart1_3.setOption(option)
    },
    getBaseData() {
      getBaseData({
        ...this.queryParams,
        areaCode: this.areaCode,
        orgType: '02,03'
      }).then((res) => {
        this.baseData = res.data
      })
    },

    handleChangeSwitch4(id) {
      this.switch4 = id
      this.switch1_1 = '1'
      this.handleMouseEnter('task3_1')
      if (id === '1') {
        this.data3_1 = this.dataSet.data3_1_1
        this.initChart3_1_1()
      }
      if (id === '2') {
        this.data3_1 = this.dataSet.data3_1_2_1
        this.initChart3_1_2()
      }
      // 移除这里的 startTask 调用，避免重复启动轮播
    },
    handleChangeSwitch1_1(id) {
      this.handleMouseEnter('task3_1')
      this.switch1_1 = id
      if (id === '1') {
        this.data3_1 = this.dataSet.data3_1_2_1
      } else {
        this.data3_1 = this.dataSet.data3_1_2_2
      }
      this.$nextTick(() => {
        this.initChart3_1_2()
      })
    },
    getData3_1(isSwitch1_1) {
      this.handleMouseEnter('task3_1')
      // 资金构成
      getManagingFundCompose({
        ...this.queryParams,
        areaCode: this.areaCode,
        orgType: '02,03'
      }).then((res) => {
        this.dataSet.data3_1_1 = res.data
        this.data3_1 = res.data
        this.initChart3_1_1()
        // 移除这里的 startTask 调用，统一在 startAllTasks 中处理
      })
      // 资金趋势
      const { year } = this.queryParams
      // 按年度
      getManagingFundTrend({
        areaCode: this.areaCode,
        orgType: '02,03',
        year
      }).then((res) => {
        const amountSet = res.data.map((cur) => cur.amount)
        const max = Math.max(...amountSet)
        const maxLen = max.toString().length
        const enumUnit = {
          元: 1,
          万元: 10000,
          亿元: 100000000,
          万亿: 1000000000000
        }
        const maxUint = this.formateMoney(max, false).unit
        this.dataSet.data3_1_2_1 = (res.data || []).map((cur) => {
          const { num } = this.formateMoney(cur.amount, false)
          const len = num.toString().length
          if (num > 0 && len < maxLen) {
            cur.amount = cur.amount / enumUnit[maxUint]
          } else {
            cur.amount = num
          }
          cur.unit = maxUint
          cur.orgAmount = cur.amount
          return cur
        })
      })
      // 按月份
      const month = this.getMonth(year)
      getManagingFundTrend({
        areaCode: this.areaCode,
        orgType: '02,03',
        year,
        month
      }).then((res) => {
        const amountSet = res.data.map((cur) => cur.amount)
        const max = Math.max(...amountSet)
        const maxLen = max.toString().length
        const enumUnit = {
          元: 1,
          万元: 10000,
          亿元: 100000000,
          万亿: 1000000000000
        }
        const maxUint = this.formateMoney(max, false).unit
        this.dataSet.data3_1_2_2 = (res.data || []).map((cur) => {
          const { num } = this.formateMoney(cur.amount, false)
          const len = num.toString().length
          if (num > 0 && len < maxLen) {
            cur.amount = cur.amount / enumUnit[maxUint]
          } else {
            cur.amount = num
          }
          cur.unit = maxUint
          cur.orgAmount = cur.amount
          return cur
        })
      })
    },
    initChart3_1_1() {
      const chartDom = document.getElementById('financial_chart3_1')
      if (this.chart3_1) {
        this.chart3_1.dispose()
      }
      this.chart3_1 = echarts.init(chartDom)
      let total = 0 // add by ymk 添加总数计算
      const data = this.data3_1.map((i) => {
        total += i.amount
        return {
          name: i.ringName,
          value: i.amount,
          rate: i.rate
        }
      })
      const totalObj = changeMoney(total)
      const option = {
        color: ['#368aff', '#1fb5fc', '#fed130', '#fc5659'],
        tooltip: {
          show: false
        },
        title: {
          text: totalObj.num.toLocaleString('en-US', { maximumFractionDigits: 2 }),
          subtext: '在管资金总额\n(' + totalObj.unit + ')',
          top: 'center',
          left: '34%',
          textAlign: 'center',
          textStyle: {
            fontSize: 21,
            color: '#2BCAFF'
          },
          subtextStyle: {
            fontSize: 14,
            align: 'center',
            color: '#fff'
          }
        },
        legend: {
          orient: 'vertical',
          left: '70%',
          right: 0,
          top: 'center',
          bottom: 'center',
          icon: 'roundRect',
          itemHeight: 15,
          itemWidth: 15,
          itemGap: 12,
          textStyle: {
            align: 'right',
            top: -10,
            color: '#89ccf5',
            fontSize: 14
          }
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['35%', '55%'],
            data: data,
            labelLine: {
              length: 20,
              length2: 30
            },
            label: {
              color: 'inherit',
              fontSize: 16,
              formatter: (e) => {
                return `${this.formateMoneyStr(e.value)}\n${e.data.rate}%`
              }
            }
          },
          {
            type: 'custom',
            coordinateSystem: 'none',
            silent: true,
            data: [0],
            renderItem(params, api) {
              // 环形图半径
              const r = Math.min(api.getWidth(), api.getHeight()) / 2
              // 圆心
              const center = {
                x: api.getWidth() * 0.35,
                y: api.getHeight() / 1.82
              }
              // 大圆半径
              const rBig = r * 0.68
              // 大圆上的扇形
              const bigSector = []
              const sectorSize = 30 // 扇形长度（弧度）
              const sectorInterval = 60 // 扇形与扇形之间的间隔
              const BigStartAngle = 300 // 大扇形起始角度
              for (let i = 0; i < 4; i++) {
                const startAngle = ((i * (sectorInterval + sectorSize) + BigStartAngle) * Math.PI) / 180
                const endAngle = startAngle + (sectorSize * Math.PI) / 180
                bigSector.push({
                  type: 'sector',
                  shape: {
                    cx: center.x,
                    cy: center.y,
                    r: rBig,
                    r0: rBig * 0.98,
                    startAngle,
                    endAngle
                  },
                  style: {
                    fill: '#C8932F',
                    lineWidth: 2
                  }
                })
              }
              return {
                type: 'group',
                children: [
                  {
                    type: 'group',
                    children: [
                      ...bigSector,
                      {
                        // 外圆环
                        type: 'arc',
                        shape: {
                          cx: center.x,
                          cy: center.y,
                          r: rBig
                        },
                        style: {
                          fill: 'transparent',
                          stroke: '#C8932F',
                          lineWidth: 1
                        }
                      }
                    ]
                  }
                ]
              }
            }
          }
        ]
      }
      this.chart3_1.setOption(option)
    },
    initChart3_1_2() {
      const chartDom = document.getElementById('financial_chart3_1')
      if (this.chart3_1) {
        this.chart3_1.dispose()
      }
      this.chart3_1 = echarts.init(chartDom)
      const showData = this.data3_1.map((i) => i.amount)
      const maxNum = Math.max(...showData)
      let unit = '元'
      this.data3_1.forEach((item) => {
        if (item.amount === maxNum) {
          unit = item.unit
        }
      })
      // const numMax = Math.ceil(maxNum)
      // const numLen = numMax.toString().length
      // const firstNum = numMax.toString()[0] * 1 + 1
      // const max = this.formateMoney(this.padNumber(firstNum, numLen), false).num
      const option = {
        grid: {
          left: '10',
          right: '10',
          bottom: '10',
          top: '30',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (val) => {
            return val[0].name + '<br />' + val[0].marker + '在管资金：' + this.formateNumberW(parseFloat(val[0].value)) + unit
          }
        },
        legend: {
          itemGap: 50,
          itemWidth: 12,
          itemHeight: 12,
          selectedMode: false,
          data: ['在管资金'],
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        dataZoom: [
          {
            type: 'inside',
            zoomLock: true,
            startValue: 0,
            endValue: 12,
            moveOnMouseWheel: true
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: this.data3_1.map((i) => i.coordinate),
            axisLabel: {
              textStyle: {
                color: '#3ea7e1',
                fontSize: 12
              },
              rotate: this.switch1_1 === '2' ? 60 : 0,
              formatter: (value) => {
                if (this.switch1_1 === '2') {
                  let newVal = value.replace('年', '-')
                  newVal = newVal.replace('月', '')
                  return newVal
                }
                return value
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#014e6a'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位: ' + unit,
            nameTextStyle: {
              color: '#3ea7e1',
              fontSize: 12,
              align: 'center',
              paddingLeft: 50
            },
            axisTick: {
              show: true
            },
            axisLabel: {
              color: '#3ea7e1'
            },
            splitLine: {
              lineStyle: {
                color: '#214672'
              }
            }
          }
        ],
        series: [
          {
            name: '在管资金',
            type: 'bar',
            yAxisIndex: '0',
            barWidth: this.data3_1.length <= 6 ? 20 : 15,
            itemStyle: {
              color: {
                colorStops: [
                  {
                    offset: 0.5,
                    color: '#00e5b2'
                  },
                  {
                    offset: 1,
                    color: '#0d6c91'
                  }
                ],
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false
              }
            },
            data: this.data3_1.map((i) => this.formateMoney(i.amount, false).num)
          }
        ]
      }
      this.chart3_1.setOption(option)
    },
    handleChangeSwitch5(item) {
      this.switch5 = item.id
      if (this.switch5 == '2' && this.switch6 == '3') {
        this.switch6 = '1'
      }
      this.getData3_2()
    },
    handleChangeSwitch6(id) {
      this.handleMouseEnter('task3_2')
      this.switch6 = id
      this.financial_chart3_2 && this.financial_chart3_2.dispose()
      if (id == '1') {
        this.data3_2 = this.dataSet.data3_3_1
      }
      if (id == '2') {
        this.data3_2 = this.dataSet.data3_3_2
        this.$nextTick(() => {
          this.initChart3_2_1_3()
        })
      }
      if (id == '3') {
        this.data3_2 = this.dataSet.data3_3_3
        this.$nextTick(() => {
          this.initChart3_2_1_3()
        })
      }
      // 移除这里的 startTask 调用，统一在 startAllTasks 中处理
    },
    handleChangeSwitch3_1(id) {
      this.switch3_1 = id
      this.financial_chart1_1_2 && this.financial_chart1_1_2.dispose()
      this.data1_1_2 = id == '1' ? this.dataSet.data3_2_2_1 : this.dataSet.data3_2_2_2
      this.$nextTick(() => {
        this.initChart1_1_2()
      })
    },
    getData3_2() {
      this.handleMouseEnter('task3_2')
      // 收款合同
      contractInfo({
        ...this.queryParams,
        areaCode: this.areaCode
      }).then((res) => {
        this.financial_chart3_2 && this.financial_chart3_2.dispose()
        // 基本情况
        this.dataSet.data3_3_1 = res.data
        this.data3_2 = res.data
        // 交易类别分布
        this.dataSet.data3_3_2 = [
          {
            aa: '出租',
            bb: res.data.tradingTypeRentCount,
            cc: res.data.tradingTypeRentRate
          },
          {
            aa: '出让',
            bb: res.data.tradingTypeSellCount,
            cc: res.data.tradingTypeSellRate
          },
          {
            aa: '发包',
            bb: res.data.tradingTypeOutCount,
            cc: res.data.tradingTypeOutRate
          },
          {
            aa: '入股（合作）',
            bb: res.data.tradingTypeShareCount,
            cc: res.data.tradingTypeShareRate
          },
          {
            aa: '出租+分成',
            bb: res.data.tradingTypeDivideCount,
            cc: res.data.tradingTypeDivideRate
          },
          {
            aa: '其他',
            bb: res.data.tradingTypeOtherCount,
            cc: res.data.tradingTypeOtherRate
          }
        ]
        // 年份分析
        this.dataSet.data3_3_3 = [
          { aa: '5年以下', bb: res.data.fiveYearCount, cc: res.data.fiveYearRate, dd: res.data.fiveYearMoney },
          { aa: '5-10年', bb: res.data.fiveToTenYearCount, cc: res.data.fiveToTenYearRate, dd: res.data.fiveToTenYearMoney },
          { aa: '10-20年', bb: res.data.tenToTwentyYearCount, cc: res.data.tenToTwentyYearRate, dd: res.data.tenToTwentyYearMoney },
          { aa: '20年以上', bb: res.data.twentyYearCount, cc: res.data.twentyYearRate, dd: res.data.twentyYearMoney }
        ]
        // 移除这里的 startTask 调用，统一在 startAllTasks 中处理
      })
    },
    initChart3_2_1_2() {
      const chartDom = document.getElementById('financial_chart3_2')
      if (this.financial_chart3_2) {
        this.financial_chart3_2.dispose()
      }
      this.financial_chart3_2 = echarts.init(chartDom)

      const data = this.data3_2.map((i) => {
        return {
          name: i.aa,
          value: this.numberFormate(i.bb, false, 0, false),
          rate: i.cc
        }
      })

      const option = {
        color: ['#00FFF0', '#FCE84E'],
        tooltip: {
          show: false
        },
        title: {
          text: this.numberFormate(
            data.reduce((sum, val) => sum + parseFloat(val.value), 0),
            true,
            0,
            false
          ),
          subtext: '合同总数\n(份)',
          left: 'center',
          top: '35%',
          textStyle: {
            fontSize: 20,
            fontWeight: 'bold',
            color: '#ffffff'
          },
          subtextStyle: {
            fontSize: 16,
            color: '#ffffff'
          }
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['65%', '90%'],
            center: ['50%', '50%'],
            data: data,
            labelLine: {
              length: 10,
              length2: 30
            },
            startAngle: 45,
            label: {
              color: 'inherit',
              fontSize: 16,
              formatter: (e) => {
                return `${e.name}\n${this.numberFormate(e.value, true, 0, false)}份   占比${e.data.rate}%`
              }
            }
          },
          {
            type: 'custom',
            coordinateSystem: 'none',
            silent: true,
            data: [0],
            renderItem(params, api) {
              // 环形图半径
              const r = Math.min(api.getWidth(), api.getHeight()) / 2
              // 圆心
              const center = {
                x: api.getWidth() / 2,
                y: api.getHeight() / 2
              }
              // 大圆半径
              const rBig = r * 0.8
              // 大圆上的扇形
              const bigSector = []
              const sectorSize = 30 // 扇形长度（弧度）
              const sectorInterval = 60 // 扇形与扇形之间的间隔
              const BigStartAngle = 310 // 大扇形起始角度
              for (let i = 0; i < 4; i++) {
                const startAngle = ((i * (sectorInterval + sectorSize) + BigStartAngle) * Math.PI) / 180
                const endAngle = startAngle + (sectorSize * Math.PI) / 180
                bigSector.push({
                  type: 'sector',
                  shape: {
                    cx: center.x,
                    cy: center.y,
                    r: rBig,
                    r0: rBig * 0.98,
                    startAngle,
                    endAngle
                  },
                  style: {
                    fill: '#C8932F',
                    lineWidth: 2
                  }
                })
              }
              return {
                type: 'group',
                children: [
                  {
                    type: 'group',
                    children: [
                      ...bigSector,
                      {
                        // 外圆环
                        type: 'arc',
                        shape: {
                          cx: center.x,
                          cy: center.y,
                          r: rBig
                        },
                        style: {
                          fill: 'transparent',
                          stroke: '#C8932F',
                          lineWidth: 1
                        }
                      }
                    ]
                  }
                ]
              }
            }
          }
        ]
      }
      this.financial_chart3_2.setOption(option)
    },
    initChart3_2_1_3() {
      const chartDom = document.getElementById('financial_chart3_2')
      if (this.financial_chart3_2) {
        this.financial_chart3_2.dispose()
      }
      this.financial_chart3_2 = echarts.init(chartDom)
      let total = 0 // add by ymk 计算总数
      const data = this.data3_2.map((item) => {
        total += item.bb
        return {
          name: item.aa,
          value: item.bb,
          rate: item.cc,
          amount: item.dd
        }
      })
      const totalObj = changeMoney(total, ['宗', '万宗', '亿宗', '万亿宗'])
      const option = {
        color: ['#368aff', '#1fb5fc', '#27e9cb', '#fed130', '#fd895b', '#fc5659'],
        grid: {
          left: '0',
          right: '2%',
          containLabel: true
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (e) => {
            if (this.switch6 == '2') {
              return e.marker + e.name + '<br />' + this.numberFormate(e.value, true, 0, false) + '宗（' + this.numberFormate(e.data.rate, false, 2, false) + '%）'
            }
            return (
              e.marker +
              e.name +
              '<br />' +
              this.numberFormate(e.value, true, 0, false) +
              '宗（' +
              this.numberFormate(e.data.rate, false, 2, false) +
              '%）' +
              '<br />' +
              '金额：' +
              this.formateMoneyStr(e.data.amount)
            )
          }
        },
        legend: {
          orient: 'vertical',
          left: '40%',
          right: 0,
          top: 'center',
          bottom: 'center',
          selectedMode: false,
          icon: 'roundRect',
          itemHeight: 15,
          itemWidth: 15,
          itemGap: 12,
          textStyle: {
            align: 'right',
            top: -10,
            color: '#89ccf5',
            fontSize: 14,
            rich: {
              name: {
                color: '#89ccf5',
                fontSize: 14,
                width: 100,
                align: 'left'
              },
              value: {
                color: '#89ccf5',
                fontSize: 14,
                width: 100,
                lineHeight: 20
              },
              rate: {
                color: '#89ccf5',
                fontSize: 14
              }
            }
          },
          formatter: (e) => {
            // const unit = this.switch6 == '2' ? '份' : '宗'
            const unit = '宗'
            const current = data.find((i) => i.name === e)
            return `{name| ${e}} {value| ${this.numberFormate(current.value, true, 0, false)}${unit}} {rate| ${this.numberFormate(current.rate, false, 2, false)}%}`
          }
        },
        labelLine: {
          show: false
        },
        title: {
          text: totalObj.num.toLocaleString('en-US', { maximumFractionDigits: 2 }),
          subtext: '合计(' + totalObj.unit + ')',
          top: '35%',
          left: '19%',
          textAlign: 'center',
          textStyle: {
            fontSize: 21,
            color: '#2BCAFF'
          },
          subtextStyle: {
            fontSize: 14,
            align: 'center',
            color: '#fff'
          }
        },
        series: [
          {
            type: 'pie',
            data: data,
            radius: ['60%', '85%'],
            center: ['20%', '50%'],
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            itemStyle: {
              normal: {}
            }
          }
        ]
      }
      this.financial_chart3_2.setOption(option)
    },
    initChart3_2_2_2() {
      const chartDom = document.getElementById('financial_chart3_2')
      if (this.financial_chart3_2) {
        this.financial_chart3_2.dispose()
      }
      this.financial_chart3_2 = echarts.init(chartDom)
      const data = this.data3_2.map((item) => {
        return {
          name: item.aa,
          value: item.bb,
          rate: item.cc
        }
      })
      const option = {
        grid: {
          left: '0',
          right: '2%',
          containLabel: true
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (e) => {
            return e.marker + e.name + '<br />' + this.numberFormate(e.value, true, 2, true) + '万元，占比' + this.numberFormate(e.data.rate, false, 2, false) + '%'
          }
        },
        legend: {
          orient: 'vertical',
          left: '40%',
          right: 0,
          top: 'center',
          bottom: 'center',
          selectedMode: false,
          icon: 'roundRect',
          itemHeight: 15,
          itemWidth: 15,
          itemGap: 12,
          textStyle: {
            align: 'right',
            top: -10,
            color: '#89ccf5',
            fontSize: 14,
            rich: {
              name: {
                color: '#89ccf5',
                fontSize: 14,
                width: 100,
                align: 'left'
              },
              value: {
                color: '#89ccf5',
                fontSize: 14,
                width: 100,
                lineHeight: 20
              },
              rate: {
                color: '#89ccf5',
                fontSize: 14
              }
            }
          },
          formatter: (e) => {
            const current = data.find((i) => i.name === e)
            return `{name| ${e}} {value| ${this.numberFormate(current.value, true, 2, true)}万元} {rate| ${this.numberFormate(current.rate, false, 2, false)}%}`
          }
        },
        labelLine: {
          show: false
        },
        series: [
          {
            type: 'pie',
            data: data,
            radius: [0, '75%'],
            center: ['20%', '50%'],
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            itemStyle: {
              normal: {}
            }
          }
        ]
      }
      this.financial_chart3_2.setOption(option)
    },
    handleReadMore() {
      this.dialogQueryParams.level = this.dateSwitch
      this.getTreeRoot()
      this.dialogVisible = true
    },
    searchPageList() {},
    handleExport() {},
    handleCurrentChange() {
      this.page = 1
      this.searchPageList()
    },
    async getTreeRoot() {
      const { data: treeData } = await userTreeData({ areaCode: this.areaCode })
      this.$set(this.dialogQueryParams, 'areaName', treeData[0].text)
      this.$set(this.dialogQueryParams, 'areaCode', treeData[0].code)
      this.treeData = treeData
      this.searchPageList()
    },
    async loadTree() {
      const params = {}
      if (arguments[0]) {
        params.id = arguments[0]
      }
      return await userTreeData(params.id)
    },
    loadChildNode({ id }, resolve) {
      this.loadTree({ id }).then((res) => {
        resolve(res.data)
      })
    },
    handleNodeChange(data) {
      this.$set(this.dialogQueryParams, 'areaName', data.text)
      this.$set(this.dialogQueryParams, 'areaCode', data.code)
      this.$refs._areaSelectRef.blur()
    },

    getData3_3() {
      getMillionVillage({
        ...this.queryParams,
        areaCode: this.areaCode,
        orgType: '02,03'
      }).then((res) => {
        this.data3_3 = res.data
        this.initChart3_3()
      })
    },
    initChart3_3() {
      const chartDom = document.getElementById('financial_chart3_3')
      if (this.financial_chart3_3) {
        this.financial_chart3_3.dispose()
      }
      this.financial_chart3_3 = echarts.init(chartDom)
      const option = {
        grid: {
          left: '20',
          right: '10',
          bottom: '10',
          top: '50',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(2,68,144,.7)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (val) => {
            return val[0].name + '<br />' + val[0].marker + '亿元村居数：' + val[0].value + '个'
          }
        },
        legend: {
          top: 10,
          itemGap: 50,
          itemWidth: 12,
          itemHeight: 12,
          selectedMode: false,
          data: ['亿元村居数'],
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        dataZoom: [
          {
            type: 'inside',
            zoomLock: true,
            startValue: 0,
            endValue: 8,
            moveOnMouseWheel: true
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: this.data3_3.map((i) => i.coordinate),
            axisLabel: {
              rotate: this.data3_3.length > 7 ? 30 : 0,
              textStyle: {
                color: '#3ea7e1',
                fontSize: 14
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#014e6a'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位: 个',
            nameTextStyle: {
              color: '#3ea7e1',
              fontSize: 14,
              align: 'center'
            },
            axisTick: {
              show: true
            },
            axisLabel: {
              color: '#3ea7e1'
            },
            min: 0,
            minInterval: 1,
            splitLine: {
              lineStyle: {
                color: '#214672'
              }
            }
          }
        ],
        series: [
          {
            name: '亿元村居数',
            type: 'bar',
            barWidth: this.data3_3.length <= 6 ? 20 : 10,
            itemStyle: {
              color: {
                colorStops: [
                  {
                    offset: 0.5,
                    color: '#50a8f7'
                  },
                  {
                    offset: 1,
                    color: '#0a3475'
                  }
                ],
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false
              }
            },
            data: this.data3_3.map((i) => i.count)
          }
        ]
      }

      this.financial_chart3_3.setOption(option)
    },
    startTask(type, options, switchName, fn) {
      console.log('startTask', this.currentOverTask, type)
      this[type] = setTimeout(() => {
        const next = this.getNextOption(this[options], this[switchName], type)
        if (next && this.currentOverTask != type) {
          this[fn](next.id)
        }
      }, this.playInterval)
    },
    // 统一启动所有轮播任务
    startAllTasks() {
      // 只有在没有鼠标悬停的情况下才启动轮播
      if (!this.currentOverTask) {
        this.startTask('task1_1', 'switchOptions1', 'switch1', 'handleChangeSwitch1')
        this.startTask('task1_2', 'switchOptions2', 'switch2', 'handleChangeSwitch2')
        this.startTask('task1_3', 'switchOptions3', 'switch3', 'handleChangeSwitch3')
        this.startTask('task3_1', 'switchOptions4', 'switch4', 'handleChangeSwitch4')
        this.startTask('task3_2', 'switchOptions6', 'switch6', 'handleChangeSwitch6')
      }
    },
    // 数字转万元
    numberFormate(value, thousands = true, precision = 2, formate = true) {
      if (isNaN(value) || parseFloat(value) == 0) {
        return parseFloat(0)?.toFixed(precision)
      }
      const temp = formate ? parseFloat(value) / 10000 : parseFloat(value)
      if (thousands) {
        return temp?.toFixed(precision).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      } else {
        return temp?.toFixed(precision)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.totalCount {
  height: 41px;
  display: flex;
  justify-content: center;
  margin-top: 30px;
}
.totalImg {
  width: 35px;
  height: 35px;
  margin-top: 8px;
}
.totalText {
  display: flex;
  align-items: center;
  color: #fff;
  margin-left: 10px;
  margin-right: 20px;
}
.totalNum {
  margin-left: -27px;
  margin-right: -17px;
  margin-top: -3px;
  scale: 0.8;
  //width: 204px;
}
::v-deep .num-box .num-span {
  width: 29px;
  height: 41px;
  background: url(@/assets/image/total-number-bg.png) 0 0 no-repeat;
}

::v-deep .el-progress {
  padding: 2px;
  border: 1px solid rgba(134, 151, 233, 0.6);
  .el-progress-bar__outer {
    height: 19px !important;
    background: none;
    border-radius: 0;
  }
  .el-progress-bar__inner {
    border-radius: 0;
    background-image: linear-gradient(to right, #1ce2dc, #2873eb);
  }
  .el-progress-bar__innerText {
    font-size: 14px;
    font-weight: bold;
  }
}
.rank-header {
  margin: 10px 0 0px 0;
  padding: 10px 0;
  display: flex;
  background: linear-gradient(to right, #15408e 0%, #0e3077 75%, #072957 100%);
  .header-item {
    text-align: center;
    color: #fff;
  }
}

.rank-list {
  flex: 1;
  overflow: hidden;
  // margin-left: 15px;
  .rank-item {
    height: 43px;
    margin: 10px 0;
    display: flex;
    align-items: center;
    &.level1 {
      background: url('@/assets/image/no1.png') no-repeat 100% 100%;
    }
    &.level2 {
      background: url('@/assets/image/no2-3.png') no-repeat;
    }
    &.level3 {
      background: url('@/assets/image/no4.png') no-repeat;
    }
    background-size: 100% 100% !important;

    .rank-order {
      display: block;
      width: 80px;
      text-align: center;
      font-size: 24px;
      color: #fff;
      font-weight: bold;
      margin-left: 3px;
    }
    .area-name {
      flex: 3;
      display: block;
      width: 73px;
      text-align: center;
      font-size: 18px;
      color: #bff7ff;
      line-height: 36px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // text-align: left;
      padding: 0 10px;
    }
    .value {
      flex: 1;
      text-align: center;
      // text-align: right;
      color: #bff7ff;
    }
  }
}
.area-time {
  font-size: 14px;
  color: #8fd2fd;
  display: flex;
  flex-direction: row;
  align-items: center;
  .area-img {
    height: 18px;
    width: 18px;
  }
  .area-title {
    margin: 0 10px;
    letter-spacing: 1px;
  }
  .area {
    font-weight: bold;
  }
}

.area-org {
  display: flex;
  flex-direction: row;
  align-items: center;
  // font-style: italic;
  line-height: 24px;
  .area-img {
    height: 18px;
    width: 18px;
  }
  .area-title {
    color: #fff;
    margin: 0 10px;
    font-size: 14px;
    letter-spacing: 1px;
  }
  .area {
    font-weight: bold;
    background: linear-gradient(to top, #d3a27a, #fff);
    -webkit-background-clip: text;
    color: transparent;
  }
}

::v-deep .el-select {
  .el-input__inner {
    color: rgb(66, 217, 236) !important;
    background: linear-gradient(to bottom, #005ba8, #002d59) !important;
    border-color: rgb(0, 156, 255) !important;
    font-size: 16px;
  }
  .el-select__caret {
    color: rgb(66, 217, 236) !important;
  }
  .popper__arrow {
    display: none;
  }

  .el-select-dropdown {
    background-color: #005ba8 !important;
    border: none;
    .el-select-dropdown__item {
      color: #fff !important;
      &.selected,
      &.hover {
        background-color: #3ea7e1;
      }
    }
  }
}

.block-box {
  background-color: rgba(3 20 61 / 53%);
  box-shadow: rgba(0 123 228 / 50%) 0px 0px 40px 9px inset;
  display: flex;
  flex-direction: column;
  .block-title {
    height: 40px;
    line-height: 45px;
    background: url('@/assets/image/title.png') 0 0 no-repeat;
    padding-left: 36px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
  }
  .block-body {
    flex: 1;
    padding: 0 10px 20px;
  }
}

::v-deep .custom-dialog {
  // background: url('@/assets/image/bj.jpg') 0 0 no-repeat;
  background: #132647;
  box-shadow: inset 0 0 60px #3d61a4;
  .el-dialog__header {
    position: relative;
    .title {
      color: #fff;
      font-size: 22px;
      padding-left: 50px;
      background: url('@/assets/image/trading/title.png') 0 0 no-repeat;
    }
    .close-btn {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .el-dialog__body {
    height: 500px;
    .el-form {
      .el-form-item__label {
        color: #3ea7e1;
        font-size: 16px;
      }
      .el-date-editor {
        background: linear-gradient(to bottom, #005ba8, #002d59) !important;
        border-color: #3ea7e1 !important;
        .el-icon-date {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range-input {
          color: #3ea7e1 !important;
          background: transparent;
          border-color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range-separator {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-range__close-icon {
          color: #3ea7e1 !important;
          font-size: 15px;
          font-weight: bold;
        }
        .el-select__caret {
          color: #3ea7e1 !important;
        }
        .popper__arrow {
          display: none;
        }
        .el-picker-panel {
          background-color: #3ea7e1 !important;
          color: #fff !important;
          .el-picker-panel__icon-btn,
          .el-date-table th,
          .el-date-table td {
            color: #fff !important;
          }
          .el-date-table td.in-range {
            color: #3ea7e1 !important;
          }
        }
      }

      .search-btn {
        background-color: #71deff;
        border-radius: 10px;
        color: #13264a;
        font-size: 16px;
        padding: 4px 15px;
      }
    }
    .el-table {
      background-color: transparent;
      border: none;
      &::before {
        display: none;
      }
      .el-table__body-wrapper {
        // &::-webkit-scrollbar {
        //   width: 0;
        //   height: 0;
        //   border: none;
        // }
        &::-webkit-scrollbar {
          width: 10px !important;
          height: 100% !important;
        }
        &::-webkit-scrollbar-track {
          background-color: rgba(62, 167, 225, 0.1) !important;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 5px !important;
          background-color: rgba(62, 167, 225, 0.2) !important;
        }
      }
      th.gutter {
        display: none !important;
        width: 0 !important;
      }

      colgroup col[name='gutter'] {
        display: none;
        width: 0;
      }

      .el-table__body {
        width: 100% !important;
      }

      th,
      td {
        border: none;
      }
      th {
        color: #3ea7e1;
        background-color: #02398b;
      }
      tr,
      td {
        color: #3ea7e1;
        background-color: transparent;
      }
      tr.el-table__row--striped td {
        background-color: rgba(26, 58, 96, 0.5);
      }
    }
  }
}

* {
  box-sizing: border-box;
}
.container {
  height: calc(100% - 110px);
  display: flex;
  color: #3ea7e1;
  padding: 0 20px 20px;

  .left-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    .box1_1 {
      height: 303px;
      position: relative;
      .switch-box {
        position: absolute;
        right: 8px;
        top: 18px;
      }
      .switch-box1 {
        padding: 10px 20px;
      }
      #financial_chart3_1 {
        height: 216px;
        padding: 0 20px 0 10px;
      }
    }

    .box1_2 {
      flex: 1;
      position: relative;
      display: flex;
      .switch-box {
        position: absolute;
        right: 8px;
        top: 18px;
      }
      #financial_chart1_2 {
        flex: 1;
        padding: 0 20px;
      }
    }

    .box1_3 {
      flex: 1;
      position: relative;
      display: flex;
      margin: 20px 0;

      .switch-box {
        position: absolute;
        right: 8px;
        top: 18px;
      }
      #financial_chart1_3 {
        flex: 1;
        padding: 10px 20px;
      }
    }
  }
  .center-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    margin: 0 20px;

    .box2_1 {
      flex: 6;
      height: 0;
      display: flex;
      flex-direction: column;
      .tooltip {
        .info {
          display: flex;
          justify-content: space-between;
        }
      }
      .map {
        flex: 1;
        height: 100%;
        width: 100%;
      }
    }

    .box2_2 {
      flex: 2;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;

      .box {
        background-color: rgba(3, 20, 61, 0.53);
        box-shadow: rgba(0, 123, 228, 0.5) 0px 0px 40px 9px inset;
        width: calc(50% - 10px);
        height: calc(33% - 10px);
        display: flex;
        align-items: center;
        .img {
          height: calc(100% - 20px);
          margin: 0 0 0 20px;
        }
        .detail-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          margin-left: 10px;
          justify-content: center;
          .value {
            text-align: left;
            font-size: 20px;
            font-weight: bold;
            color: #fff;
          }
          .title {
            text-align: left;
            color: #1ce2dc;
          }
        }
        &:nth-child(3),
        &:nth-child(4) {
          margin: 15px 0;
        }
      }
    }
  }

  .right-container {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    .box3_1 {
      flex: 328;
      position: relative;
      display: flex;
      .switch-box {
        position: absolute;
        right: 8px;
        top: 18px;
      }
      .switch-box1 {
        padding: 10px 20px;
      }

      .block-body {
        display: flex;
        flex-direction: column;
        padding-bottom: 0;
        .rank-list {
          flex: 1;
          .area-name {
            flex: 1;
          }
          .value {
            flex: 2;
          }
          .rank-progress {
            flex: 2;
          }
        }
      }
    }

    .box3_2 {
      flex: 266;
      position: relative;
      .switch-box {
        position: absolute;
        right: 8px;
        top: 18px;
      }
      .switch-box1 {
        margin: 10px 20px;
        .more-btn {
          font-size: 14px;
          margin-right: 10px;
          float: right;
          cursor: pointer;
          color: #fff;
        }
      }

      .box1 {
        flex: 1;
        margin: 25px 15px;
        background: url('@/assets/image/financial/ring.png') 0 0 no-repeat;
        background-size: 84% 97%;
        display: flex;
        background-position-x: 42%;
        position: relative;
        .left {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .item {
            background: url('@/assets/image/financial/left.png') 0 0 no-repeat;
            background-size: 100% 100%;
          }
        }
        .center {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding-bottom: 10px;
          .value {
            text-align: left;
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(to top, #28b7ff, #fff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: rgba(0, 0, 0, 0);
          }
          .title {
            text-align: left;
            color: #fff;
            .subunit {
              font-size: 14px;
              font-weight: bold;
              text-align: center;
            }
          }
        }
        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .item {
            background: url('@/assets/image/financial/right.png') 0 0 no-repeat;
            background-size: 100% 100%;
          }
        }
        .unit {
          position: absolute;
          left: 0;
          bottom: -20px;
          right: 0;
          text-align: center;
        }
        .item {
          display: flex;
          align-items: center;
          padding: 8px 5px;
          .img {
            margin: 0 10px;
          }
          .detail-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-left: 6px;
            justify-content: center;
            .value {
              text-align: left;
              font-size: 24px;
              font-weight: bold;
              background: linear-gradient(to top, #28b7ff, #fff);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: rgba(0, 0, 0, 0);
            }
            .title {
              text-align: left;
              color: #fff;
              font-size: 14px;
              .subunit {
                font-size: 12px;
              }
            }
            .fwb {
              font-weight: bolder;
            }
          }
        }
      }

      #financial_chart3_2 {
        flex: 1;
        padding: 20px;
      }
    }

    .box3_3 {
      flex: 210;
      display: flex;
      flex-direction: column;
      margin: 20px 0;

      #financial_chart3_3 {
        flex: 1;
      }
    }
  }
}
</style>
