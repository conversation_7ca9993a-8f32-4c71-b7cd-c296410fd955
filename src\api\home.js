import request from '@/utils/request'

// 项目收支列表接口
export function getHomepageData() {
  return request({
    url: '/bid/anon/bidHome/listBids/getHomepageData',
    method: 'get',
    withCredentials: true
  })
}

export function findPageList(data) {
  return request({
    url: '/bid/anon/bidHome/findPageList',
    method: 'post',
    payload: true,
    data: data,
    withCredentials: true
  })
}
// 获取主页配置数据
export function homeData() {
  return request({
    url: '/bid/anon/bidHome/listBids/getHomepageData',
    method: 'get',
    withCredentials: true
  })
}

export function findTradingCore() {
  return request({
    url: '/bid/anon/bidHome/findTradingCore',
    method: 'post',
    withCredentials: true
  })
}
