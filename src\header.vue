<template>
  <div
    class="header-container"
    :style="{
      backgroundImage:
        $route.path == '/financial'
          ? `url(${require('@/assets/image//header-financial.png')})`
          : $route.path == '/shares'
            ? `url(${require('@/assets/image/shares/header.png')})`
            : $route.path == '/trading'
              ? `url(${require('@/assets/image/trading/header.png')})`
              : $route.path == '/'
                ? `url(${require('@/assets/image/asset-header.png')})`
                : `url(${require('@/assets/image/header.png')})`
    }"
  >
    <div class="left">
      <div class="menu" :class="{ active: $route.path == '/' }" @click="$router.push({ path: '/' })">
        <span class="name">资产大屏</span>
      </div>
      <div class="menu" :class="{ active: $route.path == '/trading' }" @click="$router.push({ path: '/trading' })">
        <span class="name">交易大屏</span>
      </div>
      <div class="menu" :class="{ active: $route.path == '/financial' }" @click="$router.push({ path: '/financial' })">
        <span class="name">财务大屏</span>
      </div>
    </div>
    <div class="right">
      <div class="menu" :class="{ active: $route.path == '/shares' }">
        <span class="name" @click="$router.push({ path: '/shares' })">成员大屏</span>
      </div>
      <div class="menu" :class="{ active: $route.path == '/inventory' }">
        <span class="name" @click="$router.push({ path: '/inventory' })">资产清查</span>
      </div>
      <div class="menu" @click="goMapCloud">
        <span class="name">数字云图</span>
      </div>
    </div>
    <div class="help radio-btn" @click="downloadHelp">
      帮助
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  methods: {
    goMapCloud() {
      const query = JSON.parse(localStorage.getItem('query'))
      if (!query) {
        // this.$message.warning('参数错误')
        return
      } else {
        const str = `?platLogin=true&username=${query.username}&loginOutUrl=${encodeURIComponent(query.loginOutUrl)}&contractUrl=${encodeURIComponent(query.contractUrl)}&token=${
          query.token
        }`

        const url = `${window.homeData.mapCloudUrl}${str}`
        window.open(url, '_blank')
      }
    },
    downloadHelp() {
      this.$message.success('文件下载中，请到下载查看附件')
      window.open('RetrievalTemplate.xlsx', '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.header-container {
  width: 100%;
  height: 100px;
  position: relative;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  // background: url('./assets/image/trading/header.png') 0 0 no-repeat;
  background-size: 100% 100%;
  background-position: 0 0;
  background-repeat: no-repeat;
  .left,
  .right {
    display: flex;
    align-items: center;
    margin-top: -25px;
  }
  .left {
    padding-left: 20px;
  }
  .right {
    padding-right: 20px;
  }
  .help {
    position: absolute;
    top: 75px;
    right: 45px;
  }
  .menu {
    cursor: pointer;
    height: 100%;
    width: 150px;
    min-width: 130px;
    min-height: 30px;
    max-height: 40px;
    max-width: 150px;
    background: url('./assets/image/trading/btn.png') 0 0 no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -1.5%;
    .name {
      color: #61d3f7;
      font-size: 18px;
      font-weight: bold;
    }
    &.active {
      background-image: url(./assets/image/trading/btn-active.png);
      .name {
        color: #00fff0;
      }
    }
  }
}
</style>
