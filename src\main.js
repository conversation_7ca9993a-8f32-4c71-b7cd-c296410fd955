import Vue from 'vue'
import App from './App.vue'
import router from './router'
import 'element-ui/lib/theme-chalk/index.css'
import Element from 'element-ui'
Vue.use(Element, {
  size: 'mini' // set element-ui default size
})

import 'echarts-gl'

import '@/assets/css/index.scss'
Vue.config.productionTip = false

import mixins from './mixins/tooltipMixins'
import install from './utils/install'
Vue.mixin(mixins)
Vue.use(install)

import './permission'

new Vue({
  router,
  render: (h) => h(App)
}).$mount('#app')
