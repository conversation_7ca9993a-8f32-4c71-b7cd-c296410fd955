<template>
  <div style="display: inline-block">
    <!-- <span
      v-if="prefix"
      class="integer-item num-span"
      :style="{ fontSize: fontSize, fontWeight: fontWeight, color: fontColor }"
    >
      {{ prefix }}
    </span>
    <span
      v-for="item in integerPart"
      class="integer-item num-span"
      :style="{ fontSize: fontSize, fontWeight: fontWeight, color: fontColor }"
    >
      {{ item }}
    </span>
    <span
      v-if="precision"
      class="point-span"
      :style="{ fontSize: fontSize, fontWeight: fontWeight, color: fontColor }"
    >
      .
    </span>
    <span
      v-for="item in decimalPart"
      class="decimal-item num-sp an"
      :style="{ fontSize: fontSize, fontWeight: fontWeight, color: fontColor }"
    >
      {{ item }}
    </span>
    <span
      v-if="unit"
      class="integer-item num-span"
      :style="{ fontSize: fontSize, fontWeight: fontWeight, color: fontColor }"
    >
      {{ unit }}
    </span> -->
    <div class="num-box">
      <span
        v-for="(item, index) of 10 - String(integerPart).length"
        v-show="String(integerPart).length > 7 && (index == 2 || index == 6)"
        :key="`number${index}`"
        class="num-span"
        :style="{ fontSize: fontSize, fontWeight: fontWeight, color: fontColor }"
      >
        {{ String(integerPart).length > 7 && (index == 2 || index == 6) ? ',' : '' }}
      </span>
      <span v-for="(item, index) of String(integerPart).length" :key="`num${index}`" class="num-span" :style="{ fontSize: fontSize, fontWeight: fontWeight, color: fontColor }">
        {{ String(integerPart)[index] }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NumberFlash',
  props: {
    number: {
      type: Number,
      default: 0
      // validator(value) {
      //   return /^\d+$/.test(value)
      // }
    },
    precision: {
      // 小数位精度
      type: Number,
      default: 2
    },
    fontSize: {
      // 字体大小
      type: Number,
      default: 40
    },
    fontWeight: {
      // 字体粗细
      type: Number,
      default: 900
    },
    fontColor: {
      // 字体颜色
      type: String,
      default: '#8AEBFC'
    },
    prefix: {
      // 前缀符号
      type: String,
      default: ''
    },
    unit: {
      // 单位
      type: String,
      default: ''
    }
  },
  data() {
    return {
      numberStr: null,
      numberLength: 0,
      integerPart: [],
      decimalPart: []
    }
  },
  watch: {
    number: {
      handler() {
        this.init()
      },
      immediate: true
    }
  },
  created() {
    // this.init()
  },
  methods: {
    init() {
      if (String(parseInt(this.number)).length > 12) {
        this.numberStr = (this.number / 100000000).toFixed(this.precision)
      } else if (String(parseInt(this.number)).length > 8) {
        this.numberStr = (this.number / 10000).toFixed(this.precision)
      } else {
        this.numberStr = this.number.toFixed(this.precision)
      }
      //   this.numberStr = this.number.toFixed(this.precision)
      this.numberLength = this.precision === 0 ? this.numberStr.length : this.numberStr.length - 1
      this.createFlash()
    },
    createFlash() {
      // 获取flash数据
      let integerPartTemp = []
      let decimalPartTemp = []
      if (this.precision) {
        integerPartTemp = this.numberStr.split('.')[0]
        decimalPartTemp = this.numberStr.split('.')[1]
      } else {
        integerPartTemp = this.numberStr
        decimalPartTemp = []
      }

      const flashFreq = 80 // 闪动频率
      const flashComplete = 3000 // 闪动持续的时间

      let integerInterval = null
      let decimalInterval = null

      const integerMaxNum = Math.pow(10, integerPartTemp.length) - 1
      const integerMinNum = integerPartTemp.length > 1 ? Math.pow(10, integerPartTemp.length - 1) : 0

      const decimalMaxNum = Math.pow(10, decimalPartTemp.length) - 1
      const decimalMinNum = decimalPartTemp.length > 1 ? Math.pow(10, decimalPartTemp.length - 1) : 0

      // 闪烁
      integerInterval = setInterval(() => {
        this.integerPart = Math.floor(Math.random() * (integerMaxNum - integerMinNum + 1) + integerMinNum).toString()
        this.integerPart = this.numFormat(this.integerPart)
      }, flashFreq)

      decimalInterval = setInterval(() => {
        this.decimalPart = Math.floor(Math.random() * (decimalMaxNum - decimalMinNum + 1) + decimalMinNum).toString()
        this.decimalPart = this.numFormat(this.decimalPart)
      }, flashFreq)

      // 停止闪烁
      setTimeout(() => {
        clearInterval(integerInterval)
        clearInterval(decimalInterval)
        this.integerPart = this.numFormat(integerPartTemp)
        this.decimalPart = decimalPartTemp
      }, flashComplete)
    },
    numFormat(num) {
      const res = num.toString().replace(/\d+/, function (n) {
        // 先提取整数部分
        return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
          return $1 + ','
        })
      })
      return res
    }
  }
}
</script>

<style lang="scss" scoped>
// .num-span {
//     display: inline-block;
//     text-align: center;
//     padding: 0 2px;
//     border: 2px solid #8AEBFC;
//     border-image: linear-gradient(to top right, #0461c1, #8AEBFC) 3;
//     background-color: #05245b;
//     margin-right: 5px;
//     font-size: 18px;
//     box-shadow: 0 0 10px rgb(138, 235, 252, 0.5);
// }
.num-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .num-span {
    width: 20px;
    height: 25px;
    display: inline-block;
    text-align: center;
    // padding: 0 2px;
    margin-right: 10px;
    font-size: 18px;
    border: 2px solid #8aebfc;
    border-image: linear-gradient(to top right, #0461c1, #8aebfc) 3;
    background-color: #05245b;
    box-shadow: 0 0 10px rgb(138, 235, 252, 0.5);
  }
  .num-span:nth-child(3),
  .num-span:nth-child(7) {
    border: none;
    border-image: none;
    background: none;
    box-shadow: none;
    display: inline;
  }
}
</style>
