<!--
 * @Descripttion:
 * @version:
 * @Author: 未知
 * @Date: 2023-08-18 13:44:33
 * @LastEditors: Andy
 * @LastEditTime: 2023-09-01 08:52:12
-->
<template>
  <div id="pieEcharts" class="pie-box"></div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  components: {},
  props: {
    echartData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  watch: {
    echartData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.$nextTick(() => {
          this.initEcharts()
        })
      }
    }
  },
  created() {},
  methods: {
    initEcharts() {
      const data = [
        {
          value: this.echartData.seriesData[0],
          name: '流拍'
        },
        {
          value: this.echartData.seriesData[1],
          name: '有异议'
        },
        {
          value: this.echartData.seriesData[2],
          name: '弃拍'
        }
      ]
      const total = data.reduce((total, { value = 0 }) => total + value, 0)
      const pieEcharts = document.getElementById('pieEcharts')
      this.myChart = echarts.init(pieEcharts)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            return `${params.marker}${params.name}&nbsp;&nbsp;&nbsp;&nbsp;${params.value + '次'}`
          }
        },
        color: ['#368aff', '#00e537', '#fde84e'],
        legend: {
          top: '25%',
          left: '45%',
          orient: 'vertical',
          itemGap: 20,
          itemWidth: 15,
          textStyle: {
            color: '#fff',
            rich: {
              name: {
                fontSize: 12,
                align: 'left',
                width: 60
              },
              value: {
                fontSize: 12,
                align: 'right',
                width: 100
              },
              rate: {
                fontSize: 12,
                align: 'right',
                width: 60
              }
            }
          },
          formatter: (name) => {
            const current = data.find((i) => i.name === name)
            const arr = [
              '{name|' + name + '}',
              '{value|' + (isNaN(parseInt(current.value).toLocaleString()) ? 0 : parseInt(current.value).toLocaleString()) + '次' + '}',
              '{rate|' + (isNaN(((current.value / total) * 100).toFixed(2)) ? 0 : ((current.value / total) * 100).toFixed(2)) + '%' + '}'
            ]
            return arr.join(' ')
          }
        },
        series: [
          {
            center: ['25%', '50%'],
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: true,
            label: {
              show: true,
              position: 'center',
              rich: {
                value: {
                  fontSize: 26,
                  color: '#fff',
                  lineHeight: 40,
                  fontWeight: 900
                },
                //  失败次数
                total: {
                  fontSize: 12,
                  color: '#fff',
                  lineHeight: 20
                }
              },
              formatter: '{value|' + total + '}' + '\n\r' + '{total|失败次数}'
            },
            labelLine: {
              show: false
            },
            data: data,

            showEmptyCircle: true
          }
        ]
      }
      this.myChart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.pie-box {
  margin: 0 auto;
  width: 100%;
  height: 100%;
}
</style>
